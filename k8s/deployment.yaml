# AI Agent Framework Kubernetes 部署配置

apiVersion: v1
kind: Namespace
metadata:
  name: ai-agent-framework
  labels:
    name: ai-agent-framework

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-agent-config
  namespace: ai-agent-framework
data:
  production.yaml: |
    app:
      name: "AI Agent Framework"
      version: "1.0.0"
      environment: "production"
      debug: false
    server:
      host: "0.0.0.0"
      port: 8000
      workers: 4
    logging:
      level: "INFO"
      format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

---
apiVersion: v1
kind: Secret
metadata:
  name: ai-agent-secrets
  namespace: ai-agent-framework
type: Opaque
stringData:
  OPENAI_API_KEY: "your-openai-api-key"
  ANTHROPIC_API_KEY: "your-anthropic-api-key"
  GOOGLE_API_KEY: "your-google-api-key"
  SECRET_KEY: "your-secret-key"
  JWT_SECRET: "your-jwt-secret"
  DATABASE_URL: "****************************************/ai_agent"
  REDIS_URL: "redis://redis:6379/0"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-agent-app
  namespace: ai-agent-framework
  labels:
    app: ai-agent-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-agent-app
  template:
    metadata:
      labels:
        app: ai-agent-app
    spec:
      containers:
      - name: ai-agent
        image: ai-agent-framework:latest
        ports:
        - containerPort: 8000
        env:
        - name: PYTHONPATH
          value: "/app"
        - name: CONFIG_FILE
          value: "/app/config/production.yaml"
        envFrom:
        - secretRef:
            name: ai-agent-secrets
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        - name: data-volume
          mountPath: /app/data
        - name: logs-volume
          mountPath: /app/logs
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: config-volume
        configMap:
          name: ai-agent-config
      - name: data-volume
        persistentVolumeClaim:
          claimName: ai-agent-data-pvc
      - name: logs-volume
        persistentVolumeClaim:
          claimName: ai-agent-logs-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: ai-agent-service
  namespace: ai-agent-framework
  labels:
    app: ai-agent-app
spec:
  selector:
    app: ai-agent-app
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-agent-ingress
  namespace: ai-agent-framework
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - api.yourdomain.com
    secretName: ai-agent-tls
  rules:
  - host: api.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ai-agent-service
            port:
              number: 80

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ai-agent-data-pvc
  namespace: ai-agent-framework
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ai-agent-logs-pvc
  namespace: ai-agent-framework
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: fast-ssd

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: ai-agent-framework
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"

---
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: ai-agent-framework
spec:
  selector:
    app: redis
  ports:
  - protocol: TCP
    port: 6379
    targetPort: 6379

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: ai-agent-framework
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_DB
          value: "ai_agent"
        - name: POSTGRES_USER
          value: "ai_agent"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-agent-secrets
              key: POSTGRES_PASSWORD
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: postgres-data
        persistentVolumeClaim:
          claimName: postgres-data-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: ai-agent-framework
spec:
  selector:
    app: postgres
  ports:
  - protocol: TCP
    port: 5432
    targetPort: 5432

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-data-pvc
  namespace: ai-agent-framework
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: fast-ssd
