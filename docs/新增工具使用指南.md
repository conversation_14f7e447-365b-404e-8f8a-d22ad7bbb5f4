# AI Agent框架 - 新增工具使用指南

## 概述

本文档介绍AI Agent框架中新增的高级工具，包括消息队列、缓存、文件存储和数据处理工具。这些工具为AI Agent提供了强大的数据处理和系统集成能力。

## 1. 消息队列工具 (MessageQueueTool)

### 功能特性
- 支持多种消息队列系统（Redis、RabbitMQ、Kafka、内存队列）
- 消息优先级管理
- 延迟发送和TTL支持
- 队列管理和监控

### 基础使用

```python
from ai_agent_framework.tools import MessageQueueTool, MessageQueueType, MessagePriority

# 创建消息队列工具（使用内存队列进行测试）
mq_tool = MessageQueueTool(
    queue_type=MessageQueueType.MEMORY,
    default_timeout=30
)

# 注册工具
from ai_agent_framework.utils.tool_registry import tool_registry
tool_registry.register_tool(mq_tool, max_concurrent=5)
```

### 操作示例

```python
# 连接到消息队列
connect_args = {"action": "connect"}
result = await mq_tool.execute(connect_args)

# 创建队列
create_queue_args = {
    "action": "create_queue",
    "queue_name": "task_queue",
    "durable": True
}
result = await mq_tool.execute(create_queue_args)

# 发送消息
send_args = {
    "action": "send",
    "queue_name": "task_queue",
    "message": {
        "content": {"task": "process_data", "data": [1, 2, 3]},
        "priority": "high",
        "delay_seconds": 0,
        "metadata": {"source": "ai_agent"}
    }
}
result = await mq_tool.execute(send_args)

# 接收消息
receive_args = {
    "action": "receive",
    "queue_name": "task_queue",
    "timeout_seconds": 10
}
result = await mq_tool.execute(receive_args)
```

## 2. 缓存工具 (CacheTool)

### 功能特性
- 支持多种缓存系统（内存、Redis、Memcached）
- 键值存储和批量操作
- TTL和标签管理
- 缓存统计和监控

### 基础使用

```python
from ai_agent_framework.tools import CacheTool, CacheType

# 创建缓存工具
cache_tool = CacheTool(
    cache_type=CacheType.MEMORY,
    connection_config={"max_size": 1000},
    default_ttl=3600  # 默认1小时过期
)

# 注册工具
tool_registry.register_tool(cache_tool, max_concurrent=10)
```

### 操作示例

```python
# 连接到缓存系统
connect_args = {"action": "connect"}
result = await cache_tool.execute(connect_args)

# 设置缓存
set_args = {
    "action": "set",
    "key": "user:123",
    "value": {"name": "张三", "age": 30, "city": "北京"},
    "ttl_seconds": 1800,  # 30分钟过期
    "tags": ["user", "profile"]
}
result = await cache_tool.execute(set_args)

# 获取缓存
get_args = {
    "action": "get",
    "key": "user:123"
}
result = await cache_tool.execute(get_args)

# 批量操作
set_multi_args = {
    "action": "set_multi",
    "key_value_pairs": {
        "config:theme": "dark",
        "config:language": "zh-CN",
        "config:timezone": "Asia/Shanghai"
    },
    "ttl_seconds": 7200
}
result = await cache_tool.execute(set_multi_args)

# 获取统计信息
stats_args = {"action": "stats"}
result = await cache_tool.execute(stats_args)
```

## 3. 文件存储工具 (FileStorageTool)

### 功能特性
- 支持多种存储系统（本地文件系统、AWS S3、阿里云OSS等）
- 文件上传、下载和管理
- 目录操作和文件信息查询
- 文件复制、移动和URL生成

### 基础使用

```python
from ai_agent_framework.tools import FileStorageTool, StorageType

# 创建文件存储工具
storage_tool = FileStorageTool(
    storage_type=StorageType.LOCAL,
    connection_config={"base_path": "./agent_storage"}
)

# 注册工具
tool_registry.register_tool(storage_tool, max_concurrent=3)
```

### 操作示例

```python
# 连接到存储系统
connect_args = {"action": "connect"}
result = await storage_tool.execute(connect_args)

# 上传文件
upload_args = {
    "action": "upload",
    "local_path": "/tmp/document.pdf",
    "remote_path": "documents/user_manual.pdf",
    "metadata": {
        "category": "documentation",
        "version": "1.0",
        "author": "AI Agent"
    }
}
result = await storage_tool.execute(upload_args)

# 列出文件
list_args = {
    "action": "list",
    "prefix": "documents/",
    "limit": 50
}
result = await storage_tool.execute(list_args)

# 下载文件
download_args = {
    "action": "download",
    "remote_path": "documents/user_manual.pdf",
    "local_path": "/tmp/downloaded_manual.pdf"
}
result = await storage_tool.execute(download_args)

# 获取文件信息
info_args = {
    "action": "info",
    "remote_path": "documents/user_manual.pdf"
}
result = await storage_tool.execute(info_args)
```

## 4. 数据处理工具 (DataProcessingTool)

### 功能特性
- JSON/XML解析和格式化
- CSV数据处理
- 文本清洗和提取
- 数据验证和转换
- 编码转换和哈希计算

### 基础使用

```python
from ai_agent_framework.tools import DataProcessingTool

# 创建数据处理工具
data_tool = DataProcessingTool()

# 注册工具
tool_registry.register_tool(data_tool, max_concurrent=5)
```

### 操作示例

```python
# JSON处理
json_data = '{"name": "张三", "age": 30, "skills": ["Python", "AI"]}'
parse_args = {
    "action": "parse_json",
    "data": json_data
}
result = await data_tool.execute(parse_args)

# 格式化JSON
format_args = {
    "action": "format_json",
    "data": {"name": "李四", "age": 25},
    "indent": 4
}
result = await data_tool.execute(format_args)

# CSV处理
csv_data = "姓名,年龄,城市\n张三,30,北京\n李四,25,上海"
csv_args = {
    "action": "parse_csv",
    "data": csv_data,
    "delimiter": ","
}
result = await data_tool.execute(csv_args)

# 文本提取
text = "联系我们：邮箱 <EMAIL>，电话 138-0013-8000"
extract_args = {
    "action": "extract_emails",
    "data": text
}
result = await data_tool.execute(extract_args)

# 数据验证
validate_args = {
    "action": "validate_email",
    "data": "<EMAIL>"
}
result = await data_tool.execute(validate_args)
```

## 5. 集成使用示例

### 完整的数据处理流程

```python
import asyncio
from ai_agent_framework import Agent
from ai_agent_framework.models import OpenAIAdapter
from ai_agent_framework.memory import MemoryManager
from ai_agent_framework.tools import (
    MessageQueueTool, CacheTool, FileStorageTool, DataProcessingTool,
    MessageQueueType, CacheType, StorageType
)
from ai_agent_framework.utils.tool_registry import tool_registry

async def setup_advanced_agent():
    """设置具有高级工具的AI Agent"""
    
    # 创建和注册工具
    mq_tool = MessageQueueTool(queue_type=MessageQueueType.MEMORY)
    cache_tool = CacheTool(cache_type=CacheType.MEMORY)
    storage_tool = FileStorageTool(storage_type=StorageType.LOCAL)
    data_tool = DataProcessingTool()
    
    tool_registry.register_tool(mq_tool, max_concurrent=5)
    tool_registry.register_tool(cache_tool, max_concurrent=10)
    tool_registry.register_tool(storage_tool, max_concurrent=3)
    tool_registry.register_tool(data_tool, max_concurrent=5)
    
    # 创建模型适配器
    model = OpenAIAdapter(
        api_key="your-api-key",
        model="gpt-4",
        temperature=0.7
    )
    
    # 创建记忆管理器
    memory = MemoryManager()
    
    # 创建Agent
    agent = Agent(
        model=model,
        memory=memory,
        tool_registry=tool_registry
    )
    
    return agent

async def data_processing_workflow():
    """数据处理工作流示例"""
    agent = await setup_advanced_agent()
    
    # 模拟数据处理任务
    user_message = """
    请帮我处理以下任务：
    1. 解析这个JSON数据：{"users": [{"name": "张三", "email": "<EMAIL>"}]}
    2. 将解析结果缓存起来，键名为 "parsed_users"
    3. 创建一个消息队列 "data_queue"
    4. 发送处理完成的通知消息到队列
    """
    
    response = await agent.process_message(user_message)
    print(f"Agent响应: {response}")

if __name__ == "__main__":
    asyncio.run(data_processing_workflow())
```

## 6. 最佳实践

### 性能优化
1. **合理设置并发限制**：根据系统资源调整工具的最大并发数
2. **使用缓存减少重复计算**：将计算结果缓存以提高响应速度
3. **批量操作**：使用批量API减少网络开销

### 错误处理
1. **连接检查**：在执行操作前确保连接状态正常
2. **参数验证**：使用工具的validate_arguments方法验证参数
3. **异常捕获**：妥善处理工具执行过程中的异常

### 安全考虑
1. **权限控制**：为敏感工具设置适当的权限要求
2. **数据加密**：对敏感数据进行加密存储
3. **访问日志**：记录工具使用日志以便审计

## 7. 扩展开发

### 添加新的适配器
如需支持新的存储或队列系统，可以继承相应的适配器基类：

```python
from ai_agent_framework.tools.message_queue_tool import MessageQueueAdapter

class CustomQueueAdapter(MessageQueueAdapter):
    """自定义消息队列适配器"""
    
    async def connect(self) -> bool:
        # 实现连接逻辑
        pass
    
    async def send_message(self, queue_name: str, message: QueueMessage) -> bool:
        # 实现发送消息逻辑
        pass
    
    # 实现其他必需方法...
```

### 创建自定义工具
参考现有工具的实现模式，创建符合项目需求的自定义工具：

```python
from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult

class CustomTool(ToolInterface):
    """自定义工具示例"""
    
    @property
    def name(self) -> str:
        return "custom_tool"
    
    @property
    def description(self) -> str:
        return "自定义工具描述"
    
    @property
    def parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "action": {"type": "string", "enum": ["custom_action"]},
                # 定义其他参数...
            },
            "required": ["action"]
        }
    
    async def execute(self, arguments: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> ToolResult:
        # 实现工具逻辑
        pass
    
    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        # 实现参数验证
        pass
```

通过这些新增的工具，AI Agent可以处理更复杂的数据处理和系统集成任务，为构建企业级AI应用提供了强大的基础设施支持。
