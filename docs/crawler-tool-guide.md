# 爬虫工具使用指南

## 概述

爬虫工具（CrawlerTool）是AI Agent Framework中的一个强大工具，支持调用本地部署的爬虫服务来抓取和解析网页内容。该工具支持多种爬虫服务类型，包括Crawl4AI、Scrapy Splash、Selenium Grid等。

## 功能特性

### 核心功能
- **网页抓取**：支持抓取静态和动态网页内容
- **JavaScript渲染**：支持执行JavaScript的单页应用抓取
- **内容提取**：智能提取文本、链接、图片等结构化数据
- **多格式输出**：支持JSON、HTML、文本、Markdown等格式
- **截图功能**：支持网页截图和PDF导出
- **自定义选择器**：支持CSS选择器和XPath选择器

### 支持的爬虫服务
- **Crawl4AI**：专为AI应用设计的现代爬虫服务
- **Scrapy Splash**：基于Scrapy的JavaScript渲染服务
- **Selenium Grid**：分布式浏览器自动化服务
- **自定义服务**：支持自定义API接口的爬虫服务

## 安装和配置

### 依赖安装

```bash
# 安装必要的Python依赖
pip install aiohttp

# 如果使用Crawl4AI服务
pip install crawl4ai

# 如果使用Scrapy Splash服务
pip install scrapy-splash

# 如果使用Selenium Grid服务
pip install selenium
```

### 服务部署

#### Crawl4AI服务部署

```bash
# 使用Docker部署Crawl4AI服务
docker run -d \
  --name crawl4ai-service \
  -p 8000:8000 \
  crawl4ai/crawl4ai:latest

# 或者使用Python直接运行
python -m crawl4ai.server --host 0.0.0.0 --port 8000
```

#### Scrapy Splash服务部署

```bash
# 使用Docker部署Scrapy Splash服务
docker run -d \
  --name scrapy-splash \
  -p 8050:8050 \
  scrapinghub/splash:latest
```

#### Selenium Grid服务部署

```bash
# 部署Selenium Hub
docker run -d \
  --name selenium-hub \
  -p 4444:4444 \
  selenium/hub:latest

# 部署Chrome节点
docker run -d \
  --name selenium-chrome \
  --link selenium-hub:hub \
  selenium/node-chrome:latest
```

## 基础使用

### 初始化爬虫工具

```python
from ai_agent_framework.tools.crawler_tool import CrawlerTool, CrawlerServiceType

# 使用Crawl4AI服务
crawler = CrawlerTool(
    service_url="http://localhost:8000",
    service_type=CrawlerServiceType.CRAWL4AI,
    timeout=60,
    enable_javascript=True,
    enable_images=False
)

# 使用Scrapy Splash服务
crawler_splash = CrawlerTool(
    service_url="http://localhost:8050",
    service_type=CrawlerServiceType.SCRAPY_SPLASH,
    timeout=30
)

# 使用Selenium Grid服务
crawler_selenium = CrawlerTool(
    service_url="http://localhost:4444",
    service_type=CrawlerServiceType.SELENIUM_GRID,
    timeout=45
)
```

### 注册到Agent框架

```python
from ai_agent_framework.utils.tool_registry import tool_registry

# 注册爬虫工具
tool_registry.register_tool(crawler, max_concurrent=2)
```

## 使用示例

### 基础网页抓取

```python
# 基础爬取示例
arguments = {
    "url": "https://example.com",
    "action": "crawl",
    "format": "json"
}

result = await crawler.execute(arguments)
print(result.result)
```

### 文本内容提取

```python
# 提取网页文本内容
arguments = {
    "url": "https://news.example.com/article/123",
    "action": "extract_text",
    "selectors": {
        "title": "h1.article-title",
        "content": ".article-content",
        "author": ".author-name"
    },
    "options": {
        "wait_time": 5,
        "enable_javascript": True
    }
}

result = await crawler.execute(arguments)
```

### 链接和图片提取

```python
# 提取页面中的所有链接
arguments = {
    "url": "https://example.com",
    "action": "extract_links",
    "selectors": {
        "links": "a[href]"
    }
}

result = await crawler.execute(arguments)

# 提取页面中的所有图片
arguments = {
    "url": "https://example.com",
    "action": "extract_images",
    "selectors": {
        "images": "img[src]"
    }
}

result = await crawler.execute(arguments)
```

### 结构化数据提取

```python
# 使用自定义模式提取结构化数据
arguments = {
    "url": "https://shop.example.com/product/123",
    "action": "extract_data",
    "options": {
        "extract_schema": {
            "product_name": "h1.product-title",
            "price": ".price-current",
            "description": ".product-description",
            "images": {
                "selector": ".product-images img",
                "attribute": "src",
                "multiple": True
            },
            "reviews": {
                "selector": ".review-item",
                "fields": {
                    "rating": ".rating-stars",
                    "comment": ".review-text",
                    "author": ".review-author"
                },
                "multiple": True
            }
        }
    }
}

result = await crawler.execute(arguments)
```

### 网页截图

```python
# 网页截图
arguments = {
    "url": "https://example.com",
    "action": "screenshot",
    "options": {
        "viewport": {
            "width": 1920,
            "height": 1080
        },
        "wait_time": 3
    }
}

result = await crawler.execute(arguments)
```

### PDF导出

```python
# 导出网页为PDF
arguments = {
    "url": "https://example.com/document",
    "action": "pdf_export",
    "options": {
        "wait_time": 5,
        "viewport": {
            "width": 1200,
            "height": 800
        }
    }
}

result = await crawler.execute(arguments)
```

## 高级配置

### 自定义请求头和Cookie

```python
arguments = {
    "url": "https://api.example.com/data",
    "action": "crawl",
    "options": {
        "headers": {
            "Authorization": "Bearer your-token",
            "Accept": "application/json",
            "User-Agent": "Custom-Agent/1.0"
        },
        "cookies": [
            {
                "name": "session_id",
                "value": "abc123",
                "domain": "example.com",
                "path": "/"
            }
        ]
    }
}
```

### 代理配置

```python
# 初始化时配置代理
crawler = CrawlerTool(
    service_url="http://localhost:8000",
    service_type=CrawlerServiceType.CRAWL4AI,
    proxy_config={
        "http": "http://proxy.example.com:8080",
        "https": "https://proxy.example.com:8080"
    }
)
```

### 等待特定元素

```python
arguments = {
    "url": "https://spa.example.com",
    "action": "crawl",
    "options": {
        "wait_time": 10,
        "enable_javascript": True
    },
    "selectors": {
        "content": ".dynamic-content"  # 等待动态内容加载
    }
}
```

## 错误处理

### 常见错误类型

1. **服务连接失败**
   ```python
   # 检查服务是否正常运行
   # curl http://localhost:8000/health
   ```

2. **请求超时**
   ```python
   # 增加超时时间
   crawler = CrawlerTool(timeout=120)
   ```

3. **内存不足**
   ```python
   # 限制响应大小
   crawler = CrawlerTool(max_response_size=10*1024*1024)  # 10MB
   ```

### 错误处理示例

```python
try:
    result = await crawler.execute(arguments)
    if result.success:
        data = result.result
        print("爬取成功:", data)
    else:
        print("爬取失败:", result.result)
except Exception as e:
    print("执行异常:", str(e))
```

## 性能优化

### 并发控制

```python
# 注册时设置并发限制
tool_registry.register_tool(crawler, max_concurrent=3)
```

### 缓存策略

```python
# 可以在应用层实现缓存
import hashlib
import json

def get_cache_key(arguments):
    return hashlib.md5(json.dumps(arguments, sort_keys=True).encode()).hexdigest()

# 使用Redis或内存缓存存储结果
```

### 资源管理

```python
# 使用完毕后关闭会话
await crawler.close()
```

## 最佳实践

1. **合理设置超时时间**：根据目标网站的响应速度调整
2. **使用适当的等待时间**：给JavaScript充分的执行时间
3. **选择合适的服务类型**：根据需求选择最适合的爬虫服务
4. **实施错误重试机制**：处理网络不稳定的情况
5. **遵守robots.txt**：尊重网站的爬取规则
6. **控制请求频率**：避免对目标服务器造成过大压力

## 故障排除

### 检查服务状态

```bash
# 检查Crawl4AI服务
curl http://localhost:8000/health

# 检查Scrapy Splash服务
curl http://localhost:8050/_ping

# 检查Selenium Grid服务
curl http://localhost:4444/grid/api/hub/status
```

### 日志调试

```python
import logging

# 启用详细日志
logging.getLogger("crawler_tool").setLevel(logging.DEBUG)
```

### 常见问题解决

1. **JavaScript未执行**：确保enable_javascript=True且等待时间充足
2. **选择器无效**：使用浏览器开发者工具验证CSS选择器
3. **内容未加载**：增加wait_time或使用特定的等待条件
4. **编码问题**：检查网页编码设置和响应处理逻辑
