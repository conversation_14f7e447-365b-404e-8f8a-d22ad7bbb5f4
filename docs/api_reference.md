# AI Agent Framework API 参考文档

## 概述

本文档提供了AI Agent Framework所有公共API的详细说明，包括类、方法、参数和返回值的完整信息。

## 核心API

### Agent类

#### 构造函数

```python
Agent(
    agent_id: Optional[str] = None,
    config: Optional[FrameworkConfig] = None,
    model: Optional[ModelInterface] = None,
    memory: Optional[MemoryInterface] = None,
    reasoning_mode: str = ReasoningMode.REACT,
)
```

**参数：**
- `agent_id`: Agent唯一标识符，默认自动生成UUID
- `config`: 框架配置对象
- `model`: 模型接口实例
- `memory`: 记忆接口实例  
- `reasoning_mode`: 推理模式（"react", "cot", "direct"）

#### 主要方法

##### chat()

```python
async def chat(
    self,
    user_input: str,
    context: Optional[Dict[str, Any]] = None,
    tools: Optional[List[str]] = None,
) -> ModelResponse
```

与Agent进行对话交互。

**参数：**
- `user_input`: 用户输入文本
- `context`: 对话上下文信息
- `tools`: 可用工具列表

**返回：**
- `ModelResponse`: Agent响应对象

**异常：**
- `AgentError`: Agent执行失败
- `AgentStateError`: Agent状态错误

**示例：**
```python
response = await agent.chat("请帮我计算 2 + 3")
print(response.content)
```

##### get_state()

```python
def get_state(self) -> Dict[str, Any]
```

获取Agent当前状态信息。

**返回：**
- `Dict[str, Any]`: 包含Agent状态的字典

**示例：**
```python
state = agent.get_state()
print(f"Agent状态: {state['state']}")
print(f"消息数量: {state['message_count']}")
```

##### reset()

```python
def reset(self) -> None
```

重置Agent状态，清空对话历史。

**示例：**
```python
agent.reset()
```

##### get_memory_summary()

```python
async def get_memory_summary(self) -> Optional[Dict[str, Any]]
```

获取记忆系统摘要信息。

**返回：**
- `Optional[Dict[str, Any]]`: 记忆摘要信息

##### clear_memory()

```python
async def clear_memory(self, memory_type: Optional[str] = None) -> bool
```

清空记忆。

**参数：**
- `memory_type`: 记忆类型，None表示清空所有

**返回：**
- `bool`: 是否成功

## 模型适配器API

### OpenAIAdapter类

#### 构造函数

```python
OpenAIAdapter(
    api_key: str,
    model_name: str = "gpt-4",
    api_base: str = "https://api.openai.com/v1",
    max_tokens: int = 4096,
    temperature: float = 0.7,
    timeout: float = 60.0,
    max_retries: int = 3,
)
```

**参数：**
- `api_key`: OpenAI API密钥
- `model_name`: 模型名称
- `api_base`: API基础URL
- `max_tokens`: 最大token数
- `temperature`: 温度参数
- `timeout`: 请求超时时间
- `max_retries`: 最大重试次数

#### 主要方法

##### generate()

```python
async def generate(
    self,
    messages: List[Message],
    tools: Optional[List[Dict[str, Any]]] = None,
    **kwargs
) -> ModelResponse
```

生成模型响应。

**参数：**
- `messages`: 消息历史列表
- `tools`: 可用工具列表
- `**kwargs`: 额外参数

**返回：**
- `ModelResponse`: 模型响应

##### generate_stream()

```python
async def generate_stream(
    self,
    messages: List[Message],
    tools: Optional[List[Dict[str, Any]]] = None,
    **kwargs
) -> AsyncIterator[ModelResponse]
```

流式生成模型响应。

**参数：**
- `messages`: 消息历史列表
- `tools`: 可用工具列表
- `**kwargs`: 额外参数

**返回：**
- `AsyncIterator[ModelResponse]`: 流式响应迭代器

### ClaudeAdapter类

与OpenAIAdapter类似，但针对Anthropic Claude模型优化。

### QwenAdapter类

与OpenAIAdapter类似，但针对阿里云通义千问模型优化。

## 工具系统API

### ToolRegistry类

#### 主要方法

##### register_tool()

```python
def register_tool(
    self,
    tool: ToolInterface,
    max_concurrent: int = 1,
    timeout: float = 30.0,
    requires_confirmation: bool = False,
) -> None
```

注册工具。

**参数：**
- `tool`: 工具实例
- `max_concurrent`: 最大并发数
- `timeout`: 超时时间
- `requires_confirmation`: 是否需要确认

##### execute_tool()

```python
async def execute_tool(self, tool_call: ToolCall) -> ToolResult
```

执行单个工具。

**参数：**
- `tool_call`: 工具调用对象

**返回：**
- `ToolResult`: 工具执行结果

##### execute_tools_batch()

```python
async def execute_tools_batch(
    self,
    tool_calls: List[ToolCall],
    max_concurrent: int = 5,
) -> List[ToolResult]
```

批量执行工具。

**参数：**
- `tool_calls`: 工具调用列表
- `max_concurrent`: 最大并发数

**返回：**
- `List[ToolResult]`: 工具执行结果列表

### 示例工具

#### CalculatorTool类

```python
class CalculatorTool(ToolInterface):
    @property
    def name(self) -> str:
        return "calculator"
    
    @property
    def description(self) -> str:
        return "执行数学计算"
    
    async def execute(
        self,
        arguments: Dict[str, Any],
        context: Dict[str, Any] = None,
    ) -> ToolResult:
        # 实现计算逻辑
        pass
```

## 记忆系统API

### MemoryManager类

#### 构造函数

```python
MemoryManager(
    storage_path: str = "memory.db",
    short_term_ttl: int = 3600,
    working_ttl: int = 86400,
    long_term_ttl: Optional[int] = None,
    auto_cleanup: bool = True,
    cleanup_interval: int = 3600,
)
```

#### 主要方法

##### store()

```python
async def store(
    self,
    key: str,
    value: Any,
    memory_type: str = MemoryType.WORKING,
    ttl: Optional[int] = None,
    metadata: Optional[Dict[str, Any]] = None,
) -> bool
```

存储记忆。

**参数：**
- `key`: 记忆键
- `value`: 记忆值
- `memory_type`: 记忆类型
- `ttl`: 生存时间
- `metadata`: 元数据

**返回：**
- `bool`: 是否存储成功

##### retrieve()

```python
async def retrieve(
    self,
    key: str,
    memory_type: str = MemoryType.WORKING,
) -> Optional[Any]
```

检索记忆。

**参数：**
- `key`: 记忆键
- `memory_type`: 记忆类型

**返回：**
- `Optional[Any]`: 记忆值

##### search()

```python
async def search(
    self,
    query: str,
    memory_type: str = MemoryType.WORKING,
    limit: int = 10,
    similarity_threshold: float = 0.7,
) -> List[Dict[str, Any]]
```

搜索相似记忆。

**参数：**
- `query`: 搜索查询
- `memory_type`: 记忆类型
- `limit`: 返回数量限制
- `similarity_threshold`: 相似度阈值

**返回：**
- `List[Dict[str, Any]]`: 搜索结果列表

## 配置API

### FrameworkConfig类

```python
@dataclass
class FrameworkConfig:
    debug: bool = False
    environment: str = "development"
    max_iterations: int = 10
    max_execution_time: float = 300.0
    default_model: str = "gpt-4"
    models: Dict[str, ModelConfig] = field(default_factory=dict)
    tool_timeout: float = 30.0
    max_concurrent_tools: int = 5
```

#### 类方法

##### from_file()

```python
@classmethod
def from_file(cls, file_path: Union[str, Path]) -> "FrameworkConfig"
```

从文件加载配置。

##### from_env()

```python
@classmethod
def from_env(cls, prefix: str = "AI_AGENT_") -> "FrameworkConfig"
```

从环境变量加载配置。

## 异常API

### 异常层次结构

```
AgentFrameworkError
├── AgentError
│   ├── AgentExecutionError
│   └── AgentStateError
├── ModelError
│   ├── ModelConnectionError
│   ├── ModelAuthenticationError
│   └── ModelRateLimitError
├── ToolError
│   ├── ToolNotFoundError
│   ├── ToolExecutionError
│   └── ToolTimeoutError
├── MemoryError
│   ├── MemoryStorageError
│   └── MemoryRetrievalError
└── ConfigError
    ├── ConfigValidationError
    └── ConfigLoadError
```

### 异常属性

所有异常都包含以下属性：
- `message`: 错误消息
- `error_code`: 错误代码
- `cause`: 原始异常（如果有）

特定异常还包含额外属性：
- `ModelError.model_name`: 模型名称
- `ToolError.tool_name`: 工具名称
- `MemoryError.memory_type`: 记忆类型

## 监控API

### MonitoringSystem类

#### 构造函数

```python
MonitoringSystem(
    metrics_collector: Optional[MetricsCollector] = None,
    health_checker: Optional[HealthChecker] = None,
    http_port: int = 8000,
    enable_http_server: bool = True,
)
```

#### 主要方法

##### start()

```python
def start(self) -> None
```

启动监控系统。

##### stop()

```python
def stop(self) -> None
```

停止监控系统。

##### record_agent_request()

```python
def record_agent_request(self, duration: float, success: bool = True) -> None
```

记录Agent请求指标。

##### get_system_status()

```python
def get_system_status(self) -> Dict[str, Any]
```

获取系统状态。

## 使用示例

### 完整示例

```python
import asyncio
from ai_agent_framework import Agent, FrameworkConfig
from ai_agent_framework.models import OpenAIAdapter
from ai_agent_framework.memory import MemoryManager
from ai_agent_framework.tools import CalculatorTool
from ai_agent_framework.utils.tool_registry import tool_registry
from ai_agent_framework.monitoring import MonitoringSystem

async def main():
    # 创建配置
    config = FrameworkConfig(
        debug=True,
        max_iterations=5
    )
    
    # 注册工具
    calculator = CalculatorTool()
    tool_registry.register_tool(calculator)
    
    # 创建组件
    model = OpenAIAdapter(api_key="your-key", model_name="gpt-4")
    memory = MemoryManager()
    
    # 创建监控系统
    monitoring = MonitoringSystem()
    monitoring.start()
    
    # 创建Agent
    agent = Agent(
        config=config,
        model=model,
        memory=memory,
        reasoning_mode="react"
    )
    
    try:
        # 进行对话
        response = await agent.chat("请计算 25 * 4 + 10")
        print(f"Agent响应: {response.content}")
        
        # 获取状态
        state = agent.get_state()
        print(f"Agent状态: {state}")
        
        # 获取记忆摘要
        memory_summary = await agent.get_memory_summary()
        print(f"记忆摘要: {memory_summary}")
        
    finally:
        # 清理资源
        await memory.close()
        monitoring.stop()

if __name__ == "__main__":
    asyncio.run(main())
```

## 版本兼容性

- Python 3.9+
- 异步编程模型
- 类型提示支持
- Pydantic v2数据验证

## 更多信息

- [快速开始指南](../README.md#快速开始)
- [模块详细说明](modules/)
- [示例代码](../examples/)
- [贡献指南](../CONTRIBUTING.md)
