# AI Agent Framework 快速开始指南

欢迎使用AI Agent Framework！本指南将帮助您快速上手并构建您的第一个AI智能体应用。

## 📋 系统要求

- Python 3.9+
- Poetry (推荐) 或 pip
- Docker (可选，用于容器化部署)
- Git

## 🚀 安装

### 方式一：使用Poetry（推荐）

```bash
# 克隆项目
git clone https://github.com/your-org/ai-agent-framework.git
cd ai-agent-framework

# 安装依赖
poetry install

# 激活虚拟环境
poetry shell
```

### 方式二：使用pip

```bash
# 克隆项目
git clone https://github.com/your-org/ai-agent-framework.git
cd ai-agent-framework

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

## 🔧 配置

### 1. 环境变量配置

创建 `.env` 文件：

```bash
# AI模型API密钥
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
GOOGLE_API_KEY=your-google-api-key

# 数据库配置
DATABASE_URL=sqlite:///./data/agent.db

# Redis配置（可选）
REDIS_URL=redis://localhost:6379/0

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/agent.log
```

### 2. 配置文件

复制并编辑配置文件：

```bash
cp config/development.yaml.example config/development.yaml
```

## 🎯 第一个智能体

### 1. 基础示例

创建 `examples/my_first_agent.py`：

```python
import asyncio
from ai_agent_framework import Agent
from ai_agent_framework.providers import OpenAIProvider

async def main():
    # 创建AI提供者
    provider = OpenAIProvider(
        api_key="your-openai-api-key",
        model="gpt-3.5-turbo"
    )
    
    # 创建智能体
    agent = Agent(
        name="我的助手",
        description="一个友好的AI助手",
        provider=provider
    )
    
    # 启动智能体
    await agent.start()
    print("✅ 智能体已启动")
    
    # 发送消息
    response = await agent.process_message("你好！请介绍一下自己。")
    print(f"🤖 助手: {response.content}")
    
    # 停止智能体
    await agent.stop()
    print("👋 智能体已停止")

if __name__ == "__main__":
    asyncio.run(main())
```

运行示例：

```bash
python examples/my_first_agent.py
```

### 2. 带工具的智能体

```python
import asyncio
from ai_agent_framework import Agent
from ai_agent_framework.providers import OpenAIProvider
from ai_agent_framework.tools import DatabaseTool, GitTool

async def main():
    # 创建AI提供者
    provider = OpenAIProvider(api_key="your-openai-api-key")
    
    # 创建工具
    db_tool = DatabaseTool(db_path="./data/example.db")
    git_tool = GitTool()
    
    # 创建带工具的智能体
    agent = Agent(
        name="开发助手",
        description="专业的开发助手，可以操作数据库和Git",
        provider=provider,
        tools=[db_tool, git_tool]
    )
    
    await agent.start()
    
    # 测试数据库工具
    response = await agent.process_message(
        "请创建一个用户表，包含id、name和email字段"
    )
    print(f"🤖 助手: {response.content}")
    
    # 测试Git工具
    response = await agent.process_message("请查看当前Git仓库的状态")
    print(f"🤖 助手: {response.content}")
    
    await agent.stop()

if __name__ == "__main__":
    asyncio.run(main())
```

## 🛠️ 核心概念

### 1. 智能体 (Agent)

智能体是框架的核心组件，负责：
- 处理用户消息
- 调用AI模型生成响应
- 管理工具和记忆
- 维护对话上下文

### 2. 提供者 (Provider)

提供者封装了不同AI模型的API调用：
- `OpenAIProvider`: OpenAI GPT模型
- `AnthropicProvider`: Anthropic Claude模型
- `GoogleProvider`: Google Gemini模型

### 3. 工具 (Tools)

工具扩展智能体的能力：
- `DatabaseTool`: 数据库操作
- `GitTool`: Git版本控制
- `FileSystemTool`: 文件系统操作
- 自定义工具

### 4. 记忆 (Memory)

记忆系统管理对话历史和知识：
- 短期记忆：当前对话上下文
- 长期记忆：持久化的知识和经验
- 语义记忆：基于向量的相似性搜索

## 🔄 高级功能

### 1. 流式响应

```python
async def stream_example():
    agent = Agent(name="助手", provider=provider)
    await agent.start()
    
    async for chunk in agent.stream_response("请详细解释人工智能"):
        print(chunk.content, end="", flush=True)
    
    await agent.stop()
```

### 2. 多智能体协作

```python
from ai_agent_framework import MultiAgentSystem

async def multi_agent_example():
    # 创建多智能体系统
    mas = MultiAgentSystem()
    
    # 添加智能体
    await mas.add_agent("researcher", research_agent)
    await mas.add_agent("writer", writing_agent)
    
    # 执行协作任务
    result = await mas.execute_workflow([
        {"agent": "researcher", "task": "研究AI发展趋势"},
        {"agent": "writer", "task": "撰写研究报告"}
    ])
```

### 3. 自定义工具

```python
from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult

class WeatherTool(ToolInterface):
    @property
    def name(self) -> str:
        return "weather"
    
    @property
    def description(self) -> str:
        return "获取天气信息"
    
    @property
    def parameters_schema(self) -> dict:
        return {
            "type": "object",
            "properties": {
                "city": {"type": "string", "description": "城市名称"}
            },
            "required": ["city"]
        }
    
    async def validate_arguments(self, arguments: dict) -> bool:
        return "city" in arguments
    
    async def execute(self, arguments: dict, context=None) -> ToolResult:
        city = arguments["city"]
        # 这里调用天气API
        weather_info = f"{city}今天晴天，温度25°C"
        
        return ToolResult(
            success=True,
            result=weather_info
        )
```

## 🧪 测试

运行测试套件：

```bash
# 运行所有测试
poetry run pytest

# 运行特定测试
poetry run pytest tests/test_agents.py

# 运行覆盖率测试
poetry run pytest --cov=ai_agent_framework
```

## 📊 监控和调试

### 1. 启用日志

```python
import logging
from ai_agent_framework.utils.logging_system import logging_system

# 配置日志级别
logging_system.configure(level=logging.DEBUG)

# 获取日志器
logger = logging_system.get_logger("my_app")
logger.info("应用启动")
```

### 2. 性能监控

```python
from ai_agent_framework.monitoring import PerformanceMonitor

# 启用监控
monitor = PerformanceMonitor()
await monitor.start()

# 查看统计信息
stats = await monitor.get_statistics()
print(f"平均响应时间: {stats['avg_response_time']}ms")
```

## 🚀 部署

### 1. Docker部署

```bash
# 构建镜像
docker build -t ai-agent-framework .

# 运行容器
docker run -p 8000:8000 ai-agent-framework
```

### 2. 使用Docker Compose

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 3. Kubernetes部署

```bash
# 应用配置
kubectl apply -f k8s/

# 查看部署状态
kubectl get pods -n ai-agent-framework
```

## 🆘 故障排除

### 常见问题

1. **API密钥错误**
   - 检查环境变量是否正确设置
   - 确认API密钥有效且有足够额度

2. **依赖安装失败**
   - 更新pip: `pip install --upgrade pip`
   - 使用国内镜像: `pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/`

3. **数据库连接失败**
   - 检查数据库URL配置
   - 确认数据库服务正在运行

4. **内存不足**
   - 调整模型参数
   - 增加系统内存
   - 使用更小的模型

## 📚 下一步

- 阅读 [API文档](api-reference.md)
- 查看 [架构设计](architecture.md)
- 学习 [最佳实践](best-practices.md)
- 参与 [社区讨论](https://github.com/your-org/ai-agent-framework/discussions)

## 🤝 获取帮助

- 📖 [完整文档](https://ai-agent-framework.readthedocs.io/)
- 🐛 [问题反馈](https://github.com/your-org/ai-agent-framework/issues)
- 💬 [讨论区](https://github.com/your-org/ai-agent-framework/discussions)
- 📧 [邮件支持](mailto:<EMAIL>)
