# AI Agent Framework API 参考

本文档提供了AI Agent Framework的完整API参考。

## 核心模块

### Agent 类

智能体的主要类，负责管理AI模型、工具和记忆。

```python
from ai_agent_framework import Agent

class Agent:
    def __init__(
        self,
        name: str,
        description: str = "",
        provider: AIProvider,
        tools: List[ToolInterface] = None,
        memory_manager: MemoryManager = None,
        config: Dict[str, Any] = None
    )
```

#### 参数

- `name` (str): 智能体名称
- `description` (str): 智能体描述
- `provider` (AIProvider): AI模型提供者
- `tools` (List[ToolInterface]): 工具列表
- `memory_manager` (MemoryManager): 记忆管理器
- `config` (Dict): 配置参数

#### 方法

##### `async start() -> bool`

启动智能体。

```python
agent = Agent(name="助手", provider=provider)
success = await agent.start()
```

##### `async stop() -> bool`

停止智能体。

```python
await agent.stop()
```

##### `async process_message(message: str, context: Dict = None) -> AgentResponse`

处理用户消息。

```python
response = await agent.process_message("你好")
print(response.content)
```

##### `async stream_response(message: str) -> AsyncIterator[AgentResponse]`

流式处理消息。

```python
async for chunk in agent.stream_response("请详细解释AI"):
    print(chunk.content, end="")
```

### AI提供者

#### OpenAIProvider

OpenAI GPT模型提供者。

```python
from ai_agent_framework.providers import OpenAIProvider

provider = OpenAIProvider(
    api_key="your-api-key",
    model="gpt-4",
    temperature=0.7,
    max_tokens=2000
)
```

#### AnthropicProvider

Anthropic Claude模型提供者。

```python
from ai_agent_framework.providers import AnthropicProvider

provider = AnthropicProvider(
    api_key="your-api-key",
    model="claude-3-sonnet-20240229",
    temperature=0.7,
    max_tokens=2000
)
```

#### GoogleProvider

Google Gemini模型提供者。

```python
from ai_agent_framework.providers import GoogleProvider

provider = GoogleProvider(
    api_key="your-api-key",
    model="gemini-pro",
    temperature=0.7
)
```

### 工具系统

#### ToolInterface

所有工具的基础接口。

```python
from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult

class CustomTool(ToolInterface):
    @property
    def name(self) -> str:
        return "custom_tool"
    
    @property
    def description(self) -> str:
        return "自定义工具"
    
    @property
    def parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "input": {"type": "string"}
            },
            "required": ["input"]
        }
    
    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        return "input" in arguments
    
    async def execute(
        self, 
        arguments: Dict[str, Any], 
        context: Dict[str, Any] = None
    ) -> ToolResult:
        return ToolResult(
            success=True,
            result="处理结果"
        )
```

#### 内置工具

##### DatabaseTool

数据库操作工具。

```python
from ai_agent_framework.tools import DatabaseTool

db_tool = DatabaseTool(db_path="./data.db")

# 使用示例
result = await db_tool.execute({
    "action": "query",
    "sql": "SELECT * FROM users"
})
```

支持的操作：
- `connect`: 连接数据库
- `disconnect`: 断开连接
- `query`: 执行查询
- `execute`: 执行SQL语句
- `create_table`: 创建表
- `insert`: 插入数据
- `update`: 更新数据
- `delete`: 删除数据
- `tables`: 列出所有表
- `describe`: 描述表结构

##### GitTool

Git版本控制工具。

```python
from ai_agent_framework.tools import GitTool

git_tool = GitTool(repo_path="./")

# 使用示例
result = await git_tool.execute({
    "action": "status"
})
```

支持的操作：
- `status`: 获取状态
- `add`: 添加文件
- `commit`: 提交变更
- `push`: 推送到远程
- `pull`: 从远程拉取
- `branch`: 分支管理
- `checkout`: 切换分支
- `merge`: 合并分支
- `log`: 查看历史
- `diff`: 查看差异

### 记忆管理

#### MemoryManager

记忆管理器，负责存储和检索对话历史和知识。

```python
from ai_agent_framework.memory import MemoryManager

memory = MemoryManager(
    short_term_capacity=100,
    long_term_capacity=1000
)

# 存储记忆
await memory.store_memory(
    content="用户喜欢喝咖啡",
    memory_type="preference"
)

# 检索记忆
memories = await memory.retrieve_memories(
    query="用户喜好",
    limit=5
)
```

### MCP服务

#### MCPServiceRegistry

MCP服务注册表。

```python
from ai_agent_framework.mcp import MCPServiceRegistry
from ai_agent_framework.mcp.services import FileSystemMCPService

registry = MCPServiceRegistry()
await registry.start()

# 注册服务
fs_service = FileSystemMCPService(allowed_paths=["/safe/path"])
await registry.register_service(fs_service)

# 发现服务
services = registry.discover_services(MCPCapability.FILE_READ)
```

## HTTP API

当运行服务器模式时，框架提供RESTful API接口。

### 启动服务器

```bash
python -m ai_agent_framework.server
```

### API端点

#### POST /chat

发送聊天消息。

**请求体：**
```json
{
    "message": "你好",
    "agent_id": "default",
    "session_id": "session_123"
}
```

**响应：**
```json
{
    "response": "你好！我是AI助手，很高兴为您服务。",
    "agent_id": "default",
    "session_id": "session_123",
    "timestamp": "2024-01-01T12:00:00Z"
}
```

#### GET /agents

列出所有智能体。

**响应：**
```json
{
    "agents": [
        {
            "id": "default",
            "name": "默认助手",
            "description": "默认的AI助手",
            "status": "running"
        }
    ]
}
```

#### GET /agents/{agent_id}

获取特定智能体信息。

**响应：**
```json
{
    "id": "default",
    "name": "默认助手",
    "description": "默认的AI助手",
    "status": "running",
    "provider": "OpenAIProvider",
    "tools": ["database", "git"]
}
```

#### GET /health

健康检查。

**响应：**
```json
{
    "status": "healthy",
    "timestamp": "2024-01-01T12:00:00Z",
    "version": "1.0.0"
}
```

#### GET /metrics

获取系统指标。

**响应：**
```json
{
    "total_agents": 1,
    "running_agents": 1,
    "timestamp": "2024-01-01T12:00:00Z"
}
```

## 配置

### 环境变量

- `OPENAI_API_KEY`: OpenAI API密钥
- `ANTHROPIC_API_KEY`: Anthropic API密钥
- `GOOGLE_API_KEY`: Google API密钥
- `DATABASE_URL`: 数据库连接URL
- `REDIS_URL`: Redis连接URL
- `LOG_LEVEL`: 日志级别 (DEBUG, INFO, WARNING, ERROR)
- `LOG_FILE`: 日志文件路径

### 配置文件

使用YAML格式的配置文件：

```yaml
app:
  name: "AI Agent Framework"
  version: "1.0.0"
  debug: false

server:
  host: "0.0.0.0"
  port: 8000
  workers: 4

models:
  openai:
    api_key: "${OPENAI_API_KEY}"
    default_model: "gpt-4"
    timeout: 60

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
```

## 异常处理

### 自定义异常

```python
from ai_agent_framework.exceptions import (
    AgentError,
    ModelError,
    ToolError,
    MemoryError
)

try:
    response = await agent.process_message("测试")
except AgentError as e:
    print(f"智能体错误: {e}")
except ModelError as e:
    print(f"模型错误: {e}")
except ToolError as e:
    print(f"工具错误: {e}")
```

## 类型定义

### 消息类型

```python
from ai_agent_framework.core.messages import (
    Message,
    MessageRole,
    AgentResponse,
    ToolCall,
    ToolResult
)

# 消息角色
class MessageRole(str, Enum):
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    TOOL = "tool"

# 智能体响应
class AgentResponse(BaseModel):
    content: str
    role: MessageRole = MessageRole.ASSISTANT
    tool_calls: List[ToolCall] = []
    metadata: Dict[str, Any] = {}
```

## 最佳实践

1. **错误处理**: 始终使用try-catch处理异常
2. **资源管理**: 使用async with或手动调用start/stop
3. **配置管理**: 使用环境变量和配置文件
4. **日志记录**: 启用适当的日志级别
5. **性能监控**: 在生产环境中启用监控

## 示例代码

更多示例代码请参考 `examples/` 目录：

- `basic_agent.py`: 基础智能体示例
- `tool_usage.py`: 工具使用示例
- `memory_example.py`: 记忆管理示例
- `multi_agent.py`: 多智能体协作示例
- `api_client.py`: API客户端示例
