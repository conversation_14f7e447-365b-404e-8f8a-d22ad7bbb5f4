# AI Agent Framework 配置示例
# 这个文件展示了框架的完整配置选项

# 基础配置
debug: false
environment: "development"
max_iterations: 10
max_execution_time: 300.0

# 默认模型
default_model: "gpt-4"

# 模型配置
models:
  gpt-4:
    name: "gpt-4"
    provider: "openai"
    model_id: "gpt-4"
    api_key: "${OPENAI_API_KEY}"  # 从环境变量读取
    max_tokens: 4096
    temperature: 0.7
    timeout: 60.0
    max_retries: 3
    supports_tools: true
    supports_streaming: true
    extra_params: {}

  claude-3:
    name: "claude-3"
    provider: "anthropic"
    model_id: "claude-3-sonnet-20240229"
    api_key: "${ANTHROPIC_API_KEY}"
    max_tokens: 4096
    temperature: 0.7
    timeout: 60.0
    max_retries: 3
    supports_tools: true
    supports_streaming: true
    extra_params: {}

  qwen-max:
    name: "qwen-max"
    provider: "qwen"
    model_id: "qwen-max"
    api_key: "${QWEN_API_KEY}"
    api_base: "https://dashscope.aliyuncs.com/api/v1"
    max_tokens: 8192
    temperature: 0.7
    timeout: 60.0
    max_retries: 3
    supports_tools: true
    supports_streaming: true
    extra_params: {}

# 工具配置
tool_timeout: 30.0
max_concurrent_tools: 5

tools:
  calculator:
    enabled: true
    timeout: 10.0
    requires_confirmation: false
    max_concurrent: 2
    config: {}

  weather:
    enabled: true
    timeout: 15.0
    requires_confirmation: false
    max_concurrent: 1
    config:
      api_key: "${WEATHER_API_KEY}"
      default_units: "metric"

  search:
    enabled: true
    timeout: 20.0
    requires_confirmation: false
    max_concurrent: 1
    config:
      search_engine: "google"
      api_key: "${SEARCH_API_KEY}"
      max_results: 10

# 记忆配置
memory:
  storage_type: "sqlite"
  storage_path: "agent_memory.db"
  short_term_ttl: 3600      # 1小时
  working_ttl: 86400        # 1天
  long_term_ttl: null       # 永久
  similarity_threshold: 0.7
  max_search_results: 10
  auto_cleanup: true
  cleanup_interval: 3600    # 1小时

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "logs/agent.log"
  max_file_size: 10485760   # 10MB
  backup_count: 5
  structured: true
  json_format: false

# 监控配置
monitoring:
  enabled: true
  metrics_port: 8000
  health_check_port: 8001
  prometheus_enabled: true
  prometheus_prefix: "ai_agent"
  track_performance: true
  track_errors: true
