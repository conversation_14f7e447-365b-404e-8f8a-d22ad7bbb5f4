#!/usr/bin/env python3
"""
AI Agent Framework 高级工具使用示例

这个示例展示了如何使用新增的高级工具：
- 消息队列工具 (MessageQueueTool)
- 缓存工具 (CacheTool)
- 文件存储工具 (FileStorageTool)
- 数据处理工具 (DataProcessingTool)
"""

import asyncio
import json
import tempfile
from pathlib import Path

from ai_agent_framework import Agent, FrameworkConfig
from ai_agent_framework.core.config import ModelConfig, LoggingConfig
from ai_agent_framework.models import OpenAIAdapter
from ai_agent_framework.memory import MemoryManager
from ai_agent_framework.tools import (
    MessageQueueTool,
    CacheTool,
    FileStorageTool,
    DataProcessingTool,
    MessageQueueType,
    CacheType,
    StorageType,
)
from ai_agent_framework.utils.tool_registry import tool_registry
from ai_agent_framework.utils.logging_system import logging_system


async def setup_advanced_tools():
    """设置高级工具"""
    print("🔧 正在设置高级工具...")
    
    # 创建消息队列工具
    mq_tool = MessageQueueTool(
        queue_type=MessageQueueType.MEMORY,
        default_timeout=30
    )
    
    # 创建缓存工具
    cache_tool = CacheTool(
        cache_type=CacheType.MEMORY,
        connection_config={"max_size": 1000},
        default_ttl=3600
    )
    
    # 创建文件存储工具
    storage_tool = FileStorageTool(
        storage_type=StorageType.LOCAL,
        connection_config={"base_path": "./temp_storage"}
    )
    
    # 创建数据处理工具
    data_tool = DataProcessingTool()
    
    # 注册工具
    tool_registry.register_tool(mq_tool, max_concurrent=5)
    tool_registry.register_tool(cache_tool, max_concurrent=10)
    tool_registry.register_tool(storage_tool, max_concurrent=3)
    tool_registry.register_tool(data_tool, max_concurrent=5)
    
    print("✅ 高级工具设置完成")
    return mq_tool, cache_tool, storage_tool, data_tool


async def demo_message_queue(mq_tool):
    """演示消息队列功能"""
    print("\n📨 消息队列工具演示")
    print("-" * 40)
    
    # 连接到消息队列
    result = await mq_tool.execute({"action": "connect"})
    print(f"连接结果: {result.success}")
    
    # 创建队列
    result = await mq_tool.execute({
        "action": "create_queue",
        "queue_name": "demo_queue",
        "durable": True
    })
    print(f"创建队列: {result.result['created']}")
    
    # 发送消息
    messages = [
        {"content": "普通消息", "priority": "normal"},
        {"content": "高优先级消息", "priority": "high"},
        {"content": "紧急消息", "priority": "urgent"},
    ]
    
    for msg in messages:
        result = await mq_tool.execute({
            "action": "send",
            "queue_name": "demo_queue",
            "message": msg
        })
        print(f"发送消息 '{msg['content']}': {result.result['sent']}")
    
    # 接收消息（按优先级顺序）
    print("\n接收消息:")
    for i in range(3):
        result = await mq_tool.execute({
            "action": "receive",
            "queue_name": "demo_queue"
        })
        if result.result["received"]:
            msg = result.result["message"]
            print(f"  收到: {msg['content']} (优先级: {msg['priority']})")
    
    # 获取队列信息
    result = await mq_tool.execute({
        "action": "queue_info",
        "queue_name": "demo_queue"
    })
    print(f"队列信息: {result.result}")


async def demo_cache(cache_tool):
    """演示缓存功能"""
    print("\n💾 缓存工具演示")
    print("-" * 40)
    
    # 连接到缓存
    result = await cache_tool.execute({"action": "connect"})
    print(f"连接结果: {result.success}")
    
    # 设置单个缓存
    user_data = {
        "id": 123,
        "name": "张三",
        "email": "<EMAIL>",
        "role": "developer"
    }
    
    result = await cache_tool.execute({
        "action": "set",
        "key": "user:123",
        "value": user_data,
        "ttl_seconds": 1800,
        "tags": ["user", "profile"]
    })
    print(f"设置用户缓存: {result.result['set']}")
    
    # 批量设置配置
    config_data = {
        "app:theme": "dark",
        "app:language": "zh-CN",
        "app:timezone": "Asia/Shanghai",
        "app:debug": False
    }
    
    result = await cache_tool.execute({
        "action": "set_multi",
        "key_value_pairs": config_data,
        "ttl_seconds": 7200
    })
    print(f"批量设置配置: {result.result['success_count']}/{result.result['total_count']}")
    
    # 获取缓存
    result = await cache_tool.execute({
        "action": "get",
        "key": "user:123"
    })
    if result.result["found"]:
        print(f"获取用户数据: {result.result['value']['name']}")
    
    # 数值操作
    await cache_tool.execute({
        "action": "set",
        "key": "counter",
        "value": 10
    })
    
    result = await cache_tool.execute({
        "action": "increment",
        "key": "counter",
        "delta": 5
    })
    print(f"计数器增加: {result.result['old_value']} -> {result.result['new_value']}")
    
    # 获取统计信息
    result = await cache_tool.execute({"action": "stats"})
    stats = result.result
    print(f"缓存统计: {stats['total_items']} 项, 命中率: {stats['hit_rate']:.2%}")


async def demo_file_storage(storage_tool):
    """演示文件存储功能"""
    print("\n📁 文件存储工具演示")
    print("-" * 40)
    
    # 连接到存储
    result = await storage_tool.execute({"action": "connect"})
    print(f"连接结果: {result.success}")
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write("这是一个测试文件\n包含中文内容\n用于演示文件存储功能")
        temp_file_path = f.name
    
    try:
        # 上传文件
        result = await storage_tool.execute({
            "action": "upload",
            "local_path": temp_file_path,
            "remote_path": "documents/test_file.txt",
            "metadata": {
                "category": "demo",
                "author": "AI Agent",
                "description": "测试文件"
            }
        })
        print(f"上传文件: {result.result['uploaded']}")
        
        # 检查文件存在
        result = await storage_tool.execute({
            "action": "exists",
            "remote_path": "documents/test_file.txt"
        })
        print(f"文件存在: {result.result['exists']}")
        
        # 获取文件信息
        result = await storage_tool.execute({
            "action": "info",
            "remote_path": "documents/test_file.txt"
        })
        if result.result["found"]:
            file_info = result.result["file_info"]
            print(f"文件信息: {file_info['name']}, 大小: {file_info['size']} 字节")
        
        # 创建目录
        result = await storage_tool.execute({
            "action": "create_dir",
            "directory_path": "backup"
        })
        print(f"创建目录: {result.result['created']}")
        
        # 复制文件
        result = await storage_tool.execute({
            "action": "copy",
            "source_path": "documents/test_file.txt",
            "destination_path": "backup/test_file_backup.txt"
        })
        print(f"复制文件: {result.result['copied']}")
        
        # 列出文件
        result = await storage_tool.execute({
            "action": "list",
            "prefix": "",
            "limit": 10
        })
        print(f"文件列表: {len(result.result['files'])} 个文件")
        for file_info in result.result['files']:
            print(f"  - {file_info['path']} ({file_info['size']} 字节)")
    
    finally:
        # 清理临时文件
        Path(temp_file_path).unlink(missing_ok=True)


async def demo_data_processing(data_tool):
    """演示数据处理功能"""
    print("\n🔄 数据处理工具演示")
    print("-" * 40)
    
    # JSON处理
    json_data = {
        "users": [
            {"name": "张三", "age": 30, "email": "<EMAIL>"},
            {"name": "李四", "age": 25, "email": "<EMAIL>"}
        ],
        "total": 2
    }
    
    result = await data_tool.execute({
        "action": "format_json",
        "data": json_data,
        "indent": 2
    })
    print("格式化JSON:")
    print(result.result["data"])
    
    # CSV处理
    csv_data = "姓名,年龄,城市\n张三,30,北京\n李四,25,上海\n王五,35,广州"
    
    result = await data_tool.execute({
        "action": "parse_csv",
        "data": csv_data
    })
    print(f"\nCSV解析: {result.result['row_count']} 行数据")
    for i, row in enumerate(result.result['rows']):
        print(f"  行{i+1}: {dict(zip(result.result['headers'], row))}")
    
    # 文本提取
    text = "联系我们：邮箱 <EMAIL> 和 <EMAIL>，电话 138-0013-8000，网站 https://example.com"
    
    result = await data_tool.execute({
        "action": "extract_emails",
        "data": text
    })
    print(f"\n提取邮箱: {result.result['emails']}")
    
    result = await data_tool.execute({
        "action": "extract_urls",
        "data": text
    })
    print(f"提取URL: {result.result['urls']}")
    
    # 数据验证
    result = await data_tool.execute({
        "action": "validate_email",
        "data": "<EMAIL>"
    })
    print(f"\n邮箱验证: {result.result['message']}")
    
    # Base64编码
    result = await data_tool.execute({
        "action": "base64_encode",
        "data": "Hello, 世界!"
    })
    print(f"Base64编码: {result.result['data']}")
    
    # Base64解码
    result = await data_tool.execute({
        "action": "base64_decode",
        "data": result.result['data']
    })
    print(f"Base64解码: {result.result['data']}")


async def main():
    """主函数"""
    print("🚀 AI Agent Framework 高级工具演示")
    print("=" * 50)
    
    # 设置日志
    import logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    try:
        # 设置工具
        mq_tool, cache_tool, storage_tool, data_tool = await setup_advanced_tools()
        
        # 运行演示
        await demo_message_queue(mq_tool)
        await demo_cache(cache_tool)
        await demo_file_storage(storage_tool)
        await demo_data_processing(data_tool)
        
        print("\n🎉 所有演示完成！")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        print("\n🧹 清理资源...")
        try:
            # 断开工具连接
            await mq_tool.execute({"action": "disconnect"})
            await cache_tool.execute({"action": "disconnect"})
            await storage_tool.execute({"action": "disconnect"})
            
            # 清理临时存储目录
            import shutil
            temp_storage = Path("./temp_storage")
            if temp_storage.exists():
                shutil.rmtree(temp_storage)
                print("✅ 临时文件已清理")
        except Exception as e:
            print(f"⚠️ 清理过程中出现警告: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
