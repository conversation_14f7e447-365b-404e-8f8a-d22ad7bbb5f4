"""
AI Agent Framework API客户端示例

演示如何使用HTTP API与AI智能体进行交互。
"""

import asyncio
import json
from typing import Dict, Any

import aiohttp


class AIAgentClient:
    """AI Agent Framework API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """
        初始化客户端
        
        Args:
            base_url: API服务器地址
        """
        self.base_url = base_url.rstrip('/')
        self.session = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        async with self.session.get(f"{self.base_url}/health") as response:
            return await response.json()
    
    async def chat(
        self,
        message: str,
        agent_id: str = "default",
        session_id: str = None
    ) -> Dict[str, Any]:
        """
        发送聊天消息
        
        Args:
            message: 消息内容
            agent_id: Agent ID
            session_id: 会话ID
            
        Returns:
            Dict: 响应结果
        """
        data = {
            "message": message,
            "agent_id": agent_id
        }
        
        if session_id:
            data["session_id"] = session_id
        
        async with self.session.post(
            f"{self.base_url}/chat",
            json=data
        ) as response:
            return await response.json()
    
    async def list_agents(self) -> Dict[str, Any]:
        """列出所有Agent"""
        async with self.session.get(f"{self.base_url}/agents") as response:
            return await response.json()
    
    async def get_agent(self, agent_id: str) -> Dict[str, Any]:
        """获取特定Agent信息"""
        async with self.session.get(f"{self.base_url}/agents/{agent_id}") as response:
            return await response.json()
    
    async def get_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        async with self.session.get(f"{self.base_url}/metrics") as response:
            return await response.json()


async def basic_chat_example():
    """基本聊天示例"""
    print("🤖 基本聊天示例")
    print("=" * 50)
    
    async with AIAgentClient() as client:
        # 健康检查
        health = await client.health_check()
        print(f"服务状态: {health['status']}")
        
        # 发送消息
        messages = [
            "你好！",
            "请介绍一下你自己",
            "你能做什么？",
            "谢谢！"
        ]
        
        for message in messages:
            print(f"\n👤 用户: {message}")
            
            try:
                response = await client.chat(message)
                print(f"🤖 助手: {response['response']}")
            except Exception as e:
                print(f"❌ 错误: {e}")


async def multi_session_example():
    """多会话示例"""
    print("\n🔄 多会话示例")
    print("=" * 50)
    
    async with AIAgentClient() as client:
        # 会话1
        print("\n📱 会话1:")
        response1 = await client.chat("我叫张三", session_id="session_1")
        print(f"👤 用户: 我叫张三")
        print(f"🤖 助手: {response1['response']}")
        
        # 会话2
        print("\n📱 会话2:")
        response2 = await client.chat("我叫李四", session_id="session_2")
        print(f"👤 用户: 我叫李四")
        print(f"🤖 助手: {response2['response']}")
        
        # 回到会话1
        print("\n📱 回到会话1:")
        response3 = await client.chat("我的名字是什么？", session_id="session_1")
        print(f"👤 用户: 我的名字是什么？")
        print(f"🤖 助手: {response3['response']}")


async def agent_management_example():
    """Agent管理示例"""
    print("\n🛠️ Agent管理示例")
    print("=" * 50)
    
    async with AIAgentClient() as client:
        # 列出所有Agent
        agents = await client.list_agents()
        print(f"可用的Agent数量: {len(agents['agents'])}")
        
        for agent in agents['agents']:
            print(f"  - {agent['id']}: {agent['name']} ({agent['status']})")
        
        # 获取默认Agent详细信息
        if agents['agents']:
            agent_id = agents['agents'][0]['id']
            agent_info = await client.get_agent(agent_id)
            
            print(f"\nAgent详细信息 ({agent_id}):")
            print(f"  名称: {agent_info['name']}")
            print(f"  描述: {agent_info['description']}")
            print(f"  状态: {agent_info['status']}")
            print(f"  提供者: {agent_info['provider']}")
            print(f"  工具: {agent_info.get('tools', [])}")


async def metrics_example():
    """系统指标示例"""
    print("\n📊 系统指标示例")
    print("=" * 50)
    
    async with AIAgentClient() as client:
        metrics = await client.get_metrics()
        
        print(f"总Agent数量: {metrics['total_agents']}")
        print(f"运行中的Agent: {metrics['running_agents']}")
        print(f"统计时间: {metrics['timestamp']}")


async def interactive_chat():
    """交互式聊天"""
    print("\n💬 交互式聊天")
    print("=" * 50)
    print("输入 'quit' 或 'exit' 退出聊天")
    
    async with AIAgentClient() as client:
        # 检查服务状态
        try:
            health = await client.health_check()
            if health['status'] != 'healthy':
                print("❌ 服务不可用")
                return
        except Exception as e:
            print(f"❌ 无法连接到服务: {e}")
            return
        
        session_id = "interactive_session"
        
        while True:
            try:
                # 获取用户输入
                user_input = input("\n👤 你: ").strip()
                
                if user_input.lower() in ['quit', 'exit', '退出']:
                    print("👋 再见！")
                    break
                
                if not user_input:
                    continue
                
                # 发送消息
                response = await client.chat(
                    message=user_input,
                    session_id=session_id
                )
                
                print(f"🤖 助手: {response['response']}")
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 错误: {e}")


async def main():
    """主函数"""
    print("🚀 AI Agent Framework API客户端示例")
    print("=" * 60)
    
    try:
        # 运行各种示例
        await basic_chat_example()
        await multi_session_example()
        await agent_management_example()
        await metrics_example()
        
        # 交互式聊天（可选）
        choice = input("\n是否启动交互式聊天？(y/N): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            await interactive_chat()
    
    except Exception as e:
        print(f"❌ 运行示例时出错: {e}")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
