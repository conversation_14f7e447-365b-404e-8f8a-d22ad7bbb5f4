#!/usr/bin/env python3
"""
爬虫工具性能优化指南

展示如何优化爬虫工具的性能，包括并发控制、缓存策略、
资源管理、错误重试等最佳实践。
"""

import asyncio
import hashlib
import json
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from ai_agent_framework.tools.crawler_tool import CrawlerTool, CrawlerServiceType
from ai_agent_framework.utils.logging_system import logging_system


@dataclass
class CrawlResult:
    """爬取结果数据类"""
    url: str
    success: bool
    data: Any
    duration: float
    timestamp: float
    error: Optional[str] = None


class CrawlerCache:
    """爬虫结果缓存"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 3600):
        """
        初始化缓存
        
        Args:
            max_size: 最大缓存条目数
            ttl: 缓存生存时间（秒）
        """
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.max_size = max_size
        self.ttl = ttl
        self.access_times: Dict[str, float] = {}
    
    def _generate_key(self, arguments: Dict[str, Any]) -> str:
        """生成缓存键"""
        # 排序参数以确保一致性
        sorted_args = json.dumps(arguments, sort_keys=True)
        return hashlib.md5(sorted_args.encode()).hexdigest()
    
    def get(self, arguments: Dict[str, Any]) -> Optional[Any]:
        """获取缓存结果"""
        key = self._generate_key(arguments)
        
        if key not in self.cache:
            return None
        
        # 检查是否过期
        cache_entry = self.cache[key]
        if time.time() - cache_entry["timestamp"] > self.ttl:
            del self.cache[key]
            if key in self.access_times:
                del self.access_times[key]
            return None
        
        # 更新访问时间
        self.access_times[key] = time.time()
        return cache_entry["data"]
    
    def set(self, arguments: Dict[str, Any], result: Any):
        """设置缓存结果"""
        key = self._generate_key(arguments)
        
        # 如果缓存已满，删除最久未访问的条目
        if len(self.cache) >= self.max_size:
            self._evict_lru()
        
        self.cache[key] = {
            "data": result,
            "timestamp": time.time()
        }
        self.access_times[key] = time.time()
    
    def _evict_lru(self):
        """删除最久未访问的条目"""
        if not self.access_times:
            return
        
        # 找到最久未访问的键
        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        
        # 删除条目
        if lru_key in self.cache:
            del self.cache[lru_key]
        del self.access_times[lru_key]
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.access_times.clear()
    
    def stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            "size": len(self.cache),
            "max_size": self.max_size,
            "ttl": self.ttl,
            "hit_rate": getattr(self, "_hit_count", 0) / max(getattr(self, "_total_requests", 1), 1)
        }


class OptimizedCrawlerTool:
    """优化的爬虫工具包装器"""
    
    def __init__(
        self,
        crawler: CrawlerTool,
        enable_cache: bool = True,
        cache_ttl: int = 3600,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        rate_limit: float = 1.0
    ):
        """
        初始化优化的爬虫工具
        
        Args:
            crawler: 基础爬虫工具
            enable_cache: 是否启用缓存
            cache_ttl: 缓存生存时间
            max_retries: 最大重试次数
            retry_delay: 重试延迟
            rate_limit: 请求频率限制（秒）
        """
        self.crawler = crawler
        self.enable_cache = enable_cache
        self.cache = CrawlerCache(ttl=cache_ttl) if enable_cache else None
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.rate_limit = rate_limit
        self.last_request_time = 0
        self.logger = logging_system.get_logger("optimized_crawler")
        
        # 统计信息
        self.stats = {
            "total_requests": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "retries": 0,
            "errors": 0,
            "total_duration": 0
        }
    
    async def execute_with_optimization(self, arguments: Dict[str, Any]) -> CrawlResult:
        """
        执行优化的爬取任务
        
        Args:
            arguments: 爬取参数
            
        Returns:
            CrawlResult: 爬取结果
        """
        start_time = time.time()
        self.stats["total_requests"] += 1
        
        try:
            # 1. 检查缓存
            if self.enable_cache and self.cache:
                cached_result = self.cache.get(arguments)
                if cached_result:
                    self.stats["cache_hits"] += 1
                    self.logger.debug(f"缓存命中: {arguments.get('url')}")
                    return CrawlResult(
                        url=arguments.get("url", ""),
                        success=True,
                        data=cached_result,
                        duration=time.time() - start_time,
                        timestamp=time.time()
                    )
                else:
                    self.stats["cache_misses"] += 1
            
            # 2. 频率限制
            await self._apply_rate_limit()
            
            # 3. 执行爬取任务（带重试）
            result = await self._execute_with_retry(arguments)
            
            # 4. 缓存结果
            if self.enable_cache and self.cache and result.success:
                self.cache.set(arguments, result.data)
            
            # 5. 更新统计
            self.stats["total_duration"] += result.duration
            
            return result
            
        except Exception as e:
            self.stats["errors"] += 1
            self.logger.error(f"优化爬取执行失败: {e}")
            return CrawlResult(
                url=arguments.get("url", ""),
                success=False,
                data=None,
                duration=time.time() - start_time,
                timestamp=time.time(),
                error=str(e)
            )
    
    async def _apply_rate_limit(self):
        """应用频率限制"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.rate_limit:
            delay = self.rate_limit - time_since_last
            self.logger.debug(f"频率限制延迟: {delay:.2f}秒")
            await asyncio.sleep(delay)
        
        self.last_request_time = time.time()
    
    async def _execute_with_retry(self, arguments: Dict[str, Any]) -> CrawlResult:
        """
        带重试的执行
        
        Args:
            arguments: 爬取参数
            
        Returns:
            CrawlResult: 爬取结果
        """
        last_error = None
        
        for attempt in range(self.max_retries + 1):
            try:
                start_time = time.time()
                
                # 执行爬取
                result = await self.crawler.execute(arguments)
                
                duration = time.time() - start_time
                
                if result.success:
                    return CrawlResult(
                        url=arguments.get("url", ""),
                        success=True,
                        data=result.result,
                        duration=duration,
                        timestamp=time.time()
                    )
                else:
                    # 爬取失败，但不是异常
                    return CrawlResult(
                        url=arguments.get("url", ""),
                        success=False,
                        data=result.result,
                        duration=duration,
                        timestamp=time.time(),
                        error="爬取任务失败"
                    )
                    
            except Exception as e:
                last_error = e
                self.stats["retries"] += 1
                
                if attempt < self.max_retries:
                    delay = self.retry_delay * (2 ** attempt)  # 指数退避
                    self.logger.warning(f"爬取失败，{delay}秒后重试 (尝试 {attempt + 1}/{self.max_retries + 1}): {e}")
                    await asyncio.sleep(delay)
                else:
                    self.logger.error(f"爬取最终失败: {e}")
        
        # 所有重试都失败
        return CrawlResult(
            url=arguments.get("url", ""),
            success=False,
            data=None,
            duration=0,
            timestamp=time.time(),
            error=str(last_error) if last_error else "未知错误"
        )
    
    async def batch_crawl(self, requests: List[Dict[str, Any]], max_concurrent: int = 3) -> List[CrawlResult]:
        """
        批量爬取
        
        Args:
            requests: 爬取请求列表
            max_concurrent: 最大并发数
            
        Returns:
            List[CrawlResult]: 爬取结果列表
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def crawl_with_semaphore(request):
            async with semaphore:
                return await self.execute_with_optimization(request)
        
        tasks = [crawl_with_semaphore(request) for request in requests]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(CrawlResult(
                    url=requests[i].get("url", ""),
                    success=False,
                    data=None,
                    duration=0,
                    timestamp=time.time(),
                    error=str(result)
                ))
            else:
                processed_results.append(result)
        
        return processed_results
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = self.stats.copy()
        
        if stats["total_requests"] > 0:
            stats["average_duration"] = stats["total_duration"] / stats["total_requests"]
            stats["cache_hit_rate"] = stats["cache_hits"] / stats["total_requests"]
            stats["error_rate"] = stats["errors"] / stats["total_requests"]
        else:
            stats["average_duration"] = 0
            stats["cache_hit_rate"] = 0
            stats["error_rate"] = 0
        
        if self.cache:
            stats["cache_stats"] = self.cache.stats()
        
        return stats
    
    async def cleanup(self):
        """清理资源"""
        if self.cache:
            self.cache.clear()
        await self.crawler.close()


async def demo_crawler_optimization():
    """演示爬虫优化功能"""
    print("⚡ 爬虫工具性能优化演示")
    print("=" * 50)
    
    # 创建基础爬虫工具
    base_crawler = CrawlerTool(
        service_url="http://localhost:8000",
        service_type=CrawlerServiceType.CRAWL4AI,
        timeout=30
    )
    
    # 创建优化的爬虫工具
    optimized_crawler = OptimizedCrawlerTool(
        crawler=base_crawler,
        enable_cache=True,
        cache_ttl=1800,  # 30分钟缓存
        max_retries=3,
        retry_delay=1.0,
        rate_limit=0.5  # 每0.5秒一个请求
    )
    
    # 测试请求
    test_requests = [
        {"url": "https://httpbin.org/html", "action": "crawl"},
        {"url": "https://httpbin.org/json", "action": "crawl"},
        {"url": "https://httpbin.org/xml", "action": "crawl"},
        {"url": "https://httpbin.org/html", "action": "crawl"},  # 重复请求，测试缓存
    ]
    
    print("🚀 执行批量爬取测试...")
    start_time = time.time()
    
    try:
        # 执行批量爬取
        results = await optimized_crawler.batch_crawl(test_requests, max_concurrent=2)
        
        total_time = time.time() - start_time
        
        print(f"\n📊 批量爬取结果:")
        print(f"  总请求数: {len(results)}")
        print(f"  成功数: {sum(1 for r in results if r.success)}")
        print(f"  失败数: {sum(1 for r in results if not r.success)}")
        print(f"  总耗时: {total_time:.2f}秒")
        
        # 显示性能统计
        stats = optimized_crawler.get_performance_stats()
        print(f"\n📈 性能统计:")
        print(f"  总请求数: {stats['total_requests']}")
        print(f"  缓存命中率: {stats['cache_hit_rate']:.2%}")
        print(f"  错误率: {stats['error_rate']:.2%}")
        print(f"  平均响应时间: {stats['average_duration']:.2f}秒")
        print(f"  重试次数: {stats['retries']}")
        
        if "cache_stats" in stats:
            cache_stats = stats["cache_stats"]
            print(f"  缓存大小: {cache_stats['size']}/{cache_stats['max_size']}")
        
        print(f"\n💡 优化特性:")
        print(f"  ✅ 智能缓存 - 避免重复请求")
        print(f"  ✅ 频率限制 - 保护目标服务器")
        print(f"  ✅ 自动重试 - 提高成功率")
        print(f"  ✅ 并发控制 - 优化性能")
        print(f"  ✅ 错误处理 - 增强稳定性")
        
    finally:
        # 清理资源
        await optimized_crawler.cleanup()


if __name__ == "__main__":
    asyncio.run(demo_crawler_optimization())
