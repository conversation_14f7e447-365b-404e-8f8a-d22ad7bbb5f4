#!/usr/bin/env python3
"""
结构化数据提取示例

展示如何使用爬虫工具进行复杂的结构化数据提取，
包括电商产品信息、新闻文章、社交媒体内容等。
"""

import asyncio
import json
from typing import Dict, Any, List
from ai_agent_framework.tools.crawler_tool import CrawlerTool, CrawlerServiceType


class StructuredDataExtractor:
    """结构化数据提取器"""
    
    def __init__(self, crawler_tool: CrawlerTool):
        """初始化提取器"""
        self.crawler = crawler_tool
    
    async def extract_ecommerce_product(self, product_url: str) -> Dict[str, Any]:
        """
        提取电商产品信息
        
        Args:
            product_url: 产品页面URL
            
        Returns:
            Dict[str, Any]: 提取的产品信息
        """
        # 定义产品信息提取模式
        extract_schema = {
            "product_name": {
                "selector": "h1.product-title, h1[data-testid='product-title'], .product-name h1",
                "attribute": "text",
                "required": True
            },
            "price": {
                "selector": ".price-current, .current-price, [data-testid='price']",
                "attribute": "text",
                "required": True,
                "transform": "extract_price"
            },
            "original_price": {
                "selector": ".price-original, .original-price, .was-price",
                "attribute": "text",
                "transform": "extract_price"
            },
            "description": {
                "selector": ".product-description, .product-details, [data-testid='description']",
                "attribute": "text"
            },
            "images": {
                "selector": ".product-images img, .gallery img",
                "attribute": "src",
                "multiple": True
            },
            "specifications": {
                "selector": ".specifications tr, .spec-table tr",
                "fields": {
                    "name": "td:first-child, .spec-name",
                    "value": "td:last-child, .spec-value"
                },
                "multiple": True
            },
            "reviews": {
                "selector": ".review-item, .review",
                "fields": {
                    "rating": ".rating, .stars",
                    "comment": ".review-text, .comment",
                    "author": ".reviewer-name, .author",
                    "date": ".review-date, .date"
                },
                "multiple": True,
                "limit": 10
            },
            "availability": {
                "selector": ".stock-status, .availability, [data-testid='stock']",
                "attribute": "text"
            },
            "brand": {
                "selector": ".brand, .manufacturer, [data-testid='brand']",
                "attribute": "text"
            },
            "category": {
                "selector": ".breadcrumb a, .category-path a",
                "attribute": "text",
                "multiple": True
            }
        }
        
        arguments = {
            "url": product_url,
            "action": "extract_data",
            "options": {
                "wait_time": 5,
                "enable_javascript": True,
                "extract_schema": extract_schema
            }
        }
        
        result = await self.crawler.execute(arguments)
        return self._process_extraction_result(result, "product")
    
    async def extract_news_article(self, article_url: str) -> Dict[str, Any]:
        """
        提取新闻文章信息
        
        Args:
            article_url: 文章URL
            
        Returns:
            Dict[str, Any]: 提取的文章信息
        """
        extract_schema = {
            "title": {
                "selector": "h1, .article-title, [data-testid='headline']",
                "attribute": "text",
                "required": True
            },
            "subtitle": {
                "selector": ".subtitle, .article-subtitle, .deck",
                "attribute": "text"
            },
            "author": {
                "selector": ".author, .byline, [data-testid='author']",
                "attribute": "text",
                "multiple": True
            },
            "publish_date": {
                "selector": ".publish-date, .date, time[datetime]",
                "attribute": "text,datetime",
                "transform": "parse_date"
            },
            "content": {
                "selector": ".article-content, .story-body, .post-content",
                "attribute": "text",
                "required": True
            },
            "summary": {
                "selector": ".summary, .excerpt, .lead",
                "attribute": "text"
            },
            "tags": {
                "selector": ".tags a, .categories a, .topic-tags a",
                "attribute": "text",
                "multiple": True
            },
            "images": {
                "selector": ".article-image img, .content img",
                "fields": {
                    "src": "@src",
                    "alt": "@alt",
                    "caption": "figcaption, .caption"
                },
                "multiple": True
            },
            "related_articles": {
                "selector": ".related-articles a, .more-stories a",
                "fields": {
                    "title": "text",
                    "url": "@href"
                },
                "multiple": True,
                "limit": 5
            }
        }
        
        arguments = {
            "url": article_url,
            "action": "extract_data",
            "options": {
                "wait_time": 3,
                "enable_javascript": True,
                "extract_schema": extract_schema
            }
        }
        
        result = await self.crawler.execute(arguments)
        return self._process_extraction_result(result, "article")
    
    async def extract_social_media_post(self, post_url: str) -> Dict[str, Any]:
        """
        提取社交媒体帖子信息
        
        Args:
            post_url: 帖子URL
            
        Returns:
            Dict[str, Any]: 提取的帖子信息
        """
        extract_schema = {
            "content": {
                "selector": ".post-content, .tweet-text, .post-text",
                "attribute": "text",
                "required": True
            },
            "author": {
                "selector": ".author-name, .username, .user-name",
                "attribute": "text",
                "required": True
            },
            "author_handle": {
                "selector": ".handle, .username, @[data-testid='username']",
                "attribute": "text"
            },
            "timestamp": {
                "selector": ".timestamp, .post-time, time",
                "attribute": "text,datetime",
                "transform": "parse_date"
            },
            "likes": {
                "selector": ".like-count, .likes, [data-testid='like']",
                "attribute": "text",
                "transform": "extract_number"
            },
            "shares": {
                "selector": ".share-count, .retweets, [data-testid='retweet']",
                "attribute": "text",
                "transform": "extract_number"
            },
            "comments": {
                "selector": ".comment-count, .replies, [data-testid='reply']",
                "attribute": "text",
                "transform": "extract_number"
            },
            "hashtags": {
                "selector": ".hashtag, a[href*='hashtag']",
                "attribute": "text",
                "multiple": True
            },
            "mentions": {
                "selector": ".mention, a[href*='@']",
                "attribute": "text",
                "multiple": True
            },
            "media": {
                "selector": ".media img, .post-image, .video",
                "fields": {
                    "type": "@data-type,tag",
                    "url": "@src,@data-src",
                    "alt": "@alt"
                },
                "multiple": True
            }
        }
        
        arguments = {
            "url": post_url,
            "action": "extract_data",
            "options": {
                "wait_time": 8,  # 社交媒体页面通常需要更长加载时间
                "enable_javascript": True,
                "extract_schema": extract_schema,
                "viewport": {"width": 1280, "height": 720}
            }
        }
        
        result = await self.crawler.execute(arguments)
        return self._process_extraction_result(result, "social_post")
    
    async def extract_job_listing(self, job_url: str) -> Dict[str, Any]:
        """
        提取招聘信息
        
        Args:
            job_url: 职位页面URL
            
        Returns:
            Dict[str, Any]: 提取的职位信息
        """
        extract_schema = {
            "title": {
                "selector": ".job-title, h1.title, [data-testid='job-title']",
                "attribute": "text",
                "required": True
            },
            "company": {
                "selector": ".company-name, .employer, [data-testid='company']",
                "attribute": "text",
                "required": True
            },
            "location": {
                "selector": ".location, .job-location, [data-testid='location']",
                "attribute": "text"
            },
            "salary": {
                "selector": ".salary, .compensation, [data-testid='salary']",
                "attribute": "text"
            },
            "job_type": {
                "selector": ".job-type, .employment-type",
                "attribute": "text"
            },
            "description": {
                "selector": ".job-description, .description, .job-details",
                "attribute": "text",
                "required": True
            },
            "requirements": {
                "selector": ".requirements li, .qualifications li",
                "attribute": "text",
                "multiple": True
            },
            "benefits": {
                "selector": ".benefits li, .perks li",
                "attribute": "text",
                "multiple": True
            },
            "posted_date": {
                "selector": ".posted-date, .job-date, time",
                "attribute": "text,datetime",
                "transform": "parse_date"
            },
            "application_deadline": {
                "selector": ".deadline, .expires",
                "attribute": "text",
                "transform": "parse_date"
            }
        }
        
        arguments = {
            "url": job_url,
            "action": "extract_data",
            "options": {
                "wait_time": 4,
                "enable_javascript": True,
                "extract_schema": extract_schema
            }
        }
        
        result = await self.crawler.execute(arguments)
        return self._process_extraction_result(result, "job")
    
    def _process_extraction_result(self, result: Any, data_type: str) -> Dict[str, Any]:
        """
        处理提取结果
        
        Args:
            result: 爬虫工具返回的结果
            data_type: 数据类型
            
        Returns:
            Dict[str, Any]: 处理后的结果
        """
        if not result.success:
            return {
                "success": False,
                "error": result.result,
                "data_type": data_type
            }
        
        extracted_data = result.result.get("data", {}).get("extracted_data", {})
        
        return {
            "success": True,
            "data_type": data_type,
            "extracted_data": extracted_data,
            "metadata": {
                "extraction_time": result.timestamp,
                "url": result.result.get("url"),
                "service_type": "crawler"
            }
        }


async def demo_structured_extraction():
    """演示结构化数据提取"""
    print("🔍 结构化数据提取演示")
    print("=" * 50)
    
    # 创建爬虫工具（注意：这里使用模拟的服务URL）
    crawler = CrawlerTool(
        service_url="http://localhost:8000",
        service_type=CrawlerServiceType.CRAWL4AI,
        timeout=60,
        enable_javascript=True
    )
    
    extractor = StructuredDataExtractor(crawler)
    
    # 演示不同类型的数据提取
    demo_urls = {
        "电商产品": "https://example-shop.com/product/123",
        "新闻文章": "https://example-news.com/article/456",
        "社交媒体": "https://example-social.com/post/789",
        "招聘信息": "https://example-jobs.com/job/101"
    }
    
    print("📋 支持的数据提取类型:")
    for data_type, url in demo_urls.items():
        print(f"  - {data_type}: {url}")
    
    print("\n💡 提取模式特点:")
    print("  - 支持多层级选择器")
    print("  - 支持属性和文本提取")
    print("  - 支持数据转换和验证")
    print("  - 支持数组和对象结构")
    print("  - 支持必需字段验证")
    
    print("\n🔧 使用示例:")
    print("```python")
    print("# 提取电商产品信息")
    print("result = await extractor.extract_ecommerce_product(product_url)")
    print("product_name = result['extracted_data']['product_name']")
    print("price = result['extracted_data']['price']")
    print("```")
    
    await crawler.close()


if __name__ == "__main__":
    asyncio.run(demo_structured_extraction())
