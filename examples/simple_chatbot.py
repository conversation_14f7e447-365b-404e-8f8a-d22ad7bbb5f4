#!/usr/bin/env python3
"""
AI Agent Framework 简单聊天机器人示例

这个示例展示了如何使用AI Agent Framework创建一个简单的聊天机器人。
包含了模型配置、工具注册、记忆管理等核心功能的使用。
"""

import asyncio
import os
from typing import Optional

from ai_agent_framework import Agent, FrameworkConfig
from ai_agent_framework.core.config import ModelConfig, LoggingConfig
from ai_agent_framework.models import OpenAIAdapter
from ai_agent_framework.memory import MemoryManager
from ai_agent_framework.tools import CalculatorTool, WeatherTool, SearchTool, CrawlerTool, CrawlerServiceType
from ai_agent_framework.utils.tool_registry import tool_registry
from ai_agent_framework.utils.logging_system import logging_system


class SimpleChatbot:
    """
    简单聊天机器人
    
    展示AI Agent Framework的基本使用方法。
    """
    
    def __init__(self):
        """初始化聊天机器人"""
        self.agent: Optional[Agent] = None
        self.setup_logging()
        self.setup_config()
        self.setup_tools()
    
    def setup_logging(self):
        """设置日志系统"""
        logging_config = LoggingConfig(
            level="INFO",
            structured=True,
            json_format=False
        )
        logging_system.setup(logging_config)
        self.logger = logging_system.get_logger("chatbot")
    
    def setup_config(self):
        """设置框架配置"""
        # 创建模型配置
        model_config = ModelConfig(
            name="gpt-4",
            provider="openai",
            model_id="gpt-4",
            api_key=os.getenv("OPENAI_API_KEY", "your-api-key-here"),
            max_tokens=4096,
            temperature=0.7
        )
        
        # 创建框架配置
        self.config = FrameworkConfig(
            models={"gpt-4": model_config},
            default_model="gpt-4",
            max_iterations=5,
            max_concurrent_tools=3
        )
        
        self.logger.info("框架配置已设置")
    
    def setup_tools(self):
        """注册工具"""
        # 注册示例工具
        calculator = CalculatorTool()
        weather = WeatherTool()
        search = SearchTool()

        # 注册爬虫工具（如果有本地爬虫服务）
        crawler = CrawlerTool(
            service_url=os.getenv("CRAWLER_SERVICE_URL", "http://localhost:8000"),
            service_type=CrawlerServiceType.CRAWL4AI,
            timeout=60,
            enable_javascript=True,
            enable_images=False
        )

        tool_registry.register_tool(calculator, max_concurrent=2)
        tool_registry.register_tool(weather, max_concurrent=1)
        tool_registry.register_tool(search, max_concurrent=1)
        tool_registry.register_tool(crawler, max_concurrent=1)

        self.logger.info("工具已注册（包括爬虫工具）")
    
    async def initialize(self):
        """初始化Agent"""
        try:
            # 创建模型适配器
            model = OpenAIAdapter(
                api_key=self.config.get_model_config().api_key,
                model_name=self.config.get_model_config().model_id,
                max_tokens=self.config.get_model_config().max_tokens,
                temperature=self.config.get_model_config().temperature
            )
            
            # 创建记忆管理器
            memory = MemoryManager(
                storage_path="chatbot_memory.db",
                short_term_ttl=1800,  # 30分钟
                working_ttl=7200,     # 2小时
                long_term_ttl=None    # 永久
            )
            
            # 创建Agent
            self.agent = Agent(
                agent_id="simple_chatbot",
                config=self.config,
                model=model,
                memory=memory,
                reasoning_mode="react"  # 使用ReAct推理模式
            )
            
            self.logger.info("聊天机器人已初始化")
            
        except Exception as e:
            self.logger.error(f"初始化失败: {str(e)}")
            raise
    
    async def chat_loop(self):
        """聊天循环"""
        print("🤖 AI Agent 聊天机器人已启动！")
        print("💡 我可以帮你进行计算、查询天气、搜索信息等。")
        print("📝 输入 'quit' 或 'exit' 退出聊天。")
        print("🔄 输入 'reset' 重置对话历史。")
        print("📊 输入 'stats' 查看统计信息。")
        print("-" * 50)
        
        while True:
            try:
                # 获取用户输入
                user_input = input("\n👤 你: ").strip()
                
                if not user_input:
                    continue
                
                # 处理特殊命令
                if user_input.lower() in ['quit', 'exit']:
                    print("👋 再见！")
                    break
                
                elif user_input.lower() == 'reset':
                    self.agent.reset()
                    print("🔄 对话历史已重置")
                    continue
                
                elif user_input.lower() == 'stats':
                    await self.show_stats()
                    continue
                
                # 发送消息给Agent
                print("🤖 AI: ", end="", flush=True)
                
                response = await self.agent.chat(user_input)
                print(response.content)
                
                # 显示工具调用信息
                if response.has_tool_calls():
                    print(f"🔧 使用了 {len(response.tool_calls)} 个工具")
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            
            except Exception as e:
                self.logger.error(f"聊天过程中出错: {str(e)}")
                print(f"❌ 出现错误: {str(e)}")
    
    async def show_stats(self):
        """显示统计信息"""
        try:
            # Agent状态
            agent_state = self.agent.get_state()
            print("\n📊 Agent状态:")
            print(f"  - ID: {agent_state['agent_id']}")
            print(f"  - 状态: {agent_state['state']}")
            print(f"  - 推理模式: {agent_state['reasoning_mode']}")
            print(f"  - 消息数量: {agent_state['message_count']}")
            print(f"  - 迭代次数: {agent_state['iteration_count']}")
            
            # 记忆统计
            memory_stats = await self.agent.get_memory_summary()
            if memory_stats:
                print("\n🧠 记忆统计:")
                print(f"  - 总记忆数: {memory_stats.get('total_memories', 0)}")
                print(f"  - 按类型分布: {memory_stats.get('by_type', {})}")
                print(f"  - 过期记忆: {memory_stats.get('expired_memories', 0)}")
            
            # 工具统计
            tool_stats = tool_registry.get_registry_stats()
            print("\n🔧 工具统计:")
            print(f"  - 已注册工具: {tool_stats['total_tools']}")
            print(f"  - 工具列表: {', '.join(tool_stats['tools'])}")
            print(f"  - 当前执行数: {tool_stats['total_executions']}")
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {str(e)}")
            print(f"❌ 获取统计信息失败: {str(e)}")
    
    async def cleanup(self):
        """清理资源"""
        if self.agent and self.agent.memory:
            await self.agent.memory.close()
        self.logger.info("资源已清理")


async def main():
    """主函数"""
    chatbot = SimpleChatbot()
    
    try:
        # 初始化聊天机器人
        await chatbot.initialize()
        
        # 开始聊天循环
        await chatbot.chat_loop()
        
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")
        print("💡 请检查配置和API密钥是否正确")
        
    finally:
        # 清理资源
        await chatbot.cleanup()


if __name__ == "__main__":
    # 运行聊天机器人
    asyncio.run(main())
