#!/usr/bin/env python3
"""
爬虫工具参数验证示例

展示如何设计和实现完善的参数验证逻辑，确保Agent传递的参数符合要求。
"""

import re
from typing import Dict, Any, List
from urllib.parse import urlparse
from ai_agent_framework.tools.crawler_tool import CrawlerTool


class AdvancedCrawlerParameterValidator:
    """高级爬虫参数验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.valid_actions = [
            "crawl", "extract_text", "extract_links", 
            "extract_images", "extract_data", "screenshot", "pdf_export"
        ]
        
        self.valid_formats = ["json", "html", "text", "markdown"]
        
        # 支持的CSS选择器模式
        self.css_selector_pattern = re.compile(
            r'^[a-zA-Z0-9\-_#.\[\]="\':\s,>+~*()]+$'
        )
    
    async def validate_comprehensive(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        全面的参数验证
        
        Returns:
            Dict[str, Any]: 验证结果，包含是否有效和错误信息
        """
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "normalized_args": arguments.copy()
        }
        
        # 1. 验证必需参数
        if not self._validate_required_params(arguments, validation_result):
            return validation_result
        
        # 2. 验证URL
        if not self._validate_url(arguments.get("url"), validation_result):
            return validation_result
        
        # 3. 验证操作类型
        if not self._validate_action(arguments.get("action", "crawl"), validation_result):
            return validation_result
        
        # 4. 验证选择器
        if "selectors" in arguments:
            self._validate_selectors(arguments["selectors"], validation_result)
        
        # 5. 验证选项配置
        if "options" in arguments:
            self._validate_options(arguments["options"], validation_result)
        
        # 6. 验证格式
        if "format" in arguments:
            self._validate_format(arguments["format"], validation_result)
        
        # 7. 参数标准化
        self._normalize_parameters(validation_result["normalized_args"])
        
        return validation_result
    
    def _validate_required_params(self, arguments: Dict[str, Any], result: Dict[str, Any]) -> bool:
        """验证必需参数"""
        if "url" not in arguments or not arguments["url"]:
            result["is_valid"] = False
            result["errors"].append("缺少必需参数: url")
            return False
        return True
    
    def _validate_url(self, url: str, result: Dict[str, Any]) -> bool:
        """验证URL格式和安全性"""
        try:
            parsed_url = urlparse(url)
            
            # 检查基本格式
            if not parsed_url.scheme or not parsed_url.netloc:
                result["is_valid"] = False
                result["errors"].append(f"无效的URL格式: {url}")
                return False
            
            # 检查协议
            if parsed_url.scheme not in ["http", "https"]:
                result["is_valid"] = False
                result["errors"].append(f"不支持的协议: {parsed_url.scheme}")
                return False
            
            # 安全检查 - 防止访问内网地址
            if self._is_private_ip(parsed_url.hostname):
                result["warnings"].append(f"检测到内网地址: {parsed_url.hostname}")
            
            return True
            
        except Exception as e:
            result["is_valid"] = False
            result["errors"].append(f"URL解析失败: {str(e)}")
            return False
    
    def _validate_action(self, action: str, result: Dict[str, Any]) -> bool:
        """验证操作类型"""
        if action not in self.valid_actions:
            result["is_valid"] = False
            result["errors"].append(
                f"无效的操作类型: {action}，支持的操作: {', '.join(self.valid_actions)}"
            )
            return False
        return True
    
    def _validate_selectors(self, selectors: Dict[str, Any], result: Dict[str, Any]):
        """验证CSS选择器"""
        for key, selector in selectors.items():
            if key == "custom":
                # 自定义选择器可能是复杂对象
                continue
            
            if isinstance(selector, str):
                if not self.css_selector_pattern.match(selector):
                    result["warnings"].append(f"可能无效的CSS选择器: {key}={selector}")
            else:
                result["warnings"].append(f"选择器应为字符串类型: {key}")
    
    def _validate_options(self, options: Dict[str, Any], result: Dict[str, Any]):
        """验证选项配置"""
        # 验证等待时间
        if "wait_time" in options:
            wait_time = options["wait_time"]
            if not isinstance(wait_time, int) or wait_time < 0 or wait_time > 300:
                result["warnings"].append("wait_time应为0-300秒之间的整数")
        
        # 验证视口大小
        if "viewport" in options:
            viewport = options["viewport"]
            if isinstance(viewport, dict):
                width = viewport.get("width", 1920)
                height = viewport.get("height", 1080)
                
                if not (100 <= width <= 4000) or not (100 <= height <= 4000):
                    result["warnings"].append("视口大小应在100-4000像素范围内")
            else:
                result["warnings"].append("viewport应为包含width和height的对象")
        
        # 验证请求头
        if "headers" in options:
            headers = options["headers"]
            if not isinstance(headers, dict):
                result["warnings"].append("headers应为对象类型")
            else:
                # 检查危险的请求头
                dangerous_headers = ["host", "content-length", "connection"]
                for header in headers.keys():
                    if header.lower() in dangerous_headers:
                        result["warnings"].append(f"不建议设置请求头: {header}")
        
        # 验证Cookie
        if "cookies" in options:
            cookies = options["cookies"]
            if isinstance(cookies, list):
                for i, cookie in enumerate(cookies):
                    if not isinstance(cookie, dict):
                        result["warnings"].append(f"Cookie[{i}]应为对象类型")
                    elif "name" not in cookie or "value" not in cookie:
                        result["warnings"].append(f"Cookie[{i}]缺少name或value字段")
            else:
                result["warnings"].append("cookies应为数组类型")
    
    def _validate_format(self, format_type: str, result: Dict[str, Any]):
        """验证输出格式"""
        if format_type not in self.valid_formats:
            result["warnings"].append(
                f"不支持的格式: {format_type}，支持的格式: {', '.join(self.valid_formats)}"
            )
    
    def _normalize_parameters(self, arguments: Dict[str, Any]):
        """参数标准化"""
        # 标准化URL
        if "url" in arguments:
            url = arguments["url"].strip()
            if not url.startswith(("http://", "https://")):
                url = "https://" + url
            arguments["url"] = url
        
        # 设置默认值
        if "action" not in arguments:
            arguments["action"] = "crawl"
        
        if "format" not in arguments:
            arguments["format"] = "json"
        
        # 标准化选项
        if "options" not in arguments:
            arguments["options"] = {}
        
        options = arguments["options"]
        
        # 设置默认等待时间
        if "wait_time" not in options:
            options["wait_time"] = 3
        
        # 设置默认视口
        if "viewport" not in options:
            options["viewport"] = {"width": 1920, "height": 1080}
    
    def _is_private_ip(self, hostname: str) -> bool:
        """检查是否为内网IP地址"""
        if not hostname:
            return False
        
        # 简单的内网地址检查
        private_patterns = [
            r'^127\.',           # 127.x.x.x
            r'^10\.',            # 10.x.x.x
            r'^172\.(1[6-9]|2[0-9]|3[01])\.',  # 172.16.x.x - 172.31.x.x
            r'^192\.168\.',      # 192.168.x.x
            r'^localhost$',      # localhost
        ]
        
        for pattern in private_patterns:
            if re.match(pattern, hostname):
                return True
        
        return False


async def demo_parameter_validation():
    """演示参数验证功能"""
    print("🔍 爬虫工具参数验证演示")
    print("=" * 50)
    
    validator = AdvancedCrawlerParameterValidator()
    
    # 测试用例
    test_cases = [
        {
            "name": "有效的基础参数",
            "args": {
                "url": "https://example.com",
                "action": "crawl",
                "format": "json"
            }
        },
        {
            "name": "缺少URL参数",
            "args": {
                "action": "crawl"
            }
        },
        {
            "name": "无效的URL格式",
            "args": {
                "url": "invalid-url",
                "action": "crawl"
            }
        },
        {
            "name": "无效的操作类型",
            "args": {
                "url": "https://example.com",
                "action": "invalid_action"
            }
        },
        {
            "name": "复杂的有效参数",
            "args": {
                "url": "https://news.example.com/article/123",
                "action": "extract_data",
                "selectors": {
                    "title": "h1.article-title",
                    "content": ".article-content",
                    "author": ".author-name"
                },
                "options": {
                    "wait_time": 5,
                    "enable_javascript": True,
                    "viewport": {"width": 1920, "height": 1080},
                    "headers": {"Accept": "text/html"}
                },
                "format": "json"
            }
        },
        {
            "name": "包含警告的参数",
            "args": {
                "url": "http://localhost:8080/test",
                "action": "crawl",
                "options": {
                    "wait_time": 500,  # 超出建议范围
                    "headers": {"Host": "example.com"}  # 危险请求头
                }
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_case['name']}")
        print("-" * 30)
        
        result = await validator.validate_comprehensive(test_case["args"])
        
        print(f"✅ 验证结果: {'通过' if result['is_valid'] else '失败'}")
        
        if result["errors"]:
            print("❌ 错误:")
            for error in result["errors"]:
                print(f"   - {error}")
        
        if result["warnings"]:
            print("⚠️  警告:")
            for warning in result["warnings"]:
                print(f"   - {warning}")
        
        if result["is_valid"]:
            print("🔧 标准化后的参数:")
            normalized = result["normalized_args"]
            print(f"   URL: {normalized.get('url')}")
            print(f"   Action: {normalized.get('action')}")
            print(f"   Format: {normalized.get('format')}")
            if "options" in normalized:
                options = normalized["options"]
                print(f"   Wait Time: {options.get('wait_time')}秒")
                if "viewport" in options:
                    vp = options["viewport"]
                    print(f"   Viewport: {vp.get('width')}x{vp.get('height')}")


if __name__ == "__main__":
    import asyncio
    asyncio.run(demo_parameter_validation())
