#!/usr/bin/env python3
"""
爬虫工具集成指南

详细展示如何将爬虫工具集成到AI Agent框架中，
包括注册、配置、权限控制和并发管理。
"""

import asyncio
import os
from typing import Dict, Any, Optional, Set
from ai_agent_framework import Agent, FrameworkConfig
from ai_agent_framework.core.config import ModelConfig
from ai_agent_framework.models import OpenAIAdapter
from ai_agent_framework.memory import MemoryManager
from ai_agent_framework.tools import CrawlerTool, CrawlerServiceType, CalculatorTool
from ai_agent_framework.utils.tool_registry import tool_registry
from ai_agent_framework.utils.logging_system import logging_system


class CrawlerIntegrationManager:
    """爬虫工具集成管理器"""
    
    def __init__(self):
        """初始化集成管理器"""
        self.logger = logging_system.get_logger("crawler_integration")
        self.registered_tools = {}
    
    def register_crawler_tools(self, configurations: Dict[str, Dict[str, Any]]):
        """
        注册多个爬虫工具配置
        
        Args:
            configurations: 爬虫工具配置字典
        """
        for tool_name, config in configurations.items():
            try:
                # 创建爬虫工具实例
                crawler = CrawlerTool(
                    service_url=config.get("service_url", "http://localhost:8000"),
                    service_type=CrawlerServiceType(config.get("service_type", "crawl4ai")),
                    timeout=config.get("timeout", 60),
                    max_response_size=config.get("max_response_size", 50 * 1024 * 1024),
                    user_agent=config.get("user_agent", "AI-Agent-Framework/1.0"),
                    enable_javascript=config.get("enable_javascript", True),
                    enable_images=config.get("enable_images", False),
                    custom_headers=config.get("custom_headers", {}),
                    proxy_config=config.get("proxy_config", {})
                )
                
                # 注册到工具注册表
                tool_registry.register_tool(
                    crawler,
                    permissions=config.get("permissions", set()),
                    max_concurrent=config.get("max_concurrent", 1)
                )
                
                self.registered_tools[tool_name] = crawler
                self.logger.info(f"爬虫工具已注册: {tool_name}")
                
            except Exception as e:
                self.logger.error(f"注册爬虫工具失败 {tool_name}: {e}")
    
    def create_specialized_crawlers(self):
        """创建专门用途的爬虫工具"""
        
        # 1. 新闻爬虫 - 优化用于新闻网站
        news_crawler_config = {
            "service_url": os.getenv("NEWS_CRAWLER_URL", "http://localhost:8001"),
            "service_type": "crawl4ai",
            "timeout": 30,
            "enable_javascript": True,
            "enable_images": False,
            "user_agent": "NewsBot/1.0",
            "custom_headers": {
                "Accept": "text/html,application/xhtml+xml",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"
            },
            "max_concurrent": 2,
            "permissions": {"news_access"}
        }
        
        # 2. 电商爬虫 - 优化用于电商网站
        ecommerce_crawler_config = {
            "service_url": os.getenv("ECOMMERCE_CRAWLER_URL", "http://localhost:8002"),
            "service_type": "selenium_grid",
            "timeout": 45,
            "enable_javascript": True,
            "enable_images": True,
            "user_agent": "ShoppingBot/1.0",
            "max_concurrent": 1,
            "permissions": {"ecommerce_access"}
        }
        
        # 3. 社交媒体爬虫 - 优化用于社交媒体
        social_crawler_config = {
            "service_url": os.getenv("SOCIAL_CRAWLER_URL", "http://localhost:8003"),
            "service_type": "scrapy_splash",
            "timeout": 60,
            "enable_javascript": True,
            "enable_images": False,
            "user_agent": "SocialBot/1.0",
            "max_concurrent": 1,
            "permissions": {"social_access"}
        }
        
        # 4. 通用爬虫 - 用于一般网站
        general_crawler_config = {
            "service_url": os.getenv("GENERAL_CRAWLER_URL", "http://localhost:8000"),
            "service_type": "crawl4ai",
            "timeout": 60,
            "enable_javascript": True,
            "enable_images": False,
            "user_agent": "GeneralBot/1.0",
            "max_concurrent": 3,
            "permissions": set()  # 无特殊权限要求
        }
        
        configurations = {
            "news_crawler": news_crawler_config,
            "ecommerce_crawler": ecommerce_crawler_config,
            "social_crawler": social_crawler_config,
            "general_crawler": general_crawler_config
        }
        
        self.register_crawler_tools(configurations)
    
    def setup_crawler_permissions(self):
        """设置爬虫工具权限"""
        
        # 定义权限组
        permission_groups = {
            "basic_user": {"general_crawler"},
            "news_analyst": {"general_crawler", "news_crawler"},
            "market_researcher": {"general_crawler", "ecommerce_crawler"},
            "social_monitor": {"general_crawler", "social_crawler"},
            "admin": {"general_crawler", "news_crawler", "ecommerce_crawler", "social_crawler"}
        }
        
        # 这里可以根据用户角色分配权限
        # 实际应用中，权限检查会在工具执行前进行
        self.logger.info("爬虫工具权限已配置")
        return permission_groups
    
    async def test_crawler_tools(self):
        """测试已注册的爬虫工具"""
        self.logger.info("开始测试爬虫工具...")
        
        for tool_name, crawler in self.registered_tools.items():
            try:
                # 测试基本参数验证
                test_args = {
                    "url": "https://httpbin.org/html",
                    "action": "crawl"
                }
                
                is_valid = await crawler.validate_arguments(test_args)
                status = "✅ 可用" if is_valid else "❌ 不可用"
                self.logger.info(f"工具测试 {tool_name}: {status}")
                
            except Exception as e:
                self.logger.error(f"工具测试失败 {tool_name}: {e}")
    
    async def cleanup_tools(self):
        """清理爬虫工具资源"""
        for tool_name, crawler in self.registered_tools.items():
            try:
                if hasattr(crawler, 'close'):
                    await crawler.close()
                self.logger.info(f"工具资源已清理: {tool_name}")
            except Exception as e:
                self.logger.error(f"清理工具资源失败 {tool_name}: {e}")


class SmartCrawlerAgent:
    """智能爬虫Agent"""
    
    def __init__(self):
        """初始化智能爬虫Agent"""
        self.integration_manager = CrawlerIntegrationManager()
        self.agent: Optional[Agent] = None
        self.logger = logging_system.get_logger("smart_crawler_agent")
    
    async def initialize(self):
        """初始化Agent和工具"""
        
        # 1. 注册专门的爬虫工具
        self.integration_manager.create_specialized_crawlers()
        
        # 2. 设置权限
        self.integration_manager.setup_crawler_permissions()
        
        # 3. 测试工具
        await self.integration_manager.test_crawler_tools()
        
        # 4. 创建模型配置
        model_config = ModelConfig(
            name="gpt-4",
            provider="openai",
            model_id="gpt-4",
            api_key=os.getenv("OPENAI_API_KEY", "your-api-key"),
            max_tokens=4096,
            temperature=0.7
        )
        
        # 5. 创建框架配置
        framework_config = FrameworkConfig(
            models={"gpt-4": model_config},
            default_model="gpt-4",
            max_iterations=10,
            max_concurrent_tools=3
        )
        
        # 6. 创建模型适配器
        model = OpenAIAdapter(
            api_key=model_config.api_key,
            model=model_config.model_id,
            max_tokens=model_config.max_tokens,
            temperature=model_config.temperature
        )
        
        # 7. 创建记忆管理器
        memory_manager = MemoryManager()
        await memory_manager.initialize()
        
        # 8. 创建Agent
        self.agent = Agent(
            model=model,
            memory_manager=memory_manager,
            config=framework_config
        )
        
        self.logger.info("智能爬虫Agent初始化完成")
    
    async def process_crawling_request(self, user_request: str) -> str:
        """
        处理爬虫请求
        
        Args:
            user_request: 用户请求
            
        Returns:
            str: 处理结果
        """
        if not self.agent:
            return "Agent未初始化"
        
        # 构建系统提示，指导Agent选择合适的爬虫工具
        system_prompt = """
你是一个专业的网页内容分析助手，拥有多种专门的爬虫工具：

1. **general_crawler** - 通用爬虫，适用于大多数网站
2. **news_crawler** - 新闻爬虫，优化用于新闻网站
3. **ecommerce_crawler** - 电商爬虫，优化用于购物网站
4. **social_crawler** - 社交媒体爬虫，优化用于社交平台

根据用户的请求和目标网站类型，智能选择最合适的爬虫工具。
对于复杂的数据提取任务，使用extract_data操作和自定义选择器。
对于简单的内容获取，使用crawl或extract_text操作。

请根据用户需求提供详细的分析和建议。
"""
        
        try:
            response = await self.agent.process_message(
                user_request,
                system_prompt=system_prompt
            )
            return response.content
        except Exception as e:
            self.logger.error(f"处理爬虫请求失败: {e}")
            return f"处理请求时发生错误: {str(e)}"
    
    async def cleanup(self):
        """清理资源"""
        await self.integration_manager.cleanup_tools()
        if self.agent and hasattr(self.agent, 'memory_manager'):
            await self.agent.memory_manager.close()


async def demo_crawler_integration():
    """演示爬虫工具集成"""
    print("🕷️ 爬虫工具集成演示")
    print("=" * 50)
    
    # 创建智能爬虫Agent
    smart_agent = SmartCrawlerAgent()
    
    try:
        # 初始化Agent和工具
        print("🔧 初始化智能爬虫Agent...")
        await smart_agent.initialize()
        
        # 演示请求
        demo_requests = [
            "帮我分析 https://news.example.com 的最新新闻",
            "提取 https://shop.example.com/product/123 的产品信息",
            "监控 https://social.example.com/trending 的热门话题",
            "抓取 https://example.com 的基本信息"
        ]
        
        print("\n📋 演示请求处理:")
        for i, request in enumerate(demo_requests, 1):
            print(f"\n{i}. {request}")
            print("   (注意: 这是演示，实际需要配置相应的爬虫服务)")
        
        print("\n💡 集成特点:")
        print("  - 支持多种专门的爬虫工具")
        print("  - 智能选择最适合的工具")
        print("  - 权限控制和并发管理")
        print("  - 资源自动清理")
        print("  - 错误处理和重试机制")
        
    finally:
        # 清理资源
        await smart_agent.cleanup()


if __name__ == "__main__":
    asyncio.run(demo_crawler_integration())
