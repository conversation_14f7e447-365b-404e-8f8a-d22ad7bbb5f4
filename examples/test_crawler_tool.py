#!/usr/bin/env python3
"""
爬虫工具基本功能测试

这个脚本测试爬虫工具的基本功能，不依赖外部爬虫服务。
主要用于验证工具的参数验证、请求构建等核心逻辑。
"""

import asyncio
import json
from ai_agent_framework.tools.crawler_tool import CrawlerTool, CrawlerServiceType


async def test_crawler_tool_basic():
    """测试爬虫工具基本功能"""
    print("🕷️ 爬虫工具基本功能测试")
    print("=" * 50)
    
    # 创建爬虫工具实例
    crawler = CrawlerTool(
        service_url="http://localhost:8000",
        service_type=CrawlerServiceType.CRAWL4AI,
        timeout=30,
        enable_javascript=True,
        enable_images=False
    )
    
    print(f"✅ 工具名称: {crawler.name}")
    print(f"✅ 工具描述: {crawler.description}")
    print()
    
    # 测试参数验证
    print("📋 测试参数验证:")
    
    # 有效参数
    valid_args = {
        "url": "https://example.com",
        "action": "crawl",
        "format": "json"
    }
    
    is_valid = await crawler.validate_arguments(valid_args)
    print(f"  有效参数验证: {'✅ 通过' if is_valid else '❌ 失败'}")
    
    # 无效URL
    invalid_args = {
        "url": "invalid-url",
        "action": "crawl"
    }
    
    is_valid = await crawler.validate_arguments(invalid_args)
    print(f"  无效URL验证: {'✅ 正确拒绝' if not is_valid else '❌ 错误接受'}")
    
    # 缺少URL
    missing_url_args = {
        "action": "crawl"
    }
    
    is_valid = await crawler.validate_arguments(missing_url_args)
    print(f"  缺少URL验证: {'✅ 正确拒绝' if not is_valid else '❌ 错误接受'}")
    
    # 无效操作
    invalid_action_args = {
        "url": "https://example.com",
        "action": "invalid_action"
    }
    
    is_valid = await crawler.validate_arguments(invalid_action_args)
    print(f"  无效操作验证: {'✅ 正确拒绝' if not is_valid else '❌ 错误接受'}")
    print()
    
    # 测试请求数据构建
    print("🔧 测试请求数据构建:")
    
    selectors = {
        "title": "h1",
        "content": ".main-content",
        "links": "a[href]"
    }
    
    options = {
        "wait_time": 5,
        "enable_javascript": True,
        "viewport": {"width": 1920, "height": 1080},
        "headers": {"User-Agent": "Test-Agent"}
    }
    
    # 测试Crawl4AI请求构建
    request_data = crawler._build_request_data(
        "https://example.com", "crawl", selectors, options, "json"
    )
    
    print("  Crawl4AI请求数据:")
    print(f"    URL: {request_data.get('url')}")
    print(f"    Action: {request_data.get('action')}")
    print(f"    Format: {request_data.get('format')}")
    print(f"    Wait Time: {request_data.get('wait_for')}")
    print(f"    Viewport: {request_data.get('viewport_width')}x{request_data.get('viewport_height')}")
    print()
    
    # 测试不同服务类型的请求构建
    print("🔄 测试不同服务类型:")
    
    # Scrapy Splash
    splash_crawler = CrawlerTool(
        service_type=CrawlerServiceType.SCRAPY_SPLASH
    )
    
    splash_request = splash_crawler._build_request_data(
        "https://example.com", "crawl", selectors, options, "json"
    )
    
    print("  Scrapy Splash请求数据:")
    print(f"    Wait: {splash_request.get('wait')}")
    print(f"    HTML: {splash_request.get('html')}")
    print(f"    Viewport: {splash_request.get('viewport')}")
    print()
    
    # Selenium Grid
    selenium_crawler = CrawlerTool(
        service_type=CrawlerServiceType.SELENIUM_GRID
    )
    
    selenium_request = selenium_crawler._build_request_data(
        "https://example.com", "crawl", selectors, options, "json"
    )
    
    print("  Selenium Grid请求数据:")
    print(f"    Browser: {selenium_request.get('browser')}")
    print(f"    Implicit Wait: {selenium_request.get('implicit_wait')}")
    print(f"    Window Size: {selenium_request.get('window_size')}")
    print()
    
    # 测试API端点获取
    print("🌐 测试API端点:")
    
    endpoints = ["crawl", "extract_text", "extract_links", "screenshot"]
    for action in endpoints:
        endpoint = crawler._get_api_endpoint(action)
        print(f"  {action}: {endpoint}")
    print()
    
    # 测试响应数据处理
    print("📊 测试响应数据处理:")
    
    # 测试爬取响应处理
    crawl_response = {
        "title": "测试页面",
        "content": "这是测试内容",
        "url": "https://example.com",
        "links": [{"url": "https://example.com/link1", "text": "链接1"}],
        "images": [{"url": "https://example.com/image1.jpg", "alt": "图片1"}]
    }
    
    processed_crawl = crawler._process_crawl_response(crawl_response, "json")
    print("  爬取响应处理:")
    print(f"    类型: {processed_crawl.get('type')}")
    print(f"    标题: {processed_crawl.get('title')}")
    print(f"    链接数量: {len(processed_crawl.get('links', []))}")
    print(f"    图片数量: {len(processed_crawl.get('images', []))}")
    print()
    
    # 测试文本提取响应处理
    text_response = {
        "text": "这是提取的文本内容，包含多个单词。"
    }
    
    processed_text = crawler._process_text_extraction(text_response)
    print("  文本提取响应处理:")
    print(f"    类型: {processed_text.get('type')}")
    print(f"    字符数: {processed_text.get('char_count')}")
    print(f"    单词数: {processed_text.get('word_count')}")
    print()
    
    # 测试链接提取响应处理
    links_response = {
        "links": [
            {"url": "https://example.com/page1", "text": "页面1", "type": "internal"},
            {"url": "https://external.com", "text": "外部链接", "type": "external"}
        ]
    }
    
    processed_links = crawler._process_links_extraction(links_response)
    print("  链接提取响应处理:")
    print(f"    类型: {processed_links.get('type')}")
    print(f"    链接数量: {processed_links.get('count')}")
    print()
    
    # 测试结构化数据提取响应处理
    data_response = {
        "extracted": {
            "product_name": "测试产品",
            "price": "¥99.99",
            "description": "这是一个测试产品",
            "reviews": [
                {"rating": "5星", "comment": "很好的产品！"},
                {"rating": "4星", "comment": "物有所值"}
            ]
        }
    }
    
    processed_data = crawler._process_data_extraction(data_response)
    print("  结构化数据提取响应处理:")
    print(f"    类型: {processed_data.get('type')}")
    print(f"    产品名称: {processed_data.get('extracted_data', {}).get('product_name')}")
    print(f"    价格: {processed_data.get('extracted_data', {}).get('price')}")
    print(f"    评论数量: {len(processed_data.get('extracted_data', {}).get('reviews', []))}")
    print()
    
    # 清理资源
    await crawler.close()
    
    print("✅ 爬虫工具基本功能测试完成！")
    print()
    print("💡 提示:")
    print("  - 要使用实际的爬虫功能，请先部署相应的爬虫服务")
    print("  - 参考文档: docs/crawler-tool-guide.md")
    print("  - 运行完整示例: python examples/crawler_agent_example.py")


async def main():
    """主函数"""
    try:
        await test_crawler_tool_basic()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
