#!/usr/bin/env python3
"""
爬虫Agent集成示例

演示如何将CrawlerTool集成到AI Agent中，实现智能网页抓取和内容分析。
Agent可以根据用户需求自动选择合适的爬取策略和数据提取方法。
"""

import asyncio
import os
from typing import Optional

from ai_agent_framework import Agent, FrameworkConfig
from ai_agent_framework.core.config import ModelConfig, LoggingConfig
from ai_agent_framework.models import OpenAIAdapter
from ai_agent_framework.memory import MemoryManager
from ai_agent_framework.tools import CrawlerTool, CrawlerServiceType, CalculatorTool
from ai_agent_framework.utils.tool_registry import tool_registry
from ai_agent_framework.utils.logging_system import logging_system


class CrawlerAgent:
    """
    爬虫Agent示例类
    
    集成了爬虫工具的智能Agent，可以根据用户需求自动抓取和分析网页内容。
    """
    
    def __init__(self):
        """初始化爬虫Agent"""
        self.config: Optional[FrameworkConfig] = None
        self.agent: Optional[Agent] = None
        self.memory_manager: Optional[MemoryManager] = None
        self.logger = logging_system.get_logger("crawler_agent")
        
        # 设置日志配置
        logging_config = LoggingConfig(
            level="INFO",
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        logging_system.configure(logging_config)
    
    def setup_config(self):
        """设置框架配置"""
        # 创建模型配置
        model_config = ModelConfig(
            name="gpt-4",
            provider="openai",
            model_id="gpt-4",
            api_key=os.getenv("OPENAI_API_KEY", "your-api-key-here"),
            max_tokens=4096,
            temperature=0.7
        )
        
        # 创建框架配置
        self.config = FrameworkConfig(
            models={"gpt-4": model_config},
            default_model="gpt-4",
            max_iterations=10,  # 增加迭代次数以支持复杂的爬取任务
            max_concurrent_tools=2
        )
        
        self.logger.info("爬虫Agent配置已设置")
    
    def setup_tools(self):
        """注册工具"""
        # 注册爬虫工具
        crawler = CrawlerTool(
            service_url=os.getenv("CRAWLER_SERVICE_URL", "http://localhost:8000"),
            service_type=CrawlerServiceType.CRAWL4AI,
            timeout=120,  # 增加超时时间
            enable_javascript=True,
            enable_images=False,
            user_agent="AI-Agent-Crawler/1.0"
        )
        
        # 注册计算器工具（用于数据分析）
        calculator = CalculatorTool()
        
        tool_registry.register_tool(crawler, max_concurrent=1)
        tool_registry.register_tool(calculator, max_concurrent=2)
        
        self.logger.info("爬虫工具已注册")
    
    async def initialize(self):
        """初始化Agent"""
        try:
            # 创建模型适配器
            model = OpenAIAdapter(
                api_key=self.config.models["gpt-4"].api_key,
                model=self.config.models["gpt-4"].model_id,
                max_tokens=self.config.models["gpt-4"].max_tokens,
                temperature=self.config.models["gpt-4"].temperature
            )
            
            # 创建记忆管理器
            self.memory_manager = MemoryManager()
            await self.memory_manager.initialize()
            
            # 创建Agent
            self.agent = Agent(
                model=model,
                memory_manager=self.memory_manager,
                config=self.config
            )
            
            self.logger.info("爬虫Agent初始化完成")
            
        except Exception as e:
            self.logger.error(f"Agent初始化失败: {e}")
            raise
    
    async def crawl_and_analyze(self, user_input: str) -> str:
        """
        爬取和分析网页内容
        
        Args:
            user_input: 用户输入的需求描述
            
        Returns:
            str: Agent的分析结果
        """
        try:
            # 构建系统提示，指导Agent使用爬虫工具
            system_prompt = """
你是一个专业的网页内容分析助手，具备强大的网页抓取和数据分析能力。

你可以使用以下工具：
1. web_crawler - 爬虫工具，支持多种操作：
   - crawl: 基础网页抓取
   - extract_text: 提取文本内容
   - extract_links: 提取链接
   - extract_images: 提取图片
   - extract_data: 结构化数据提取
   - screenshot: 网页截图
   - pdf_export: 导出PDF

2. calculator - 计算器工具，用于数据分析和统计

使用爬虫工具时，请根据用户需求选择合适的操作类型和参数配置。
对于复杂的数据提取，可以使用extract_schema参数定义结构化提取规则。

请根据用户的具体需求，智能地选择工具和参数，并提供详细的分析结果。
"""
            
            # 发送消息给Agent
            response = await self.agent.process_message(
                user_input,
                system_prompt=system_prompt
            )
            
            return response.content
            
        except Exception as e:
            self.logger.error(f"处理用户请求失败: {e}")
            return f"处理请求时发生错误: {str(e)}"
    
    async def run_interactive_session(self):
        """运行交互式会话"""
        self.logger.info("启动爬虫Agent交互式会话")
        print("🕷️ 爬虫Agent已启动！")
        print("你可以要求我抓取和分析任何网页内容。")
        print("例如：")
        print("- 抓取 https://example.com 的内容")
        print("- 提取 https://news.site.com 的新闻标题和摘要")
        print("- 分析 https://shop.com/product 的产品信息")
        print("- 截图 https://dashboard.com 的页面")
        print("输入 'quit' 退出\n")
        
        while True:
            try:
                user_input = input("👤 用户: ").strip()
                
                if user_input.lower() in ['quit', 'exit', '退出']:
                    print("👋 再见！")
                    break
                
                if not user_input:
                    continue
                
                print("🤖 Agent正在处理...")
                response = await self.crawl_and_analyze(user_input)
                print(f"🤖 Agent: {response}\n")
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}\n")
    
    async def run_demo_tasks(self):
        """运行演示任务"""
        self.logger.info("运行爬虫Agent演示任务")
        
        demo_tasks = [
            "抓取 https://httpbin.org/html 的内容并分析页面结构",
            "提取 https://httpbin.org/links/5/0 页面中的所有链接",
            "分析 https://httpbin.org/json 返回的JSON数据结构",
        ]
        
        for i, task in enumerate(demo_tasks, 1):
            print(f"\n📋 演示任务 {i}: {task}")
            print("🤖 Agent正在处理...")
            
            try:
                response = await self.crawl_and_analyze(task)
                print(f"🤖 Agent: {response}")
            except Exception as e:
                print(f"❌ 任务执行失败: {e}")
            
            # 任务间暂停
            await asyncio.sleep(2)
    
    async def cleanup(self):
        """清理资源"""
        self.logger.info("清理Agent资源...")
        
        if self.memory_manager:
            await self.memory_manager.close()
        
        # 清理工具注册表中的爬虫工具
        crawler_tool = tool_registry.get_tool("web_crawler")
        if crawler_tool and hasattr(crawler_tool, 'close'):
            await crawler_tool.close()
        
        self.logger.info("资源清理完成")


async def main():
    """主函数"""
    agent = CrawlerAgent()
    
    try:
        # 设置配置
        agent.setup_config()
        
        # 注册工具
        agent.setup_tools()
        
        # 初始化Agent
        await agent.initialize()
        
        # 检查命令行参数
        import sys
        if len(sys.argv) > 1 and sys.argv[1] == "--demo":
            # 运行演示任务
            await agent.run_demo_tasks()
        else:
            # 运行交互式会话
            await agent.run_interactive_session()
    
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        agent.logger.error(f"启动失败: {e}")
    
    finally:
        # 清理资源
        await agent.cleanup()


if __name__ == "__main__":
    # 运行爬虫Agent
    asyncio.run(main())
