#!/usr/bin/env python3
"""
爬虫工具使用示例

演示如何使用CrawlerTool调用本地部署的爬虫服务来抓取和解析网页内容。
包含基础爬取、内容提取、结构化数据解析等功能的使用示例。
"""

import asyncio
import json
import os
from typing import Dict, Any

from ai_agent_framework.tools.crawler_tool import CrawlerTool, CrawlerServiceType
from ai_agent_framework.utils.logging_system import logging_system


class CrawlerToolExample:
    """爬虫工具使用示例类"""
    
    def __init__(self):
        """初始化示例"""
        self.logger = logging_system.get_logger("crawler_example")
        
        # 初始化不同类型的爬虫工具
        self.crawl4ai_tool = CrawlerTool(
            service_url="http://localhost:8000",
            service_type=CrawlerServiceType.CRAWL4AI,
            timeout=60,
            enable_javascript=True,
            enable_images=False,
            user_agent="AI-Agent-Framework-Example/1.0"
        )
        
        self.splash_tool = CrawlerTool(
            service_url="http://localhost:8050",
            service_type=CrawlerServiceType.SCRAPY_SPLASH,
            timeout=30
        )
        
        self.selenium_tool = CrawlerTool(
            service_url="http://localhost:4444",
            service_type=CrawlerServiceType.SELENIUM_GRID,
            timeout=45
        )
    
    async def basic_crawl_example(self):
        """基础网页抓取示例"""
        self.logger.info("=== 基础网页抓取示例 ===")
        
        arguments = {
            "url": "https://httpbin.org/html",
            "action": "crawl",
            "format": "json",
            "options": {
                "wait_time": 3,
                "enable_javascript": False
            }
        }
        
        try:
            result = await self.crawl4ai_tool.execute(arguments)
            if result.success:
                self.logger.info("基础爬取成功")
                self._print_result(result.result)
            else:
                self.logger.error(f"基础爬取失败: {result.result}")
        except Exception as e:
            self.logger.error(f"基础爬取异常: {e}")
    
    async def text_extraction_example(self):
        """文本内容提取示例"""
        self.logger.info("=== 文本内容提取示例 ===")
        
        arguments = {
            "url": "https://httpbin.org/html",
            "action": "extract_text",
            "selectors": {
                "title": "title",
                "content": "body",
                "headings": "h1, h2, h3"
            },
            "options": {
                "wait_time": 2,
                "enable_javascript": False
            }
        }
        
        try:
            result = await self.crawl4ai_tool.execute(arguments)
            if result.success:
                self.logger.info("文本提取成功")
                self._print_result(result.result)
            else:
                self.logger.error(f"文本提取失败: {result.result}")
        except Exception as e:
            self.logger.error(f"文本提取异常: {e}")
    
    async def links_extraction_example(self):
        """链接提取示例"""
        self.logger.info("=== 链接提取示例 ===")
        
        arguments = {
            "url": "https://httpbin.org/links/10/0",
            "action": "extract_links",
            "selectors": {
                "links": "a[href]"
            },
            "options": {
                "wait_time": 2
            }
        }
        
        try:
            result = await self.crawl4ai_tool.execute(arguments)
            if result.success:
                self.logger.info("链接提取成功")
                self._print_result(result.result)
            else:
                self.logger.error(f"链接提取失败: {result.result}")
        except Exception as e:
            self.logger.error(f"链接提取异常: {e}")
    
    async def structured_data_extraction_example(self):
        """结构化数据提取示例"""
        self.logger.info("=== 结构化数据提取示例 ===")
        
        arguments = {
            "url": "https://httpbin.org/html",
            "action": "extract_data",
            "options": {
                "wait_time": 3,
                "extract_schema": {
                    "page_title": "title",
                    "main_heading": "h1",
                    "paragraphs": {
                        "selector": "p",
                        "multiple": True
                    },
                    "links": {
                        "selector": "a",
                        "fields": {
                            "text": "text()",
                            "url": "@href"
                        },
                        "multiple": True
                    }
                }
            }
        }
        
        try:
            result = await self.crawl4ai_tool.execute(arguments)
            if result.success:
                self.logger.info("结构化数据提取成功")
                self._print_result(result.result)
            else:
                self.logger.error(f"结构化数据提取失败: {result.result}")
        except Exception as e:
            self.logger.error(f"结构化数据提取异常: {e}")
    
    async def javascript_rendering_example(self):
        """JavaScript渲染示例"""
        self.logger.info("=== JavaScript渲染示例 ===")
        
        arguments = {
            "url": "https://httpbin.org/delay/2",
            "action": "crawl",
            "options": {
                "wait_time": 5,
                "enable_javascript": True,
                "viewport": {
                    "width": 1920,
                    "height": 1080
                }
            }
        }
        
        try:
            result = await self.crawl4ai_tool.execute(arguments)
            if result.success:
                self.logger.info("JavaScript渲染成功")
                self._print_result(result.result)
            else:
                self.logger.error(f"JavaScript渲染失败: {result.result}")
        except Exception as e:
            self.logger.error(f"JavaScript渲染异常: {e}")
    
    async def custom_headers_example(self):
        """自定义请求头示例"""
        self.logger.info("=== 自定义请求头示例 ===")
        
        arguments = {
            "url": "https://httpbin.org/headers",
            "action": "crawl",
            "options": {
                "headers": {
                    "Authorization": "Bearer test-token",
                    "Accept": "application/json",
                    "X-Custom-Header": "CustomValue"
                },
                "wait_time": 2
            }
        }
        
        try:
            result = await self.crawl4ai_tool.execute(arguments)
            if result.success:
                self.logger.info("自定义请求头成功")
                self._print_result(result.result)
            else:
                self.logger.error(f"自定义请求头失败: {result.result}")
        except Exception as e:
            self.logger.error(f"自定义请求头异常: {e}")
    
    async def error_handling_example(self):
        """错误处理示例"""
        self.logger.info("=== 错误处理示例 ===")
        
        # 测试无效URL
        arguments = {
            "url": "https://invalid-domain-that-does-not-exist.com",
            "action": "crawl"
        }
        
        try:
            result = await self.crawl4ai_tool.execute(arguments)
            if result.success:
                self.logger.info("意外成功")
                self._print_result(result.result)
            else:
                self.logger.info(f"预期的错误处理: {result.result}")
        except Exception as e:
            self.logger.info(f"预期的异常处理: {e}")
    
    async def performance_test_example(self):
        """性能测试示例"""
        self.logger.info("=== 性能测试示例 ===")
        
        urls = [
            "https://httpbin.org/delay/1",
            "https://httpbin.org/delay/2",
            "https://httpbin.org/delay/3"
        ]
        
        tasks = []
        for i, url in enumerate(urls):
            arguments = {
                "url": url,
                "action": "crawl",
                "options": {"wait_time": 1}
            }
            task = self._crawl_with_timing(f"Task-{i+1}", arguments)
            tasks.append(task)
        
        # 并发执行
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"Task-{i+1} 异常: {result}")
            else:
                self.logger.info(f"Task-{i+1} 完成: {result}")
    
    async def _crawl_with_timing(self, task_name: str, arguments: Dict[str, Any]) -> str:
        """带计时的爬取任务"""
        import time
        start_time = time.time()
        
        try:
            result = await self.crawl4ai_tool.execute(arguments)
            end_time = time.time()
            duration = end_time - start_time
            
            if result.success:
                return f"{task_name} 成功 (耗时: {duration:.2f}秒)"
            else:
                return f"{task_name} 失败 (耗时: {duration:.2f}秒): {result.result}"
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            return f"{task_name} 异常 (耗时: {duration:.2f}秒): {e}"
    
    def _print_result(self, result: Any):
        """打印结果（格式化输出）"""
        if isinstance(result, dict):
            print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            print(result)
    
    async def cleanup(self):
        """清理资源"""
        self.logger.info("清理资源...")
        await self.crawl4ai_tool.close()
        await self.splash_tool.close()
        await self.selenium_tool.close()
        self.logger.info("资源清理完成")


async def main():
    """主函数"""
    example = CrawlerToolExample()
    
    try:
        # 运行各种示例
        await example.basic_crawl_example()
        await asyncio.sleep(1)
        
        await example.text_extraction_example()
        await asyncio.sleep(1)
        
        await example.links_extraction_example()
        await asyncio.sleep(1)
        
        await example.structured_data_extraction_example()
        await asyncio.sleep(1)
        
        await example.javascript_rendering_example()
        await asyncio.sleep(1)
        
        await example.custom_headers_example()
        await asyncio.sleep(1)
        
        await example.error_handling_example()
        await asyncio.sleep(1)
        
        await example.performance_test_example()
        
    finally:
        # 清理资源
        await example.cleanup()


if __name__ == "__main__":
    # 设置日志级别
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行示例
    asyncio.run(main())
