# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试和覆盖率
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/

# 数据库
*.db
*.sqlite
*.sqlite3

# 配置文件（包含敏感信息）
config.local.yaml
config.local.json
.secrets/
secrets/

# 临时文件
tmp/
temp/
.tmp/

# 文档构建
docs/_build/
docs/site/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath
*.sage.py

# 环境变量文件
.env.local
.env.development
.env.test
.env.production

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# 项目特定
# AI模型文件
models/
*.model
*.pkl
*.joblib

# 数据文件
data/
datasets/
*.csv
*.json
*.parquet

# 缓存目录
.cache/
cache/

# 监控和指标
metrics/
prometheus/
