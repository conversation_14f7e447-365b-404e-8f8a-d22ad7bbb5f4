"""
爬虫工具单元测试

测试CrawlerTool的各种功能，包括参数验证、请求构建、响应处理等。
使用Mock来模拟HTTP请求，避免依赖外部服务。
"""

import asyncio
import json
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

import pytest

from ai_agent_framework.tools.crawler_tool import CrawlerTool, CrawlerServiceType
from ai_agent_framework.core.messages import ToolResult


class TestCrawlerTool:
    """爬虫工具测试类"""
    
    @pytest.fixture
    def crawler_tool(self):
        """创建爬虫工具实例"""
        return CrawlerTool(
            service_url="http://localhost:8000",
            service_type=CrawlerServiceType.CRAWL4AI,
            timeout=30,
            enable_javascript=True,
            enable_images=False
        )
    
    @pytest.fixture
    def mock_session(self):
        """创建Mock HTTP会话"""
        session = AsyncMock()
        response = AsyncMock()
        response.status = 200
        response.headers = {"content-type": "application/json"}
        response.json = AsyncMock(return_value={
            "success": True,
            "url": "https://example.com",
            "title": "Example Domain",
            "content": "This domain is for use in illustrative examples.",
            "links": [],
            "images": []
        })

        # 正确设置异步上下文管理器
        context_manager = AsyncMock()
        context_manager.__aenter__ = AsyncMock(return_value=response)
        context_manager.__aexit__ = AsyncMock(return_value=None)
        session.post.return_value = context_manager

        return session
    
    def test_tool_properties(self, crawler_tool):
        """测试工具基本属性"""
        assert crawler_tool.name == "web_crawler"
        assert "爬虫服务调用工具" in crawler_tool.description
        
        schema = crawler_tool.parameters_schema
        assert schema["type"] == "object"
        assert "url" in schema["properties"]
        assert "action" in schema["properties"]
        assert "url" in schema["required"]
    
    @pytest.mark.asyncio
    async def test_validate_arguments_valid_url(self, crawler_tool):
        """测试有效URL参数验证"""
        arguments = {
            "url": "https://example.com",
            "action": "crawl"
        }
        
        result = await crawler_tool.validate_arguments(arguments)
        assert result is True
    
    @pytest.mark.asyncio
    async def test_validate_arguments_invalid_url(self, crawler_tool):
        """测试无效URL参数验证"""
        arguments = {
            "url": "invalid-url",
            "action": "crawl"
        }
        
        result = await crawler_tool.validate_arguments(arguments)
        assert result is False
    
    @pytest.mark.asyncio
    async def test_validate_arguments_missing_url(self, crawler_tool):
        """测试缺少URL参数验证"""
        arguments = {
            "action": "crawl"
        }
        
        result = await crawler_tool.validate_arguments(arguments)
        assert result is False
    
    @pytest.mark.asyncio
    async def test_validate_arguments_invalid_action(self, crawler_tool):
        """测试无效操作类型参数验证"""
        arguments = {
            "url": "https://example.com",
            "action": "invalid_action"
        }
        
        result = await crawler_tool.validate_arguments(arguments)
        assert result is False
    
    def test_build_crawl4ai_request(self, crawler_tool):
        """测试构建Crawl4AI请求数据"""
        selectors = {
            "title": "h1",
            "content": ".content"
        }
        options = {
            "wait_time": 5,
            "enable_javascript": True,
            "viewport": {"width": 1920, "height": 1080}
        }
        
        request_data = crawler_tool._build_request_data(
            "https://example.com", "crawl", selectors, options, "json"
        )
        
        assert request_data["url"] == "https://example.com"
        assert request_data["action"] == "crawl"
        assert request_data["format"] == "json"
        assert request_data["wait_for"] == 5
        assert request_data["viewport_width"] == 1920
        assert request_data["viewport_height"] == 1080
    
    def test_build_scrapy_splash_request(self):
        """测试构建Scrapy Splash请求数据"""
        crawler_tool = CrawlerTool(
            service_type=CrawlerServiceType.SCRAPY_SPLASH
        )
        
        selectors = {"content": ".content"}
        options = {
            "wait_time": 3,
            "enable_javascript": True,
            "viewport": {"width": 1280, "height": 720}
        }
        
        request_data = crawler_tool._build_request_data(
            "https://example.com", "crawl", selectors, options, "json"
        )
        
        assert request_data["url"] == "https://example.com"
        assert request_data["wait"] == 3
        assert request_data["html"] == 1
        assert request_data["viewport"] == "1280x720"
    
    def test_build_selenium_grid_request(self):
        """测试构建Selenium Grid请求数据"""
        crawler_tool = CrawlerTool(
            service_type=CrawlerServiceType.SELENIUM_GRID
        )
        
        selectors = {"content": ".content"}
        options = {
            "wait_time": 4,
            "enable_javascript": False,
            "enable_images": False
        }
        
        request_data = crawler_tool._build_request_data(
            "https://example.com", "crawl", selectors, options, "json"
        )
        
        assert request_data["url"] == "https://example.com"
        assert request_data["browser"] == "chrome"
        assert request_data["implicit_wait"] == 4
        assert "--disable-javascript" in request_data["chrome_options"]
        assert "--blink-settings=imagesEnabled=false" in request_data["chrome_options"]
    
    def test_get_api_endpoint_crawl4ai(self, crawler_tool):
        """测试Crawl4AI API端点获取"""
        assert crawler_tool._get_api_endpoint("crawl") == "/crawl"
        assert crawler_tool._get_api_endpoint("extract_text") == "/extract/text"
        assert crawler_tool._get_api_endpoint("extract_links") == "/extract/links"
        assert crawler_tool._get_api_endpoint("screenshot") == "/screenshot"
    
    def test_get_api_endpoint_scrapy_splash(self):
        """测试Scrapy Splash API端点获取"""
        crawler_tool = CrawlerTool(
            service_type=CrawlerServiceType.SCRAPY_SPLASH
        )
        
        assert crawler_tool._get_api_endpoint("crawl") == "/render.json"
        assert crawler_tool._get_api_endpoint("extract_data") == "/execute"
        assert crawler_tool._get_api_endpoint("screenshot") == "/render.png"
    
    def test_process_crawl_response(self, crawler_tool):
        """测试处理爬取响应数据"""
        response_data = {
            "title": "Example Domain",
            "content": "This is example content",
            "url": "https://example.com",
            "links": [{"url": "https://example.com/link1", "text": "Link 1"}],
            "images": [{"url": "https://example.com/image1.jpg", "alt": "Image 1"}]
        }
        
        result = crawler_tool._process_crawl_response(response_data, "json")
        
        assert result["type"] == "crawl"
        assert result["format"] == "json"
        assert result["title"] == "Example Domain"
        assert result["content"] == "This is example content"
        assert result["url"] == "https://example.com"
        assert len(result["links"]) == 1
        assert len(result["images"]) == 1
    
    def test_process_text_extraction(self, crawler_tool):
        """测试处理文本提取响应"""
        response_data = {
            "text": "This is extracted text content with multiple words."
        }
        
        result = crawler_tool._process_text_extraction(response_data)
        
        assert result["type"] == "text_extraction"
        assert result["text"] == "This is extracted text content with multiple words."
        assert result["word_count"] == 8
        assert result["char_count"] == 51
    
    def test_process_links_extraction(self, crawler_tool):
        """测试处理链接提取响应"""
        response_data = {
            "links": [
                {"url": "https://example.com/page1", "text": "Page 1", "type": "internal"},
                {"url": "https://external.com", "text": "External Link", "type": "external"}
            ]
        }
        
        result = crawler_tool._process_links_extraction(response_data)
        
        assert result["type"] == "links_extraction"
        assert result["count"] == 2
        assert len(result["links"]) == 2
        assert result["links"][0]["url"] == "https://example.com/page1"
        assert result["links"][0]["text"] == "Page 1"
        assert result["links"][0]["type"] == "internal"
    
    def test_process_images_extraction(self, crawler_tool):
        """测试处理图片提取响应"""
        response_data = {
            "images": [
                {
                    "url": "https://example.com/image1.jpg",
                    "alt": "Image 1",
                    "title": "First Image",
                    "width": 800,
                    "height": 600
                },
                {
                    "url": "https://example.com/image2.png",
                    "alt": "Image 2"
                }
            ]
        }
        
        result = crawler_tool._process_images_extraction(response_data)
        
        assert result["type"] == "images_extraction"
        assert result["count"] == 2
        assert len(result["images"]) == 2
        assert result["images"][0]["url"] == "https://example.com/image1.jpg"
        assert result["images"][0]["alt"] == "Image 1"
        assert result["images"][0]["width"] == 800
        assert result["images"][0]["height"] == 600
    
    def test_process_data_extraction(self, crawler_tool):
        """测试处理结构化数据提取响应"""
        response_data = {
            "extracted": {
                "product_name": "Test Product",
                "price": "$99.99",
                "reviews": [
                    {"rating": "5 stars", "comment": "Great product!"},
                    {"rating": "4 stars", "comment": "Good value"}
                ]
            }
        }
        
        result = crawler_tool._process_data_extraction(response_data)
        
        assert result["type"] == "data_extraction"
        assert result["extracted_data"]["product_name"] == "Test Product"
        assert result["extracted_data"]["price"] == "$99.99"
        assert len(result["extracted_data"]["reviews"]) == 2
    
    @pytest.mark.asyncio
    async def test_execute_missing_aiohttp(self, crawler_tool):
        """测试缺少aiohttp依赖的情况"""
        with patch('ai_agent_framework.tools.crawler_tool.aiohttp', None):
            arguments = {"url": "https://example.com", "action": "crawl"}
            result = await crawler_tool.execute(arguments)
            
            assert result.success is False
            assert "需要安装aiohttp" in result.result
    
    @pytest.mark.asyncio
    async def test_execute_invalid_arguments(self, crawler_tool):
        """测试执行时参数验证失败"""
        arguments = {"url": "invalid-url", "action": "crawl"}
        result = await crawler_tool.execute(arguments)
        
        assert result.success is False
        assert "参数验证失败" in result.result
    
    @pytest.mark.asyncio
    async def test_execute_successful_crawl(self, crawler_tool):
        """测试成功执行爬取任务"""
        # 直接Mock _execute_crawl_task方法
        mock_result = {
            "success": True,
            "url": "https://example.com",
            "action": "crawl",
            "format": "json",
            "data": {"title": "Test", "content": "Test content"},
            "metadata": {"status_code": 200}
        }

        with patch.object(crawler_tool, '_execute_crawl_task', return_value=mock_result):
            arguments = {
                "url": "https://example.com",
                "action": "crawl",
                "format": "json"
            }

            result = await crawler_tool.execute(arguments)

            assert result.success is True
            assert result.result["success"] is True
            assert result.result["url"] == "https://example.com"
            assert result.result["action"] == "crawl"
    
    @pytest.mark.asyncio
    async def test_execute_http_error(self, crawler_tool):
        """测试HTTP错误响应处理"""
        # 直接Mock _execute_crawl_task方法返回错误
        mock_result = {
            "success": False,
            "error": "爬虫服务返回错误状态: 404",
            "details": "Not Found",
            "url": "https://example.com/notfound",
            "action": "crawl"
        }

        with patch.object(crawler_tool, '_execute_crawl_task', return_value=mock_result):
            arguments = {
                "url": "https://example.com/notfound",
                "action": "crawl"
            }

            result = await crawler_tool.execute(arguments)

            assert result.success is False  # 工具执行失败（因为爬取失败）
            assert result.result["success"] is False  # 爬取失败
            assert "错误状态: 404" in result.result["error"]
    
    @pytest.mark.asyncio
    async def test_execute_timeout_error(self, crawler_tool):
        """测试超时错误处理"""
        # 直接Mock _execute_crawl_task方法返回超时错误
        mock_result = {
            "success": False,
            "error": "爬虫请求超时（>30秒）",
            "url": "https://example.com",
            "action": "crawl"
        }

        with patch.object(crawler_tool, '_execute_crawl_task', return_value=mock_result):
            arguments = {
                "url": "https://example.com",
                "action": "crawl"
            }

            result = await crawler_tool.execute(arguments)

            assert result.success is False  # 工具执行失败（因为爬取失败）
            assert result.result["success"] is False  # 爬取失败
            assert "请求超时" in result.result["error"]
    
    @pytest.mark.asyncio
    async def test_close_session(self, crawler_tool):
        """测试关闭HTTP会话"""
        mock_session = AsyncMock()
        crawler_tool._session = mock_session
        
        await crawler_tool.close()
        
        mock_session.close.assert_called_once()
        assert crawler_tool._session is None
    
    def test_generate_lua_script(self, crawler_tool):
        """测试生成Lua脚本"""
        selectors = {"title": "h1", "content": ".content"}
        options = {"wait_time": 3}
        
        lua_script = crawler_tool._generate_lua_script(selectors, options)
        
        assert "function main(splash, args)" in lua_script
        assert "splash:go(args.url)" in lua_script
        assert "splash:wait" in lua_script
        assert "splash:html()" in lua_script
