"""
AI Agent Framework 工具测试

测试示例工具的功能和工具注册表的管理功能。
"""

from unittest.mock import patch

import pytest

from ai_agent_framework.core.messages import ToolCall
from ai_agent_framework.tools.example_tools import (
    CalculatorTool,
    SearchTool,
    WeatherTool,
)
from ai_agent_framework.utils.tool_registry import ToolRegistry


class TestCalculatorTool:
    """计算器工具测试"""

    @pytest.fixture
    def calculator(self):
        """创建计算器工具实例"""
        return CalculatorTool()

    def test_tool_properties(self, calculator):
        """测试工具基本属性"""
        assert calculator.name == "calculator"
        assert "数学计算" in calculator.description
        assert "expression" in calculator.parameters_schema["properties"]

    @pytest.mark.asyncio
    async def test_simple_calculation(self, calculator):
        """测试简单计算"""
        arguments = {"expression": "2 + 3"}
        result = await calculator.execute(arguments)

        assert result.success is True
        assert result.result == 5.0

    @pytest.mark.asyncio
    async def test_complex_calculation(self, calculator):
        """测试复杂计算"""
        arguments = {"expression": "sqrt(16) + sin(pi/2)"}
        result = await calculator.execute(arguments)

        assert result.success is True
        assert result.result == 5.0  # sqrt(16) = 4, sin(pi/2) = 1

    @pytest.mark.asyncio
    async def test_invalid_expression(self, calculator):
        """测试无效表达式"""
        arguments = {"expression": "invalid_function(1)"}
        result = await calculator.execute(arguments)

        assert result.success is False
        assert "不允许使用的函数" in result.error

    @pytest.mark.asyncio
    async def test_empty_expression(self, calculator):
        """测试空表达式"""
        arguments = {"expression": ""}
        result = await calculator.execute(arguments)

        assert result.success is False
        assert "缺少计算表达式" in result.error

    @pytest.mark.asyncio
    async def test_validate_arguments(self, calculator):
        """测试参数验证"""
        # 有效参数
        valid_args = {"expression": "1 + 1"}
        assert await calculator.validate_arguments(valid_args) is True

        # 无效参数
        invalid_args = {"wrong_key": "1 + 1"}
        assert await calculator.validate_arguments(invalid_args) is False

        # 参数类型错误
        wrong_type_args = {"expression": 123}
        assert await calculator.validate_arguments(wrong_type_args) is False


class TestWeatherTool:
    """天气工具测试"""

    @pytest.fixture
    def weather(self):
        """创建天气工具实例"""
        return WeatherTool()

    def test_tool_properties(self, weather):
        """测试工具基本属性"""
        assert weather.name == "weather"
        assert "天气信息" in weather.description
        assert "city" in weather.parameters_schema["properties"]

    @pytest.mark.asyncio
    async def test_weather_query(self, weather):
        """测试天气查询"""
        arguments = {"city": "北京"}
        result = await weather.execute(arguments)

        assert result.success is True
        assert isinstance(result.result, dict)
        assert "city" in result.result
        assert "temperature" in result.result
        assert "condition" in result.result
        assert result.result["city"] == "北京"

    @pytest.mark.asyncio
    async def test_weather_with_unit(self, weather):
        """测试指定温度单位的天气查询"""
        arguments = {"city": "上海", "unit": "fahrenheit"}
        result = await weather.execute(arguments)

        assert result.success is True
        assert result.result["unit"] == "°F"

    @pytest.mark.asyncio
    async def test_empty_city(self, weather):
        """测试空城市名称"""
        arguments = {"city": ""}
        result = await weather.execute(arguments)

        assert result.success is False
        assert "缺少城市名称" in result.error


class TestSearchTool:
    """搜索工具测试"""

    @pytest.fixture
    def search(self):
        """创建搜索工具实例"""
        return SearchTool()

    def test_tool_properties(self, search):
        """测试工具基本属性"""
        assert search.name == "search"
        assert "搜索" in search.description
        assert "query" in search.parameters_schema["properties"]

    @pytest.mark.asyncio
    async def test_search_query(self, search):
        """测试搜索查询"""
        arguments = {"query": "人工智能"}
        result = await search.execute(arguments)

        assert result.success is True
        assert isinstance(result.result, dict)
        assert "query" in result.result
        assert "results" in result.result
        assert "total" in result.result
        assert result.result["query"] == "人工智能"
        assert len(result.result["results"]) > 0

    @pytest.mark.asyncio
    async def test_search_with_limit(self, search):
        """测试限制搜索结果数量"""
        arguments = {"query": "机器学习", "limit": 3}
        result = await search.execute(arguments)

        assert result.success is True
        assert len(result.result["results"]) <= 3

    @pytest.mark.asyncio
    async def test_empty_query(self, search):
        """测试空搜索查询"""
        arguments = {"query": ""}
        result = await search.execute(arguments)

        assert result.success is False
        assert "缺少搜索关键词" in result.error


class TestToolRegistry:
    """工具注册表测试"""

    @pytest.fixture
    def registry(self):
        """创建工具注册表实例"""
        return ToolRegistry()

    @pytest.fixture
    def calculator(self):
        """创建计算器工具实例"""
        return CalculatorTool()

    def test_register_tool(self, registry, calculator):
        """测试工具注册"""
        registry.register_tool(calculator)

        assert "calculator" in registry.list_tools()
        assert registry.get_tool("calculator") is calculator

    def test_unregister_tool(self, registry, calculator):
        """测试工具注销"""
        registry.register_tool(calculator)
        assert "calculator" in registry.list_tools()

        success = registry.unregister_tool("calculator")
        assert success is True
        assert "calculator" not in registry.list_tools()
        assert registry.get_tool("calculator") is None

    def test_get_tool_schemas(self, registry, calculator):
        """测试获取工具schemas"""
        registry.register_tool(calculator)

        schemas = registry.get_tool_schemas()
        assert len(schemas) == 1
        assert schemas[0]["type"] == "function"
        assert schemas[0]["function"]["name"] == "calculator"

    @pytest.mark.asyncio
    async def test_execute_tool(self, registry, calculator):
        """测试工具执行"""
        registry.register_tool(calculator)

        tool_call = ToolCall(
            id="test_call", name="calculator", arguments={"expression": "5 * 6"}
        )

        result = await registry.execute_tool(tool_call)

        assert result.success is True
        assert result.result == 30.0
        assert result.tool_call_id == "test_call"

    @pytest.mark.asyncio
    async def test_execute_nonexistent_tool(self, registry):
        """测试执行不存在的工具"""
        tool_call = ToolCall(id="test_call", name="nonexistent_tool", arguments={})

        with pytest.raises(Exception):  # 应该抛出ToolNotFoundError
            await registry.execute_tool(tool_call)

    @pytest.mark.asyncio
    async def test_execute_tools_batch(self, registry, calculator):
        """测试批量执行工具"""
        registry.register_tool(calculator)

        tool_calls = [
            ToolCall(id="call1", name="calculator", arguments={"expression": "1 + 1"}),
            ToolCall(id="call2", name="calculator", arguments={"expression": "2 * 3"}),
        ]

        results = await registry.execute_tools_batch(tool_calls)

        assert len(results) == 2
        assert results[0].result == 2.0
        assert results[1].result == 6.0

    def test_get_tool_info(self, registry, calculator):
        """测试获取工具信息"""
        registry.register_tool(calculator, max_concurrent=2)

        info = registry.get_tool_info("calculator")

        assert info is not None
        assert info["name"] == "calculator"
        assert info["max_concurrent"] == 2
        assert info["current_concurrent"] == 0

    def test_get_registry_stats(self, registry, calculator):
        """测试获取注册表统计信息"""
        registry.register_tool(calculator)

        stats = registry.get_registry_stats()

        assert stats["total_tools"] == 1
        assert "calculator" in stats["tools"]
        assert stats["total_executions"] == 0
