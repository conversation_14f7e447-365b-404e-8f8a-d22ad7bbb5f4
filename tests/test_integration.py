"""
AI Agent Framework 集成测试

测试各个模块之间的集成和整体系统功能。
"""

import asyncio
import tempfile
from unittest.mock import AsyncMock, MagicMock

import pytest

from ai_agent_framework.mcp.mcp_registry import MCPServiceRegistry
from ai_agent_framework.mcp.services.filesystem_service import FileSystemMCPService
from ai_agent_framework.security.auth import AuthManager, JWTAuth, APIKeyAuth
from ai_agent_framework.security.rate_limiter import RateLimiter
from ai_agent_framework.security.encryption import EncryptionManager
from ai_agent_framework.tools.database_tool import DatabaseTool
from ai_agent_framework.tools.git_tool import GitTool


class TestSystemIntegration:
    """系统集成测试"""
    
    @pytest.mark.asyncio
    async def test_mcp_with_security(self):
        """测试MCP服务与安全模块的集成"""
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 初始化组件
            registry = MCPServiceRegistry()
            auth_manager = AuthManager([
                JWTAuth("test-secret-key"),
                APIKeyAuth({"test-key": {"id": "user1", "username": "testuser"}})
            ])
            rate_limiter = RateLimiter(requests_per_minute=10, burst_size=5)
            
            await registry.start()
            
            try:
                # 注册MCP服务
                fs_service = FileSystemMCPService(
                    allowed_paths=[temp_dir],
                    read_only=False
                )
                success = await registry.register_service(fs_service)
                assert success
                
                # 测试身份验证
                auth_result = await auth_manager.authenticate({
                    "api_key": "test-key"
                })
                assert auth_result.success
                assert auth_result.user.username == "testuser"
                
                # 测试速率限制
                client_id = "test-client"
                for i in range(5):  # 在突发限制内
                    allowed = await rate_limiter.check_rate_limit(client_id)
                    assert allowed
                
                # 超出突发限制应该抛出异常
                with pytest.raises(Exception):  # RateLimitExceeded
                    await rate_limiter.check_rate_limit(client_id)
                
                # 验证MCP服务正常运行
                services = registry.list_services()
                assert len(services) == 1
                assert services[0].name == "filesystem"
                
            finally:
                await registry.stop()
    
    @pytest.mark.asyncio
    async def test_tools_with_encryption(self):
        """测试工具与加密模块的集成"""
        # 初始化组件
        db_tool = DatabaseTool(db_path=":memory:")
        encryption_manager = EncryptionManager()
        
        # 创建测试表
        await db_tool.execute({
            "action": "create_table",
            "sql": "CREATE TABLE secure_data (id INTEGER PRIMARY KEY, encrypted_data TEXT)"
        })
        
        # 测试数据加密和存储
        sensitive_data = "这是敏感信息：用户密码123456"
        encrypted_data = encryption_manager.encrypt_symmetric(sensitive_data)
        
        # 存储加密数据
        result = await db_tool.execute({
            "action": "insert",
            "table": "secure_data",
            "data": {"encrypted_data": encrypted_data}
        })
        assert result.success
        
        # 查询并解密数据
        query_result = await db_tool.execute({
            "action": "query",
            "sql": "SELECT encrypted_data FROM secure_data WHERE id = 1"
        })
        assert query_result.success
        
        stored_encrypted_data = query_result.result["rows"][0]["encrypted_data"]
        decrypted_data = encryption_manager.decrypt_symmetric(stored_encrypted_data)
        
        assert decrypted_data == sensitive_data
    
    @pytest.mark.asyncio
    async def test_concurrent_operations(self):
        """测试并发操作的集成"""
        # 初始化组件
        registry = MCPServiceRegistry()
        db_tool = DatabaseTool(db_path=":memory:")
        rate_limiter = RateLimiter(requests_per_minute=100, burst_size=20)
        
        await registry.start()
        
        try:
            # 注册多个服务
            services = []
            for i in range(5):
                with tempfile.TemporaryDirectory() as temp_dir:
                    service = FileSystemMCPService(
                        allowed_paths=[temp_dir],
                        read_only=True
                    )
                    services.append(service)
                    await registry.register_service(service, f"endpoint_{i}")
            
            # 创建数据库表
            await db_tool.execute({
                "action": "create_table",
                "sql": "CREATE TABLE concurrent_test (id INTEGER PRIMARY KEY, data TEXT)"
            })
            
            # 并发操作函数
            async def concurrent_operation(operation_id: int):
                # 检查速率限制
                await rate_limiter.check_rate_limit(f"client_{operation_id % 3}")
                
                # 数据库操作
                await db_tool.execute({
                    "action": "insert",
                    "table": "concurrent_test",
                    "data": {"data": f"operation_{operation_id}"}
                })
                
                # MCP服务健康检查
                health_results = await registry.health_check()
                assert len(health_results) == 5
                
                return operation_id
            
            # 并发执行20个操作
            tasks = [concurrent_operation(i) for i in range(20)]
            results = await asyncio.gather(*tasks)
            
            # 验证结果
            assert len(results) == 20
            assert all(isinstance(r, int) for r in results)
            
            # 验证数据库数据
            count_result = await db_tool.execute({
                "action": "query",
                "sql": "SELECT COUNT(*) as count FROM concurrent_test"
            })
            assert count_result.success
            assert count_result.result["rows"][0]["count"] == 20
            
        finally:
            await registry.stop()
    
    @pytest.mark.asyncio
    async def test_error_handling_integration(self):
        """测试错误处理的集成"""
        # 初始化组件
        db_tool = DatabaseTool(db_path=":memory:")
        encryption_manager = EncryptionManager()
        
        # 测试数据库错误处理
        result = await db_tool.execute({
            "action": "query",
            "sql": "SELECT * FROM nonexistent_table"
        })
        assert not result.success
        assert "失败" in result.result
        
        # 测试加密错误处理
        try:
            encryption_manager.decrypt_symmetric("invalid_encrypted_data")
            assert False, "应该抛出异常"
        except Exception:
            pass  # 预期的异常
        
        # 测试工具参数验证
        result = await db_tool.execute({
            "action": "invalid_action"
        })
        assert not result.success
    
    @pytest.mark.asyncio
    async def test_system_performance_integration(self):
        """测试系统性能集成"""
        import time
        
        # 初始化组件
        registry = MCPServiceRegistry()
        db_tool = DatabaseTool(db_path=":memory:")
        
        await registry.start()
        
        try:
            # 注册服务
            with tempfile.TemporaryDirectory() as temp_dir:
                fs_service = FileSystemMCPService(
                    allowed_paths=[temp_dir],
                    read_only=False
                )
                await registry.register_service(fs_service)
            
            # 创建测试表
            await db_tool.execute({
                "action": "create_table",
                "sql": "CREATE TABLE perf_test (id INTEGER PRIMARY KEY, data TEXT, timestamp REAL)"
            })
            
            # 性能测试
            start_time = time.time()
            
            # 并发执行多种操作
            async def mixed_operations(batch_id: int):
                operations = []
                
                # 数据库操作
                for i in range(10):
                    operations.append(db_tool.execute({
                        "action": "insert",
                        "table": "perf_test",
                        "data": {
                            "data": f"batch_{batch_id}_item_{i}",
                            "timestamp": time.time()
                        }
                    }))
                
                # MCP健康检查
                operations.append(registry.health_check())
                
                return await asyncio.gather(*operations)
            
            # 执行5个批次的混合操作
            batch_tasks = [mixed_operations(i) for i in range(5)]
            batch_results = await asyncio.gather(*batch_tasks)
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 验证结果
            assert len(batch_results) == 5
            for batch_result in batch_results:
                assert len(batch_result) == 11  # 10个数据库操作 + 1个健康检查
            
            # 验证数据完整性
            count_result = await db_tool.execute({
                "action": "query",
                "sql": "SELECT COUNT(*) as count FROM perf_test"
            })
            assert count_result.success
            assert count_result.result["rows"][0]["count"] == 50
            
            # 性能要求：50个数据库操作 + 5个健康检查应该在3秒内完成
            assert duration < 3.0
            
            operations_per_second = 55 / duration  # 总操作数 / 时间
            print(f"✅ 集成性能测试: 55个操作耗时{duration:.2f}秒，"
                  f"平均{operations_per_second:.2f}操作/秒")
            
        finally:
            await registry.stop()


class TestEndToEndScenarios:
    """端到端场景测试"""
    
    @pytest.mark.asyncio
    async def test_secure_file_management_scenario(self):
        """测试安全文件管理场景"""
        # 模拟一个完整的安全文件管理流程
        with tempfile.TemporaryDirectory() as temp_dir:
            # 初始化所有组件
            registry = MCPServiceRegistry()
            auth_manager = AuthManager([
                JWTAuth("test-secret-key"),
                APIKeyAuth({"admin-key": {"id": "admin", "username": "admin", "roles": ["admin"]}})
            ])
            rate_limiter = RateLimiter(requests_per_minute=50, burst_size=10)
            encryption_manager = EncryptionManager()
            
            await registry.start()
            
            try:
                # 1. 用户认证
                auth_result = await auth_manager.authenticate({
                    "api_key": "admin-key"
                })
                assert auth_result.success
                user = auth_result.user
                
                # 2. 检查权限（管理员可以访问文件系统）
                has_permission = auth_manager.check_permission(user, "file_access")
                assert has_permission or "admin" in user.roles
                
                # 3. 速率限制检查
                await rate_limiter.check_rate_limit(user.id)
                
                # 4. 注册文件系统服务
                fs_service = FileSystemMCPService(
                    allowed_paths=[temp_dir],
                    read_only=False
                )
                await registry.register_service(fs_service)
                
                # 5. 创建加密文件
                sensitive_content = "机密文档内容：公司财务报告"
                encrypted_content = encryption_manager.encrypt_symmetric(sensitive_content)
                
                # 6. 通过MCP服务写入文件
                import os
                test_file = os.path.join(temp_dir, "encrypted_document.txt")
                with open(test_file, 'w') as f:
                    f.write(encrypted_content)
                
                # 7. 验证文件存在
                assert os.path.exists(test_file)
                
                # 8. 读取并解密文件
                with open(test_file, 'r') as f:
                    stored_encrypted_content = f.read()
                
                decrypted_content = encryption_manager.decrypt_symmetric(stored_encrypted_content)
                assert decrypted_content == sensitive_content
                
                # 9. 健康检查
                health_results = await registry.health_check()
                assert health_results["filesystem"] is True
                
                print("✅ 安全文件管理场景测试通过")
                
            finally:
                await registry.stop()
    
    @pytest.mark.asyncio
    async def test_multi_user_concurrent_access_scenario(self):
        """测试多用户并发访问场景"""
        # 模拟多个用户同时访问系统的场景
        db_tool = DatabaseTool(db_path=":memory:")
        auth_manager = AuthManager([
            APIKeyAuth({
                "user1-key": {"id": "user1", "username": "user1", "roles": ["user"]},
                "user2-key": {"id": "user2", "username": "user2", "roles": ["user"]},
                "admin-key": {"id": "admin", "username": "admin", "roles": ["admin"]},
            })
        ])
        rate_limiter = RateLimiter(requests_per_minute=30, burst_size=8)
        
        # 创建用户活动表
        await db_tool.execute({
            "action": "create_table",
            "sql": "CREATE TABLE user_activities (id INTEGER PRIMARY KEY, user_id TEXT, activity TEXT, timestamp REAL)"
        })
        
        async def user_session(user_key: str, session_id: int):
            """模拟用户会话"""
            import time
            
            # 用户认证
            auth_result = await auth_manager.authenticate({"api_key": user_key})
            assert auth_result.success
            user = auth_result.user
            
            # 执行用户活动
            activities = []
            for i in range(5):
                # 速率限制检查
                await rate_limiter.check_rate_limit(user.id)
                
                # 记录活动
                activity = f"session_{session_id}_activity_{i}"
                result = await db_tool.execute({
                    "action": "insert",
                    "table": "user_activities",
                    "data": {
                        "user_id": user.id,
                        "activity": activity,
                        "timestamp": time.time()
                    }
                })
                assert result.success
                activities.append(activity)
                
                # 短暂延迟模拟真实操作
                await asyncio.sleep(0.01)
            
            return user.id, activities
        
        # 并发执行多个用户会话
        user_keys = ["user1-key", "user2-key", "admin-key"]
        tasks = []
        
        for i in range(9):  # 3个用户，每个用户3个会话
            user_key = user_keys[i % 3]
            tasks.append(user_session(user_key, i))
        
        results = await asyncio.gather(*tasks)
        
        # 验证结果
        assert len(results) == 9
        
        # 验证数据库中的活动记录
        count_result = await db_tool.execute({
            "action": "query",
            "sql": "SELECT COUNT(*) as count FROM user_activities"
        })
        assert count_result.success
        assert count_result.result["rows"][0]["count"] == 45  # 9个会话 × 5个活动
        
        # 验证每个用户的活动数量
        for user_key in user_keys:
            user_id = user_key.replace("-key", "")
            user_count_result = await db_tool.execute({
                "action": "query",
                "sql": f"SELECT COUNT(*) as count FROM user_activities WHERE user_id = '{user_id}'"
            })
            assert user_count_result.success
            assert user_count_result.result["rows"][0]["count"] == 15  # 3个会话 × 5个活动
        
        print("✅ 多用户并发访问场景测试通过")
