"""
AI Agent Framework 工具简单测试

测试新工具的基本功能。
"""

import pytest

from ai_agent_framework.tools.database_tool import DatabaseTool
from ai_agent_framework.tools.git_tool import GitTool


class TestToolsBasic:
    """工具基本功能测试"""

    @pytest.mark.asyncio
    async def test_database_tool_basic(self):
        """测试数据库工具基本功能"""
        db_tool = DatabaseTool(db_path=":memory:")

        # 测试工具属性
        assert db_tool.name == "database"
        assert "数据库连接工具" in db_tool.description
        assert "action" in db_tool.parameters_schema["properties"]

        # 测试参数验证
        assert await db_tool.validate_arguments({"action": "connect"})
        assert not await db_tool.validate_arguments({"action": "invalid"})
        assert not await db_tool.validate_arguments({})

        # 测试基本操作
        result = await db_tool.execute({"action": "connect"})
        assert result.success

        # 创建表
        create_sql = "CREATE TABLE test (id INTEGER PRIMARY KEY, name TEXT)"
        result = await db_tool.execute({"action": "create_table", "sql": create_sql})
        assert result.success

        # 插入数据
        result = await db_tool.execute(
            {"action": "insert", "table": "test", "data": {"name": "测试数据"}}
        )
        assert result.success

        # 查询数据
        result = await db_tool.execute({"action": "query", "sql": "SELECT * FROM test"})
        assert result.success
        assert len(result.result["rows"]) == 1
        assert result.result["rows"][0]["name"] == "测试数据"

        # 断开连接
        result = await db_tool.execute({"action": "disconnect"})
        assert result.success

    @pytest.mark.asyncio
    async def test_git_tool_basic(self):
        """测试Git工具基本功能"""
        git_tool = GitTool()

        # 测试工具属性
        assert git_tool.name == "git"
        assert "Git版本控制工具" in git_tool.description
        assert "action" in git_tool.parameters_schema["properties"]

        # 测试参数验证
        assert await git_tool.validate_arguments({"action": "status"})
        assert not await git_tool.validate_arguments({"action": "invalid"})
        assert not await git_tool.validate_arguments({})

        # 注意：Git操作需要实际的Git仓库，这里只测试工具接口
        # 在实际的Git仓库中，可以测试更多功能

    @pytest.mark.asyncio
    async def test_database_error_handling(self):
        """测试数据库错误处理"""
        db_tool = DatabaseTool(db_path=":memory:")

        # 测试空SQL语句
        result = await db_tool.execute({"action": "query", "sql": ""})
        assert not result.success
        assert "SQL语句不能为空" in result.result

        # 测试无效SQL语句
        result = await db_tool.execute({"action": "query", "sql": "INVALID SQL"})
        assert not result.success
        assert "查询失败" in result.result

        # 测试删除操作没有WHERE条件
        result = await db_tool.execute({"action": "delete", "table": "test"})
        assert not result.success
        assert "WHERE条件" in result.result

    @pytest.mark.asyncio
    async def test_git_error_handling(self):
        """测试Git错误处理"""
        git_tool = GitTool()

        # 测试不支持的操作
        result = await git_tool.execute({"action": "invalid_action"})
        assert not result.success
        assert "不支持的Git操作" in result.result

        # 测试空提交消息
        result = await git_tool.execute({"action": "commit", "message": ""})
        assert not result.success
        assert "提交消息不能为空" in result.result
