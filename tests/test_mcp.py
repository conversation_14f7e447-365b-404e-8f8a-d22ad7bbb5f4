"""
AI Agent Framework MCP模块测试

测试MCP协议适配器、服务注册表和文件系统服务。
"""

import asyncio
import os
import tempfile
from pathlib import Path

import pytest

from ai_agent_framework.mcp.mcp_adapter import MCPAdapter
from ai_agent_framework.mcp.mcp_registry import MCPServiceRegistry
from ai_agent_framework.mcp.mcp_service import MC<PERSON>apability, MCPRequest
from ai_agent_framework.mcp.services.filesystem_service import FileSystemMCPService


class TestMCPService:
    """MCP服务测试套件"""

    @pytest.fixture
    def temp_dir(self):
        """创建临时目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir

    @pytest.fixture
    async def filesystem_service(self, temp_dir):
        """创建文件系统服务"""
        service = FileSystemMCPService(
            allowed_paths=[temp_dir],
            read_only=False,
        )
        await service.start()
        yield service
        await service.stop()

    @pytest.mark.asyncio
    async def test_filesystem_service_basic(self, filesystem_service, temp_dir):
        """测试文件系统服务基本功能"""
        # 测试服务启动
        assert filesystem_service.is_running
        assert await filesystem_service.health_check()

        # 测试能力发现
        request = MCPRequest(method="discover_capabilities", params={})
        response = await filesystem_service.handle_request(request)
        assert response.is_success
        assert "capabilities" in response.result
        assert MCPCapability.FILE_READ.value in response.result["capabilities"]

    @pytest.mark.asyncio
    async def test_filesystem_read_write(self, filesystem_service, temp_dir):
        """测试文件读写功能"""
        test_file = os.path.join(temp_dir, "test.txt")
        test_content = "Hello, MCP World!"

        # 写入文件
        write_request = MCPRequest(
            method="file.write",
            params={
                "path": test_file,
                "content": test_content,
            },
        )
        write_response = await filesystem_service.handle_request(write_request)
        assert write_response.is_success
        assert write_response.result["success"]

        # 读取文件
        read_request = MCPRequest(method="file.read", params={"path": test_file})
        read_response = await filesystem_service.handle_request(read_request)
        assert read_response.is_success
        assert read_response.result["content"] == test_content
        assert not read_response.result["is_binary"]

    @pytest.mark.asyncio
    async def test_filesystem_list_directory(self, filesystem_service, temp_dir):
        """测试目录列表功能"""
        # 创建测试文件
        test_files = ["file1.txt", "file2.py", "file3.md"]
        for filename in test_files:
            file_path = os.path.join(temp_dir, filename)
            with open(file_path, "w") as f:
                f.write(f"Content of {filename}")

        # 列出目录
        list_request = MCPRequest(method="file.list", params={"path": temp_dir})
        list_response = await filesystem_service.handle_request(list_request)
        assert list_response.is_success

        result = list_response.result
        assert result["total_files"] == len(test_files)

        file_names = [f["name"] for f in result["files"]]
        for test_file in test_files:
            assert test_file in file_names

    @pytest.mark.asyncio
    async def test_filesystem_search_files(self, filesystem_service, temp_dir):
        """测试文件搜索功能"""
        # 创建测试文件
        test_files = {
            "test1.py": "print('hello')",
            "test2.txt": "hello world",
            "other.md": "# Title",
        }

        for filename, content in test_files.items():
            file_path = os.path.join(temp_dir, filename)
            with open(file_path, "w") as f:
                f.write(content)

        # 搜索Python文件
        search_request = MCPRequest(
            method="file.search",
            params={
                "path": temp_dir,
                "pattern": "*.py",
            },
        )
        search_response = await filesystem_service.handle_request(search_request)
        assert search_response.is_success

        result = search_response.result
        assert result["total_matches"] == 1
        assert result["matches"][0]["name"] == "test1.py"


class TestMCPRegistry:
    """MCP服务注册表测试套件"""

    @pytest.fixture
    async def registry(self):
        """创建服务注册表"""
        registry = MCPServiceRegistry(
            health_check_interval=1,  # 1秒用于测试
        )
        await registry.start()
        yield registry
        await registry.stop()

    @pytest.fixture
    def temp_dir(self):
        """创建临时目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir

    @pytest.mark.asyncio
    async def test_registry_service_lifecycle(self, registry, temp_dir):
        """测试服务注册表生命周期"""
        # 创建文件系统服务
        service = FileSystemMCPService(
            allowed_paths=[temp_dir],
            read_only=True,
        )

        # 注册服务
        success = await registry.register_service(service)
        assert success

        # 检查服务是否已注册
        registered_service = registry.get_service("filesystem")
        assert registered_service is not None
        assert registered_service.name == "filesystem"

        # 列出所有服务
        services = registry.list_services()
        assert len(services) == 1
        assert services[0].name == "filesystem"

        # 注销服务
        success = await registry.unregister_service("filesystem")
        assert success

        # 检查服务是否已注销
        assert registry.get_service("filesystem") is None
        assert len(registry.list_services()) == 0

    @pytest.mark.asyncio
    async def test_registry_capability_discovery(self, registry, temp_dir):
        """测试能力发现功能"""
        # 创建文件系统服务
        service = FileSystemMCPService(
            allowed_paths=[temp_dir],
            read_only=False,
        )

        # 注册服务
        await registry.register_service(service)

        # 发现具有文件读取能力的服务
        services = registry.discover_services(MCPCapability.FILE_READ)
        assert len(services) == 1
        assert services[0].name == "filesystem"

        # 发现具有多个能力的服务
        services = registry.discover_services_by_capabilities(
            [
                MCPCapability.FILE_READ,
                MCPCapability.FILE_WRITE,
            ]
        )
        assert len(services) == 1

        # 发现不存在的能力
        services = registry.discover_services(MCPCapability.CODE_EXECUTE)
        assert len(services) == 0

    @pytest.mark.asyncio
    async def test_registry_health_check(self, registry, temp_dir):
        """测试健康检查功能"""
        # 创建文件系统服务
        service = FileSystemMCPService(
            allowed_paths=[temp_dir],
            read_only=True,
        )

        # 注册服务
        await registry.register_service(service)

        # 执行健康检查
        health_results = await registry.health_check()
        assert "filesystem" in health_results
        assert health_results["filesystem"] is True

        # 停止服务
        await service.stop()

        # 再次检查健康状态
        health_results = await registry.health_check()
        assert health_results["filesystem"] is False

    @pytest.mark.asyncio
    async def test_registry_statistics(self, registry, temp_dir):
        """测试统计信息功能"""
        # 初始统计
        stats = registry.get_statistics()
        assert stats["total_services"] == 0
        assert stats["running_services"] == 0

        # 注册服务
        service = FileSystemMCPService(
            allowed_paths=[temp_dir],
            read_only=True,
        )
        await registry.register_service(service)

        # 检查统计信息
        stats = registry.get_statistics()
        assert stats["total_services"] == 1
        assert stats["running_services"] == 1
        assert MCPCapability.FILE_READ.value in stats["capability_distribution"]


class TestMCPAdapter:
    """MCP适配器测试套件"""

    @pytest.mark.asyncio
    async def test_adapter_lifecycle(self):
        """测试适配器生命周期"""
        adapter = MCPAdapter()

        # 启动适配器
        await adapter.start()

        # 关闭适配器
        await adapter.close()

    @pytest.mark.asyncio
    async def test_adapter_context_manager(self):
        """测试适配器上下文管理器"""
        async with MCPAdapter() as adapter:
            assert adapter is not None
            # 适配器应该已经启动
            assert adapter._session is not None
