"""
AI Agent Framework Agent类单元测试

测试Agent类的核心功能，包括对话、推理模式、状态管理等。
"""

from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from ai_agent_framework.core.agent import Agent, AgentState, ReasoningMode
from ai_agent_framework.core.messages import (
    Message,
    MessageRole,
    ModelResponse,
    ToolCall,
)
from ai_agent_framework.exceptions import AgentError, AgentExecutionError


class TestAgent:
    """Agent类测试套件"""

    @pytest.fixture
    def mock_model(self):
        """创建模拟模型"""
        model = AsyncMock()
        model.model_name = "test-model"
        model.supports_tools = True
        model.supports_streaming = True
        return model

    @pytest.fixture
    def mock_memory(self):
        """创建模拟记忆管理器"""
        memory = AsyncMock()
        return memory

    @pytest.fixture
    def agent(self, mock_model, mock_memory):
        """创建测试用Agent实例"""
        return Agent(
            agent_id="test-agent",
            model=mock_model,
            memory=mock_memory,
            reasoning_mode=ReasoningMode.DIRECT,
        )

    def test_agent_initialization(self, agent):
        """测试Agent初始化"""
        assert agent.agent_id == "test-agent"
        assert agent.state == AgentState.IDLE
        assert agent.reasoning_mode == ReasoningMode.DIRECT
        assert agent.iteration_count == 0
        assert len(agent.message_history) == 0

    @pytest.mark.asyncio
    async def test_simple_chat(self, agent, mock_model):
        """测试简单对话功能"""
        # 设置模型响应
        mock_response = ModelResponse(
            content="你好！我是AI助手。", model_name="test-model"
        )
        mock_model.generate.return_value = mock_response

        # 执行对话
        response = await agent.chat("你好")

        # 验证结果
        assert response.content == "你好！我是AI助手。"
        assert agent.state == AgentState.COMPLETED
        assert len(agent.message_history) == 2  # 用户消息 + 助手响应

        # 验证消息历史
        user_message = agent.message_history[0]
        assert user_message.role == MessageRole.USER
        assert user_message.content == "你好"

        assistant_message = agent.message_history[1]
        assert assistant_message.role == MessageRole.ASSISTANT
        assert assistant_message.content == "你好！我是AI助手。"

    @pytest.mark.asyncio
    async def test_chat_with_tools(self, agent, mock_model):
        """测试带工具调用的对话"""
        # 创建工具调用
        tool_call = ToolCall(
            id="call_123", name="calculator", arguments={"expression": "2 + 2"}
        )

        # 设置模型响应（包含工具调用）
        mock_response = ModelResponse(
            content="我来帮你计算。", tool_calls=[tool_call], model_name="test-model"
        )
        mock_model.generate.return_value = mock_response

        # 模拟工具执行结果
        with patch(
            "ai_agent_framework.utils.tool_registry.tool_registry"
        ) as mock_registry:
            from ai_agent_framework.core.messages import ToolResult

            mock_result = ToolResult(tool_call_id="call_123", success=True, result=4)
            mock_registry.execute_tools_batch.return_value = [mock_result]

            # 执行对话
            response = await agent.chat("计算 2 + 2")

            # 验证工具被调用
            mock_registry.execute_tools_batch.assert_called_once()

    @pytest.mark.asyncio
    async def test_react_reasoning(self, agent, mock_model):
        """测试ReAct推理模式"""
        agent.reasoning_mode = ReasoningMode.REACT
        agent.max_iterations = 2

        # 第一次调用：返回工具调用
        tool_call = ToolCall(
            id="call_123", name="calculator", arguments={"expression": "10 * 5"}
        )
        first_response = ModelResponse(
            content="我需要计算这个表达式。",
            tool_calls=[tool_call],
            model_name="test-model",
        )

        # 第二次调用：返回最终答案
        final_response = ModelResponse(
            content="计算结果是50。", model_name="test-model"
        )

        mock_model.generate.side_effect = [first_response, final_response]

        # 模拟工具执行
        with patch(
            "ai_agent_framework.utils.tool_registry.tool_registry"
        ) as mock_registry:
            from ai_agent_framework.core.messages import ToolResult

            mock_result = ToolResult(tool_call_id="call_123", success=True, result=50)
            mock_registry.execute_tools_batch.return_value = [mock_result]
            mock_registry.get_tool_schemas.return_value = []
            mock_registry.list_tools.return_value = []

            # 执行ReAct推理
            response = await agent.chat("计算 10 * 5")

            # 验证结果
            assert response.content == "计算结果是50。"
            assert agent.iteration_count == 2

    @pytest.mark.asyncio
    async def test_max_iterations_exceeded(self, agent, mock_model):
        """测试超过最大迭代次数的情况"""
        agent.reasoning_mode = ReasoningMode.REACT
        agent.max_iterations = 1

        # 模型总是返回工具调用，永不结束
        tool_call = ToolCall(
            id="call_123", name="calculator", arguments={"expression": "1 + 1"}
        )
        mock_response = ModelResponse(
            content="继续计算...", tool_calls=[tool_call], model_name="test-model"
        )
        mock_model.generate.return_value = mock_response

        # 模拟工具执行
        with patch(
            "ai_agent_framework.utils.tool_registry.tool_registry"
        ) as mock_registry:
            from ai_agent_framework.core.messages import ToolResult

            mock_result = ToolResult(tool_call_id="call_123", success=True, result=2)
            mock_registry.execute_tools_batch.return_value = [mock_result]
            mock_registry.get_tool_schemas.return_value = []
            mock_registry.list_tools.return_value = []

            # 应该抛出异常
            with pytest.raises(AgentExecutionError) as exc_info:
                await agent.chat("开始计算")

            assert "最大迭代次数" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_error_state_handling(self, agent):
        """测试错误状态处理"""
        # 设置Agent为错误状态
        agent.state = AgentState.ERROR

        # 尝试对话应该抛出异常
        with pytest.raises(AgentExecutionError):
            await agent.chat("测试消息")

    @pytest.mark.asyncio
    async def test_memory_integration(self, agent, mock_memory):
        """测试记忆系统集成"""
        # 设置模型响应
        mock_response = ModelResponse(
            content="记住了这个信息。", model_name="test-model"
        )
        agent.model.generate.return_value = mock_response

        # 执行对话
        await agent.chat("请记住我的名字是张三")

        # 验证记忆存储被调用
        assert mock_memory.store.call_count >= 1

    def test_get_state(self, agent):
        """测试获取Agent状态"""
        state = agent.get_state()

        assert state["agent_id"] == "test-agent"
        assert state["state"] == AgentState.IDLE
        assert state["reasoning_mode"] == ReasoningMode.DIRECT
        assert state["iteration_count"] == 0
        assert state["message_count"] == 0
        assert state["has_model"] is True
        assert state["has_memory"] is True

    def test_reset(self, agent):
        """测试重置Agent状态"""
        # 设置一些状态
        agent.state = AgentState.COMPLETED
        agent.current_task = "测试任务"
        agent.iteration_count = 5
        agent.message_history.append(Message(role=MessageRole.USER, content="测试消息"))

        # 重置
        agent.reset()

        # 验证状态被重置
        assert agent.state == AgentState.IDLE
        assert agent.current_task is None
        assert agent.iteration_count == 0
        assert len(agent.message_history) == 0

    @pytest.mark.asyncio
    async def test_no_model_error(self):
        """测试没有模型时的错误处理"""
        agent = Agent(model=None)

        with pytest.raises(AgentError) as exc_info:
            await agent.chat("测试消息")

        assert "未配置模型" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_clear_memory(self, agent, mock_memory):
        """测试清空记忆功能"""
        mock_memory.clear.return_value = True

        result = await agent.clear_memory("working")

        assert result is True
        mock_memory.clear.assert_called_once_with("working")

    @pytest.mark.asyncio
    async def test_get_memory_summary(self, agent, mock_memory):
        """测试获取记忆摘要"""
        mock_stats = {
            "total_memories": 10,
            "by_type": {"working": 5, "short_term": 3, "long_term": 2},
        }
        mock_memory.get_stats.return_value = mock_stats

        summary = await agent.get_memory_summary()

        assert summary == mock_stats
        mock_memory.get_stats.assert_called_once()
