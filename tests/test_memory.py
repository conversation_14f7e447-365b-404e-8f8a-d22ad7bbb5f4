"""
AI Agent Framework 记忆系统测试

测试记忆管理器的各种功能，包括存储、检索、搜索、清理等。
"""

import asyncio
import time
from unittest.mock import patch

import pytest

from ai_agent_framework.exceptions import MemoryRetrievalError, MemoryStorageError
from ai_agent_framework.memory.memory_manager import MemoryManager, MemoryType


class TestMemoryManager:
    """记忆管理器测试套件"""

    @pytest.fixture
    async def memory_manager(self):
        """创建测试用记忆管理器"""
        manager = MemoryManager(
            storage_path=":memory:",  # 使用内存数据库
            short_term_ttl=1,  # 1秒TTL用于测试
            working_ttl=3,  # 3秒TTL用于测试
            auto_cleanup=False,  # 禁用自动清理以便测试
        )
        yield manager
        await manager.close()

    @pytest.mark.asyncio
    async def test_store_and_retrieve(self, memory_manager):
        """测试基本的存储和检索功能"""
        # 存储记忆
        success = await memory_manager.store(
            key="test_key", value="test_value", memory_type=MemoryType.WORKING
        )
        assert success is True

        # 检索记忆
        retrieved = await memory_manager.retrieve(
            key="test_key", memory_type=MemoryType.WORKING
        )
        assert retrieved == "test_value"

    @pytest.mark.asyncio
    async def test_store_complex_data(self, memory_manager):
        """测试存储复杂数据结构"""
        complex_data = {
            "list": [1, 2, 3],
            "dict": {"nested": "value"},
            "string": "测试中文",
            "number": 42.5,
            "boolean": True,
        }

        await memory_manager.store("complex", complex_data, MemoryType.WORKING)
        retrieved = await memory_manager.retrieve("complex", MemoryType.WORKING)

        assert retrieved == complex_data

    @pytest.mark.asyncio
    async def test_memory_types(self, memory_manager):
        """测试不同记忆类型"""
        # 存储到不同类型的记忆
        await memory_manager.store("key1", "short_term", MemoryType.SHORT_TERM)
        await memory_manager.store("key2", "working", MemoryType.WORKING)
        await memory_manager.store("key3", "long_term", MemoryType.LONG_TERM)

        # 验证可以从不同类型检索
        assert (
            await memory_manager.retrieve("key1", MemoryType.SHORT_TERM) == "short_term"
        )
        assert await memory_manager.retrieve("key2", MemoryType.WORKING) == "working"
        assert (
            await memory_manager.retrieve("key3", MemoryType.LONG_TERM) == "long_term"
        )

        # 验证类型隔离
        assert await memory_manager.retrieve("key1", MemoryType.WORKING) is None
        assert await memory_manager.retrieve("key2", MemoryType.SHORT_TERM) is None

    @pytest.mark.asyncio
    async def test_ttl_expiration(self, memory_manager):
        """测试TTL过期功能"""
        # 存储短期记忆（1秒TTL）
        await memory_manager.store("expire_key", "expire_value", MemoryType.SHORT_TERM)

        # 立即检索应该成功
        retrieved = await memory_manager.retrieve("expire_key", MemoryType.SHORT_TERM)
        assert retrieved == "expire_value"

        # 等待过期
        await asyncio.sleep(1.5)

        # 过期后检索应该返回None
        retrieved = await memory_manager.retrieve("expire_key", MemoryType.SHORT_TERM)
        assert retrieved is None

    @pytest.mark.asyncio
    async def test_custom_ttl(self, memory_manager):
        """测试自定义TTL"""
        # 使用自定义TTL存储
        await memory_manager.store(
            key="custom_ttl",
            value="custom_value",
            memory_type=MemoryType.WORKING,
            ttl=0.5,  # 0.5秒TTL
        )

        # 立即检索应该成功
        retrieved = await memory_manager.retrieve("custom_ttl", MemoryType.WORKING)
        assert retrieved == "custom_value"

        # 等待过期
        await asyncio.sleep(0.8)

        # 过期后检索应该返回None
        retrieved = await memory_manager.retrieve("custom_ttl", MemoryType.WORKING)
        assert retrieved is None

    @pytest.mark.asyncio
    async def test_search_functionality(self, memory_manager):
        """测试搜索功能"""
        # 存储一些测试数据
        test_data = [
            ("doc1", "这是一个关于人工智能的文档"),
            ("doc2", "机器学习是人工智能的重要分支"),
            ("doc3", "深度学习技术发展迅速"),
            ("doc4", "自然语言处理很有趣"),
            ("doc5", "计算机视觉应用广泛"),
        ]

        for key, value in test_data:
            await memory_manager.store(key, value, MemoryType.WORKING)

        # 搜索相关记忆
        results = await memory_manager.search(
            query="人工智能", memory_type=MemoryType.WORKING, limit=5
        )

        # 验证搜索结果
        assert len(results) >= 2  # 至少应该找到2个相关文档

        # 验证结果包含相关内容
        found_keys = [result["key"] for result in results]
        assert "doc1" in found_keys
        assert "doc2" in found_keys

    @pytest.mark.asyncio
    async def test_delete_functionality(self, memory_manager):
        """测试删除功能"""
        # 存储记忆
        await memory_manager.store("delete_key", "delete_value", MemoryType.WORKING)

        # 验证存在
        retrieved = await memory_manager.retrieve("delete_key", MemoryType.WORKING)
        assert retrieved == "delete_value"

        # 删除记忆
        success = await memory_manager.delete("delete_key", MemoryType.WORKING)
        assert success is True

        # 验证已删除
        retrieved = await memory_manager.retrieve("delete_key", MemoryType.WORKING)
        assert retrieved is None

        # 删除不存在的记忆
        success = await memory_manager.delete("nonexistent", MemoryType.WORKING)
        assert success is False

    @pytest.mark.asyncio
    async def test_clear_functionality(self, memory_manager):
        """测试清空功能"""
        # 存储一些记忆
        await memory_manager.store("key1", "value1", MemoryType.WORKING)
        await memory_manager.store("key2", "value2", MemoryType.WORKING)
        await memory_manager.store("key3", "value3", MemoryType.SHORT_TERM)

        # 清空工作记忆
        success = await memory_manager.clear(MemoryType.WORKING)
        assert success is True

        # 验证工作记忆已清空
        assert await memory_manager.retrieve("key1", MemoryType.WORKING) is None
        assert await memory_manager.retrieve("key2", MemoryType.WORKING) is None

        # 验证其他类型记忆未受影响
        assert await memory_manager.retrieve("key3", MemoryType.SHORT_TERM) == "value3"

        # 清空所有记忆
        success = await memory_manager.clear()
        assert success is True

        # 验证所有记忆已清空
        assert await memory_manager.retrieve("key3", MemoryType.SHORT_TERM) is None

    @pytest.mark.asyncio
    async def test_get_stats(self, memory_manager):
        """测试统计信息功能"""
        # 存储一些记忆
        await memory_manager.store("stat1", "value1", MemoryType.WORKING)
        await memory_manager.store("stat2", "value2", MemoryType.SHORT_TERM)
        await memory_manager.store("stat3", "value3", MemoryType.LONG_TERM)

        # 获取统计信息
        stats = await memory_manager.get_stats()

        # 验证统计信息
        assert "total_memories" in stats
        assert "by_type" in stats
        assert stats["total_memories"] >= 3
        assert stats["by_type"]["working"] >= 1
        assert stats["by_type"]["short_term"] >= 1
        assert stats["by_type"]["long_term"] >= 1

    @pytest.mark.asyncio
    async def test_metadata_support(self, memory_manager):
        """测试元数据支持"""
        metadata = {
            "source": "user_input",
            "importance": "high",
            "tags": ["test", "metadata"],
        }

        await memory_manager.store(
            key="meta_key",
            value="meta_value",
            memory_type=MemoryType.WORKING,
            metadata=metadata,
        )

        # 检索记忆（元数据在内部存储，这里主要测试不会出错）
        retrieved = await memory_manager.retrieve("meta_key", MemoryType.WORKING)
        assert retrieved == "meta_value"

    @pytest.mark.asyncio
    async def test_concurrent_operations(self, memory_manager):
        """测试并发操作"""
        # 并发存储
        store_tasks = [
            memory_manager.store(f"concurrent_{i}", f"value_{i}", MemoryType.WORKING)
            for i in range(10)
        ]
        results = await asyncio.gather(*store_tasks)
        assert all(results)

        # 并发检索
        retrieve_tasks = [
            memory_manager.retrieve(f"concurrent_{i}", MemoryType.WORKING)
            for i in range(10)
        ]
        results = await asyncio.gather(*retrieve_tasks)

        # 验证所有检索都成功
        for i, result in enumerate(results):
            assert result == f"value_{i}"

    @pytest.mark.asyncio
    async def test_similarity_calculation(self, memory_manager):
        """测试相似度计算"""
        # 这是一个内部方法测试
        similarity1 = memory_manager._calculate_similarity("人工智能", "人工智能技术")
        similarity2 = memory_manager._calculate_similarity("人工智能", "机器学习")
        similarity3 = memory_manager._calculate_similarity("人工智能", "天气预报")

        # 验证相似度计算合理性
        assert similarity1 > similarity2  # 更相似的文本应该有更高的相似度
        assert similarity2 > similarity3  # 相关的文本应该比不相关的有更高相似度
        assert 0 <= similarity1 <= 1  # 相似度应该在0-1之间
        assert 0 <= similarity2 <= 1
        assert 0 <= similarity3 <= 1

    @pytest.mark.asyncio
    async def test_cleanup_expired_memories(self, memory_manager):
        """测试过期记忆清理"""
        # 存储一些会过期的记忆
        await memory_manager.store("expire1", "value1", MemoryType.SHORT_TERM, ttl=0.1)
        await memory_manager.store("expire2", "value2", MemoryType.SHORT_TERM, ttl=0.1)
        await memory_manager.store("keep", "value3", MemoryType.LONG_TERM)  # 不会过期

        # 等待过期
        await asyncio.sleep(0.2)

        # 手动触发清理
        cleaned_count = await memory_manager._cleanup_expired_memories()

        # 验证清理结果
        assert cleaned_count >= 2

        # 验证过期记忆已被清理
        assert await memory_manager.retrieve("expire1", MemoryType.SHORT_TERM) is None
        assert await memory_manager.retrieve("expire2", MemoryType.SHORT_TERM) is None

        # 验证未过期记忆仍然存在
        assert await memory_manager.retrieve("keep", MemoryType.LONG_TERM) == "value3"

    @pytest.mark.asyncio
    async def test_error_handling(self, memory_manager):
        """测试错误处理"""
        # 测试无效的记忆类型（通过直接调用内部方法）
        with pytest.raises(Exception):
            await memory_manager.store("key", "value", "invalid_type")

    @pytest.mark.asyncio
    async def test_database_error_handling(self):
        """测试数据库错误处理"""
        # 使用无效路径创建记忆管理器
        with pytest.raises(MemoryStorageError):
            MemoryManager(storage_path="/invalid/path/memory.db")

    @pytest.mark.asyncio
    async def test_large_data_handling(self, memory_manager):
        """测试大数据处理"""
        # 创建一个较大的数据对象
        large_data = {
            "text": "这是一个很长的文本" * 1000,
            "numbers": list(range(1000)),
            "nested": {"level1": {"level2": {"level3": "deep_value"}}},
        }

        # 存储大数据
        await memory_manager.store("large_data", large_data, MemoryType.WORKING)

        # 检索大数据
        retrieved = await memory_manager.retrieve("large_data", MemoryType.WORKING)

        # 验证数据完整性
        assert retrieved == large_data
        assert len(retrieved["text"]) == len(large_data["text"])
        assert len(retrieved["numbers"]) == 1000
        assert retrieved["nested"]["level1"]["level2"]["level3"] == "deep_value"
