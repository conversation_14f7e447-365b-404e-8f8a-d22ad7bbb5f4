"""
新增工具测试

测试消息队列、缓存、文件存储和数据处理工具的功能。
"""

import asyncio
import json
import tempfile
from pathlib import Path

import pytest

from ai_agent_framework.tools import (
    MessageQueueTool,
    CacheTool,
    FileStorageTool,
    DataProcessingTool,
    MessageQueueType,
    CacheType,
    StorageType,
)


class TestMessageQueueTool:
    """消息队列工具测试"""
    
    @pytest.fixture
    async def mq_tool(self):
        """创建消息队列工具实例"""
        tool = MessageQueueTool(queue_type=MessageQueueType.MEMORY)
        await tool.execute({"action": "connect"})
        yield tool
        await tool.execute({"action": "disconnect"})
    
    @pytest.mark.asyncio
    async def test_connect_disconnect(self):
        """测试连接和断开连接"""
        tool = MessageQueueTool(queue_type=MessageQueueType.MEMORY)
        
        # 测试连接
        result = await tool.execute({"action": "connect"})
        assert result.success
        assert result.result["connected"]
        
        # 测试断开连接
        result = await tool.execute({"action": "disconnect"})
        assert result.success
        assert result.result["disconnected"]
    
    @pytest.mark.asyncio
    async def test_queue_operations(self, mq_tool):
        """测试队列操作"""
        # 创建队列
        result = await mq_tool.execute({
            "action": "create_queue",
            "queue_name": "test_queue"
        })
        assert result.success
        assert result.result["created"]
        
        # 发送消息
        result = await mq_tool.execute({
            "action": "send",
            "queue_name": "test_queue",
            "message": {
                "content": "测试消息",
                "priority": "normal"
            }
        })
        assert result.success
        assert result.result["sent"]
        
        # 接收消息
        result = await mq_tool.execute({
            "action": "receive",
            "queue_name": "test_queue"
        })
        assert result.success
        assert result.result["received"]
        assert result.result["message"]["content"] == "测试消息"
    
    @pytest.mark.asyncio
    async def test_queue_info(self, mq_tool):
        """测试队列信息查询"""
        # 创建队列
        await mq_tool.execute({
            "action": "create_queue",
            "queue_name": "info_queue"
        })
        
        # 获取队列信息
        result = await mq_tool.execute({
            "action": "queue_info",
            "queue_name": "info_queue"
        })
        assert result.success
        assert result.result["name"] == "info_queue"
        assert result.result["type"] == "memory"


class TestCacheTool:
    """缓存工具测试"""
    
    @pytest.fixture
    async def cache_tool(self):
        """创建缓存工具实例"""
        tool = CacheTool(cache_type=CacheType.MEMORY)
        await tool.execute({"action": "connect"})
        yield tool
        await tool.execute({"action": "disconnect"})
    
    @pytest.mark.asyncio
    async def test_basic_operations(self, cache_tool):
        """测试基本缓存操作"""
        # 设置缓存
        result = await cache_tool.execute({
            "action": "set",
            "key": "test_key",
            "value": "测试值"
        })
        assert result.success
        assert result.result["set"]
        
        # 获取缓存
        result = await cache_tool.execute({
            "action": "get",
            "key": "test_key"
        })
        assert result.success
        assert result.result["value"] == "测试值"
        assert result.result["found"]
        
        # 检查存在
        result = await cache_tool.execute({
            "action": "exists",
            "key": "test_key"
        })
        assert result.success
        assert result.result["exists"]
        
        # 删除缓存
        result = await cache_tool.execute({
            "action": "delete",
            "key": "test_key"
        })
        assert result.success
        assert result.result["deleted"]
    
    @pytest.mark.asyncio
    async def test_batch_operations(self, cache_tool):
        """测试批量操作"""
        # 批量设置
        result = await cache_tool.execute({
            "action": "set_multi",
            "key_value_pairs": {
                "key1": "value1",
                "key2": "value2",
                "key3": "value3"
            }
        })
        assert result.success
        assert result.result["success_count"] == 3
        
        # 批量获取
        result = await cache_tool.execute({
            "action": "get_multi",
            "keys": ["key1", "key2", "key3"]
        })
        assert result.success
        assert result.result["found_count"] == 3
        assert result.result["values"]["key1"] == "value1"
    
    @pytest.mark.asyncio
    async def test_increment_decrement(self, cache_tool):
        """测试数值增减操作"""
        # 设置初始值
        await cache_tool.execute({
            "action": "set",
            "key": "counter",
            "value": 10
        })
        
        # 增加
        result = await cache_tool.execute({
            "action": "increment",
            "key": "counter",
            "delta": 5
        })
        assert result.success
        assert result.result["new_value"] == 15
        
        # 减少
        result = await cache_tool.execute({
            "action": "decrement",
            "key": "counter",
            "delta": 3
        })
        assert result.success
        assert result.result["new_value"] == 12


class TestFileStorageTool:
    """文件存储工具测试"""
    
    @pytest.fixture
    async def storage_tool(self):
        """创建文件存储工具实例"""
        with tempfile.TemporaryDirectory() as temp_dir:
            tool = FileStorageTool(
                storage_type=StorageType.LOCAL,
                connection_config={"base_path": temp_dir}
            )
            await tool.execute({"action": "connect"})
            yield tool, temp_dir
            await tool.execute({"action": "disconnect"})
    
    @pytest.mark.asyncio
    async def test_file_operations(self, storage_tool):
        """测试文件操作"""
        tool, temp_dir = storage_tool
        
        # 创建测试文件
        test_file = Path(temp_dir) / "test_input.txt"
        test_file.write_text("测试文件内容", encoding="utf-8")
        
        # 上传文件
        result = await tool.execute({
            "action": "upload",
            "local_path": str(test_file),
            "remote_path": "uploads/test.txt"
        })
        assert result.success
        assert result.result["uploaded"]
        
        # 检查文件存在
        result = await tool.execute({
            "action": "exists",
            "remote_path": "uploads/test.txt"
        })
        assert result.success
        assert result.result["exists"]
        
        # 获取文件信息
        result = await tool.execute({
            "action": "info",
            "remote_path": "uploads/test.txt"
        })
        assert result.success
        assert result.result["found"]
        assert result.result["file_info"]["name"] == "test.txt"
        
        # 下载文件
        download_path = Path(temp_dir) / "downloaded.txt"
        result = await tool.execute({
            "action": "download",
            "remote_path": "uploads/test.txt",
            "local_path": str(download_path)
        })
        assert result.success
        assert result.result["downloaded"]
        assert download_path.exists()
        assert download_path.read_text(encoding="utf-8") == "测试文件内容"
    
    @pytest.mark.asyncio
    async def test_directory_operations(self, storage_tool):
        """测试目录操作"""
        tool, temp_dir = storage_tool
        
        # 创建目录
        result = await tool.execute({
            "action": "create_dir",
            "directory_path": "test_directory"
        })
        assert result.success
        assert result.result["created"]
        
        # 列出文件
        result = await tool.execute({
            "action": "list",
            "prefix": "",
            "limit": 10
        })
        assert result.success
        assert isinstance(result.result["files"], list)


class TestDataProcessingTool:
    """数据处理工具测试"""
    
    @pytest.fixture
    def data_tool(self):
        """创建数据处理工具实例"""
        return DataProcessingTool()
    
    @pytest.mark.asyncio
    async def test_json_operations(self, data_tool):
        """测试JSON操作"""
        # JSON解析
        json_str = '{"name": "张三", "age": 30, "city": "北京"}'
        result = await data_tool.execute({
            "action": "parse_json",
            "data": json_str
        })
        assert result.success
        assert result.result["parsed"]
        assert result.result["data"]["name"] == "张三"
        
        # JSON格式化
        data = {"name": "李四", "age": 25}
        result = await data_tool.execute({
            "action": "format_json",
            "data": data,
            "indent": 2
        })
        assert result.success
        assert result.result["formatted"]
        assert "李四" in result.result["data"]
        
        # JSON验证
        result = await data_tool.execute({
            "action": "validate_json",
            "data": json_str
        })
        assert result.success
        assert result.result["valid"]
    
    @pytest.mark.asyncio
    async def test_csv_operations(self, data_tool):
        """测试CSV操作"""
        csv_data = "姓名,年龄,城市\n张三,30,北京\n李四,25,上海"
        
        result = await data_tool.execute({
            "action": "parse_csv",
            "data": csv_data
        })
        assert result.success
        assert result.result["parsed"]
        assert result.result["headers"] == ["姓名", "年龄", "城市"]
        assert len(result.result["rows"]) == 2
        assert result.result["rows"][0] == ["张三", "30", "北京"]
    
    @pytest.mark.asyncio
    async def test_xml_operations(self, data_tool):
        """测试XML操作"""
        xml_data = """<?xml version="1.0" encoding="UTF-8"?>
        <person>
            <name>张三</name>
            <age>30</age>
            <city>北京</city>
        </person>"""
        
        # XML解析
        result = await data_tool.execute({
            "action": "parse_xml",
            "data": xml_data
        })
        assert result.success
        assert result.result["parsed"]
        assert result.result["root_tag"] == "person"
        
        # XML转字典
        result = await data_tool.execute({
            "action": "xml_to_dict",
            "data": xml_data
        })
        assert result.success
        assert result.result["converted"]
        assert "name" in result.result["data"]
    
    @pytest.mark.asyncio
    async def test_text_extraction(self, data_tool):
        """测试文本提取"""
        text = "联系我们：邮箱 <EMAIL>，电话 138-0013-8000，网站 https://example.com"
        
        # 提取邮箱
        result = await data_tool.execute({
            "action": "extract_emails",
            "data": text
        })
        assert result.success
        # 注意：这个测试需要在实际实现中完成extract_emails方法
        
        # 提取URL
        result = await data_tool.execute({
            "action": "extract_urls",
            "data": text
        })
        assert result.success
        # 注意：这个测试需要在实际实现中完成extract_urls方法


@pytest.mark.asyncio
async def test_tool_validation():
    """测试工具参数验证"""
    # 测试消息队列工具验证
    mq_tool = MessageQueueTool()
    
    # 有效参数
    valid_args = {"action": "connect"}
    assert await mq_tool.validate_arguments(valid_args)
    
    # 无效参数
    invalid_args = {"action": "invalid_action"}
    assert not await mq_tool.validate_arguments(invalid_args)
    
    # 缺少必需参数
    missing_args = {"action": "send"}  # 缺少queue_name
    assert not await mq_tool.validate_arguments(missing_args)


if __name__ == "__main__":
    # 运行简单测试
    async def run_basic_tests():
        """运行基础测试"""
        print("开始测试新增工具...")
        
        # 测试消息队列工具
        print("测试消息队列工具...")
        mq_tool = MessageQueueTool(queue_type=MessageQueueType.MEMORY)
        result = await mq_tool.execute({"action": "connect"})
        print(f"连接结果: {result.success}")
        
        # 测试缓存工具
        print("测试缓存工具...")
        cache_tool = CacheTool(cache_type=CacheType.MEMORY)
        result = await cache_tool.execute({"action": "connect"})
        print(f"缓存连接结果: {result.success}")
        
        # 测试数据处理工具
        print("测试数据处理工具...")
        data_tool = DataProcessingTool()
        result = await data_tool.execute({
            "action": "parse_json",
            "data": '{"test": "数据"}'
        })
        print(f"JSON解析结果: {result.success}")
        
        print("基础测试完成！")
    
    asyncio.run(run_basic_tests())
