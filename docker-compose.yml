version: '3.8'

services:
  # AI Agent Framework 主服务
  ai-agent:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ai-agent-framework
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
      - LOG_LEVEL=INFO
      - DATABASE_URL=sqlite:///data/agent.db
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    depends_on:
      - redis
      - postgres
    restart: unless-stopped
    networks:
      - ai-agent-network

  # Redis 缓存服务
  redis:
    image: redis:7-alpine
    container_name: ai-agent-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - ai-agent-network

  # PostgreSQL 数据库服务
  postgres:
    image: postgres:15-alpine
    container_name: ai-agent-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=ai_agent
      - POSTGRES_USER=ai_agent
      - POSTGRES_PASSWORD=ai_agent_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    networks:
      - ai-agent-network

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: ai-agent-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - ai-agent
    restart: unless-stopped
    networks:
      - ai-agent-network

  # 监控服务 - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: ai-agent-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    restart: unless-stopped
    networks:
      - ai-agent-network

  # 监控服务 - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: ai-agent-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - ai-agent-network

volumes:
  redis_data:
  postgres_data:
  prometheus_data:
  grafana_data:

networks:
  ai-agent-network:
    driver: bridge
