# Pre-commit 配置文件
# 用于在提交代码前自动运行代码质量检查工具

repos:
  # 通用代码检查
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: trailing-whitespace
        description: 移除行尾空白字符
      - id: end-of-file-fixer
        description: 确保文件以换行符结尾
      - id: check-yaml
        description: 检查YAML文件语法
      - id: check-toml
        description: 检查TOML文件语法
      - id: check-json
        description: 检查JSON文件语法
      - id: check-merge-conflict
        description: 检查合并冲突标记
      - id: check-added-large-files
        description: 检查大文件
        args: ['--maxkb=1000']
      - id: debug-statements
        description: 检查调试语句
      - id: check-docstring-first
        description: 检查docstring位置

  # Python代码格式化
  - repo: https://github.com/psf/black
    rev: 24.8.0
    hooks:
      - id: black
        description: 使用Black格式化Python代码
        language_version: python3
        args: [--line-length=88]

  # Python导入排序
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        description: 使用isort排序Python导入
        args: [--profile=black, --line-length=88]

  # Python代码检查
  - repo: https://github.com/pycqa/flake8
    rev: 7.1.1
    hooks:
      - id: flake8
        description: 使用flake8检查Python代码
        additional_dependencies:
          - flake8-docstrings
          - flake8-bugbear
          - flake8-comprehensions
        args: [--max-line-length=88, --extend-ignore=E203,W503]

  # Python类型检查
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.11.2
    hooks:
      - id: mypy
        description: 使用mypy进行类型检查
        additional_dependencies:
          - pydantic
          - types-requests
        args: [--strict, --ignore-missing-imports]

  # 安全检查
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.9
    hooks:
      - id: bandit
        description: 使用bandit检查安全问题
        args: [-r, src/, -f, json]
        exclude: tests/

  # 文档检查
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        description: 检查Python文档字符串风格
        args: [--convention=google]
        exclude: tests/

# 全局配置
default_language_version:
  python: python3.9

# CI配置
ci:
  autofix_commit_msg: |
    [pre-commit.ci] 自动修复代码格式问题

    由pre-commit.ci自动生成的提交
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] 自动更新pre-commit钩子版本'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
