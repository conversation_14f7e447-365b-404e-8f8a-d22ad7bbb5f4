# 贡献指南

感谢您对 AI Agent Framework 项目的关注！我们欢迎所有形式的贡献，包括但不限于：

- 🐛 报告 Bug
- 💡 提出新功能建议
- 📝 改进文档
- 🔧 提交代码修复
- ✨ 开发新功能
- 🧪 编写测试用例

## 🚀 快速开始

### 开发环境设置

1. **Fork 项目**
   ```bash
   # 在 GitHub 上 Fork 项目到你的账户
   # 然后克隆你的 Fork
   git clone https://github.com/your-username/ai-agent.git
   cd ai-agent
   ```

2. **安装依赖**
   ```bash
   # 使用 Poetry 安装依赖
   poetry install
   
   # 激活虚拟环境
   poetry shell
   ```

3. **安装开发工具**
   ```bash
   # 安装 pre-commit 钩子
   pre-commit install
   
   # 运行代码检查
   pre-commit run --all-files
   ```

4. **配置环境变量**
   ```bash
   # 复制环境变量模板
   cp .env.example .env
   
   # 编辑 .env 文件，添加必要的 API 密钥
   ```

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_agent.py

# 运行测试并生成覆盖率报告
pytest --cov=ai_agent_framework --cov-report=html
```

## 📋 开发规范

### 代码风格

我们使用以下工具确保代码质量：

- **Black**: 代码格式化
- **isort**: 导入排序
- **flake8**: 代码检查
- **mypy**: 类型检查

所有代码都会通过 pre-commit 钩子自动检查。

### 提交规范

我们使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<类型>[可选的作用域]: <描述>

[可选的正文]

[可选的脚注]
```

**类型说明：**
- `feat`: 新功能
- `fix`: Bug 修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例：**
```
feat(models): 添加 Claude-3.5 模型支持

- 实现 Claude-3.5-Sonnet 适配器
- 添加流式响应支持
- 更新相关文档

Closes #123
```

### 分支策略

- `main`: 主分支，包含稳定的生产代码
- `develop`: 开发分支，包含最新的开发代码
- `feature/*`: 功能分支，用于开发新功能
- `fix/*`: 修复分支，用于修复 Bug
- `docs/*`: 文档分支，用于文档更新

### 代码注释

- 所有公共 API 必须有详细的中文文档字符串
- 复杂的业务逻辑需要添加中文注释说明
- 使用 Google 风格的文档字符串

```python
def example_function(param1: str, param2: int) -> bool:
    """
    示例函数说明
    
    这个函数用于演示文档字符串的格式。
    
    Args:
        param1: 第一个参数，字符串类型
        param2: 第二个参数，整数类型
        
    Returns:
        bool: 执行结果，成功返回 True
        
    Raises:
        ValueError: 当参数无效时抛出
        
    Example:
        >>> result = example_function("test", 42)
        >>> print(result)
        True
    """
    # 实现逻辑...
    return True
```

## 🐛 报告 Bug

在报告 Bug 之前，请：

1. 检查是否已有相关的 Issue
2. 确保使用的是最新版本
3. 尝试在干净的环境中重现问题

**Bug 报告模板：**

```markdown
## Bug 描述
简要描述遇到的问题

## 重现步骤
1. 执行步骤 1
2. 执行步骤 2
3. 看到错误

## 期望行为
描述你期望发生的情况

## 实际行为
描述实际发生的情况

## 环境信息
- OS: [例如 Ubuntu 20.04]
- Python 版本: [例如 3.9.7]
- 框架版本: [例如 0.1.0]
- 相关依赖版本

## 附加信息
- 错误日志
- 配置文件
- 其他相关信息
```

## 💡 功能建议

**功能建议模板：**

```markdown
## 功能描述
简要描述建议的功能

## 使用场景
描述这个功能的使用场景和价值

## 详细设计
如果有具体的设计想法，请详细描述

## 替代方案
是否考虑过其他实现方式

## 附加信息
其他相关信息或参考资料
```

## 🔧 提交代码

### Pull Request 流程

1. **创建分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **开发和测试**
   - 编写代码
   - 添加测试
   - 更新文档
   - 运行测试确保通过

3. **提交代码**
   ```bash
   git add .
   git commit -m "feat: 添加新功能描述"
   git push origin feature/your-feature-name
   ```

4. **创建 Pull Request**
   - 在 GitHub 上创建 PR
   - 填写详细的 PR 描述
   - 关联相关的 Issue

### Pull Request 模板

```markdown
## 变更描述
简要描述这个 PR 的变更内容

## 变更类型
- [ ] Bug 修复
- [ ] 新功能
- [ ] 文档更新
- [ ] 代码重构
- [ ] 性能优化
- [ ] 其他

## 测试
- [ ] 添加了新的测试用例
- [ ] 所有测试都通过
- [ ] 手动测试通过

## 检查清单
- [ ] 代码遵循项目规范
- [ ] 添加了必要的文档
- [ ] 更新了相关的 CHANGELOG
- [ ] 没有引入破坏性变更

## 关联 Issue
Closes #issue_number

## 附加信息
其他需要说明的信息
```

## 📚 文档贡献

- 文档使用 Markdown 格式
- 所有文档都应该使用中文
- 代码示例需要完整且可运行
- 及时更新 API 文档

## 🎯 开发优先级

当前开发重点：

1. **高优先级**
   - Bug 修复
   - 安全问题
   - 性能优化

2. **中优先级**
   - 新模型适配器
   - 工具扩展
   - 文档完善

3. **低优先级**
   - 新功能特性
   - 代码重构
   - 实验性功能

## 🤝 社区

- 加入我们的讨论：[GitHub Discussions](https://github.com/aier/ai-agent/discussions)
- 关注项目动态：[GitHub Issues](https://github.com/aier/ai-agent/issues)
- 联系维护者：<EMAIL>

## 📄 许可证

通过贡献代码，您同意您的贡献将在 MIT 许可证下发布。

---

再次感谢您的贡献！🎉
