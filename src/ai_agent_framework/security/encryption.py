"""
数据加密和解密模块

提供对称和非对称加密功能，保护敏感数据。
"""

import base64
import hashlib
import os
from typing import Optional, Tuple

from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding, rsa
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from ai_agent_framework.utils.logging_system import logging_system


class EncryptionManager:
    """加密管理器"""
    
    def __init__(self, master_key: Optional[str] = None):
        """
        初始化加密管理器
        
        Args:
            master_key: 主密钥，如果不提供则自动生成
        """
        self._logger = logging_system.get_logger("encryption_manager")
        
        if master_key:
            self._master_key = master_key.encode()
        else:
            self._master_key = self._generate_master_key()
        
        # 初始化对称加密
        self._fernet = self._create_fernet_cipher()
        
        # 生成RSA密钥对
        self._private_key, self._public_key = self._generate_rsa_keypair()
    
    def _generate_master_key(self) -> bytes:
        """生成主密钥"""
        return os.urandom(32)  # 256位密钥
    
    def _create_fernet_cipher(self) -> Fernet:
        """创建Fernet加密器"""
        # 使用PBKDF2从主密钥派生Fernet密钥
        salt = b'ai_agent_framework_salt'  # 在生产环境中应该使用随机盐
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self._master_key))
        return Fernet(key)
    
    def _generate_rsa_keypair(self) -> Tuple[rsa.RSAPrivateKey, rsa.RSAPublicKey]:
        """生成RSA密钥对"""
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
        )
        public_key = private_key.public_key()
        return private_key, public_key
    
    def encrypt_symmetric(self, data: str) -> str:
        """
        对称加密数据
        
        Args:
            data: 要加密的字符串
            
        Returns:
            str: 加密后的base64编码字符串
        """
        try:
            encrypted_data = self._fernet.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            self._logger.error(f"对称加密失败: {e}")
            raise
    
    def decrypt_symmetric(self, encrypted_data: str) -> str:
        """
        对称解密数据
        
        Args:
            encrypted_data: 加密的base64编码字符串
            
        Returns:
            str: 解密后的字符串
        """
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self._fernet.decrypt(encrypted_bytes)
            return decrypted_data.decode()
        except Exception as e:
            self._logger.error(f"对称解密失败: {e}")
            raise
    
    def encrypt_asymmetric(self, data: str, public_key: Optional[rsa.RSAPublicKey] = None) -> str:
        """
        非对称加密数据
        
        Args:
            data: 要加密的字符串
            public_key: 公钥，如果不提供则使用内置公钥
            
        Returns:
            str: 加密后的base64编码字符串
        """
        try:
            key = public_key or self._public_key
            encrypted_data = key.encrypt(
                data.encode(),
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            self._logger.error(f"非对称加密失败: {e}")
            raise
    
    def decrypt_asymmetric(self, encrypted_data: str, private_key: Optional[rsa.RSAPrivateKey] = None) -> str:
        """
        非对称解密数据
        
        Args:
            encrypted_data: 加密的base64编码字符串
            private_key: 私钥，如果不提供则使用内置私钥
            
        Returns:
            str: 解密后的字符串
        """
        try:
            key = private_key or self._private_key
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = key.decrypt(
                encrypted_bytes,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            return decrypted_data.decode()
        except Exception as e:
            self._logger.error(f"非对称解密失败: {e}")
            raise
    
    def hash_password(self, password: str, salt: Optional[bytes] = None) -> Tuple[str, str]:
        """
        哈希密码
        
        Args:
            password: 原始密码
            salt: 盐值，如果不提供则自动生成
            
        Returns:
            Tuple[str, str]: (哈希值, 盐值) 的base64编码
        """
        try:
            if salt is None:
                salt = os.urandom(32)
            
            # 使用PBKDF2进行密码哈希
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            
            password_hash = kdf.derive(password.encode())
            
            return (
                base64.urlsafe_b64encode(password_hash).decode(),
                base64.urlsafe_b64encode(salt).decode()
            )
        except Exception as e:
            self._logger.error(f"密码哈希失败: {e}")
            raise
    
    def verify_password(self, password: str, password_hash: str, salt: str) -> bool:
        """
        验证密码
        
        Args:
            password: 原始密码
            password_hash: 存储的密码哈希
            salt: 盐值
            
        Returns:
            bool: 密码是否正确
        """
        try:
            salt_bytes = base64.urlsafe_b64decode(salt.encode())
            stored_hash = base64.urlsafe_b64decode(password_hash.encode())
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt_bytes,
                iterations=100000,
            )
            
            try:
                kdf.verify(password.encode(), stored_hash)
                return True
            except Exception:
                return False
                
        except Exception as e:
            self._logger.error(f"密码验证失败: {e}")
            return False
    
    def generate_secure_token(self, length: int = 32) -> str:
        """
        生成安全令牌
        
        Args:
            length: 令牌长度（字节）
            
        Returns:
            str: base64编码的安全令牌
        """
        try:
            token = os.urandom(length)
            return base64.urlsafe_b64encode(token).decode()
        except Exception as e:
            self._logger.error(f"生成安全令牌失败: {e}")
            raise
    
    def get_public_key_pem(self) -> str:
        """
        获取公钥的PEM格式
        
        Returns:
            str: PEM格式的公钥
        """
        try:
            pem = self._public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )
            return pem.decode()
        except Exception as e:
            self._logger.error(f"获取公钥PEM失败: {e}")
            raise
    
    def get_private_key_pem(self, password: Optional[str] = None) -> str:
        """
        获取私钥的PEM格式
        
        Args:
            password: 私钥加密密码（可选）
            
        Returns:
            str: PEM格式的私钥
        """
        try:
            encryption_algorithm = serialization.NoEncryption()
            if password:
                encryption_algorithm = serialization.BestAvailableEncryption(password.encode())
            
            pem = self._private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=encryption_algorithm
            )
            return pem.decode()
        except Exception as e:
            self._logger.error(f"获取私钥PEM失败: {e}")
            raise
    
    def load_public_key_from_pem(self, pem_data: str) -> rsa.RSAPublicKey:
        """
        从PEM格式加载公钥
        
        Args:
            pem_data: PEM格式的公钥数据
            
        Returns:
            RSAPublicKey: RSA公钥对象
        """
        try:
            public_key = serialization.load_pem_public_key(pem_data.encode())
            if not isinstance(public_key, rsa.RSAPublicKey):
                raise ValueError("不是有效的RSA公钥")
            return public_key
        except Exception as e:
            self._logger.error(f"加载公钥失败: {e}")
            raise
    
    def load_private_key_from_pem(self, pem_data: str, password: Optional[str] = None) -> rsa.RSAPrivateKey:
        """
        从PEM格式加载私钥
        
        Args:
            pem_data: PEM格式的私钥数据
            password: 私钥密码（如果加密）
            
        Returns:
            RSAPrivateKey: RSA私钥对象
        """
        try:
            password_bytes = password.encode() if password else None
            private_key = serialization.load_pem_private_key(
                pem_data.encode(),
                password=password_bytes
            )
            if not isinstance(private_key, rsa.RSAPrivateKey):
                raise ValueError("不是有效的RSA私钥")
            return private_key
        except Exception as e:
            self._logger.error(f"加载私钥失败: {e}")
            raise
    
    def compute_hash(self, data: str, algorithm: str = "sha256") -> str:
        """
        计算数据哈希
        
        Args:
            data: 要哈希的数据
            algorithm: 哈希算法 (sha256, sha512, md5)
            
        Returns:
            str: 十六进制哈希值
        """
        try:
            if algorithm == "sha256":
                hash_obj = hashlib.sha256()
            elif algorithm == "sha512":
                hash_obj = hashlib.sha512()
            elif algorithm == "md5":
                hash_obj = hashlib.md5()
            else:
                raise ValueError(f"不支持的哈希算法: {algorithm}")
            
            hash_obj.update(data.encode())
            return hash_obj.hexdigest()
        except Exception as e:
            self._logger.error(f"计算哈希失败: {e}")
            raise
