"""
速率限制器

防止API滥用和DDoS攻击。
"""

import asyncio
import time
from collections import defaultdict, deque
from typing import Dict, Optional, Tuple

from ai_agent_framework.utils.logging_system import logging_system


class RateLimitExceeded(Exception):
    """速率限制超出异常"""
    
    def __init__(self, message: str, retry_after: int):
        super().__init__(message)
        self.retry_after = retry_after


class RateLimiter:
    """速率限制器"""
    
    def __init__(
        self,
        requests_per_minute: int = 60,
        burst_size: int = 10,
        cleanup_interval: int = 300,  # 5分钟
    ):
        """
        初始化速率限制器
        
        Args:
            requests_per_minute: 每分钟允许的请求数
            burst_size: 突发请求大小
            cleanup_interval: 清理间隔（秒）
        """
        self.requests_per_minute = requests_per_minute
        self.burst_size = burst_size
        self.cleanup_interval = cleanup_interval
        
        # 使用滑动窗口算法
        self._windows: Dict[str, deque] = defaultdict(deque)
        self._burst_counters: Dict[str, int] = defaultdict(int)
        self._last_cleanup = time.time()
        
        self._logger = logging_system.get_logger("rate_limiter")
    
    async def check_rate_limit(self, identifier: str) -> bool:
        """
        检查速率限制
        
        Args:
            identifier: 客户端标识符（如IP地址、用户ID等）
            
        Returns:
            bool: 是否允许请求
            
        Raises:
            RateLimitExceeded: 当超出速率限制时
        """
        current_time = time.time()
        
        # 定期清理过期数据
        if current_time - self._last_cleanup > self.cleanup_interval:
            await self._cleanup_expired_data()
            self._last_cleanup = current_time
        
        # 检查滑动窗口
        window = self._windows[identifier]
        window_start = current_time - 60  # 1分钟窗口
        
        # 移除过期的请求记录
        while window and window[0] < window_start:
            window.popleft()
        
        # 检查是否超出每分钟限制
        if len(window) >= self.requests_per_minute:
            retry_after = int(60 - (current_time - window[0]))
            self._logger.warning(
                f"客户端 {identifier} 超出每分钟请求限制 ({self.requests_per_minute})"
            )
            raise RateLimitExceeded(
                f"超出每分钟请求限制 ({self.requests_per_minute})",
                retry_after
            )
        
        # 检查突发请求限制
        burst_window_start = current_time - 10  # 10秒突发窗口
        burst_count = sum(1 for t in window if t > burst_window_start)
        
        if burst_count >= self.burst_size:
            retry_after = int(10 - (current_time - max(t for t in window if t > burst_window_start)))
            self._logger.warning(
                f"客户端 {identifier} 超出突发请求限制 ({self.burst_size})"
            )
            raise RateLimitExceeded(
                f"超出突发请求限制 ({self.burst_size})",
                retry_after
            )
        
        # 记录请求
        window.append(current_time)
        return True
    
    async def get_rate_limit_status(self, identifier: str) -> Dict[str, int]:
        """
        获取速率限制状态
        
        Args:
            identifier: 客户端标识符
            
        Returns:
            Dict: 包含限制信息的字典
        """
        current_time = time.time()
        window = self._windows[identifier]
        window_start = current_time - 60
        
        # 计算当前窗口内的请求数
        current_requests = sum(1 for t in window if t > window_start)
        
        # 计算突发窗口内的请求数
        burst_window_start = current_time - 10
        burst_requests = sum(1 for t in window if t > burst_window_start)
        
        return {
            "requests_per_minute_limit": self.requests_per_minute,
            "requests_per_minute_remaining": max(0, self.requests_per_minute - current_requests),
            "burst_limit": self.burst_size,
            "burst_remaining": max(0, self.burst_size - burst_requests),
            "reset_time": int(current_time + 60),
        }
    
    async def _cleanup_expired_data(self):
        """清理过期的数据"""
        current_time = time.time()
        cutoff_time = current_time - 3600  # 保留1小时的数据
        
        expired_identifiers = []
        
        for identifier, window in self._windows.items():
            # 移除过期的请求记录
            while window and window[0] < cutoff_time:
                window.popleft()
            
            # 如果窗口为空，标记为过期
            if not window:
                expired_identifiers.append(identifier)
        
        # 删除过期的标识符
        for identifier in expired_identifiers:
            del self._windows[identifier]
            if identifier in self._burst_counters:
                del self._burst_counters[identifier]
        
        if expired_identifiers:
            self._logger.debug(f"清理了 {len(expired_identifiers)} 个过期的客户端记录")


class AdaptiveRateLimiter(RateLimiter):
    """自适应速率限制器"""
    
    def __init__(
        self,
        base_requests_per_minute: int = 60,
        base_burst_size: int = 10,
        trust_score_multiplier: float = 2.0,
        cleanup_interval: int = 300,
    ):
        """
        初始化自适应速率限制器
        
        Args:
            base_requests_per_minute: 基础每分钟请求数
            base_burst_size: 基础突发大小
            trust_score_multiplier: 信任分数乘数
            cleanup_interval: 清理间隔
        """
        super().__init__(base_requests_per_minute, base_burst_size, cleanup_interval)
        
        self.base_requests_per_minute = base_requests_per_minute
        self.base_burst_size = base_burst_size
        self.trust_score_multiplier = trust_score_multiplier
        
        # 客户端信任分数 (0.0 - 1.0)
        self._trust_scores: Dict[str, float] = defaultdict(lambda: 0.5)
        self._violation_counts: Dict[str, int] = defaultdict(int)
    
    async def check_rate_limit(self, identifier: str) -> bool:
        """检查自适应速率限制"""
        # 根据信任分数调整限制
        trust_score = self._trust_scores[identifier]
        
        # 动态调整限制
        self.requests_per_minute = int(
            self.base_requests_per_minute * (1 + trust_score * self.trust_score_multiplier)
        )
        self.burst_size = int(
            self.base_burst_size * (1 + trust_score * self.trust_score_multiplier)
        )
        
        try:
            result = await super().check_rate_limit(identifier)
            
            # 成功请求，略微提高信任分数
            self._trust_scores[identifier] = min(1.0, trust_score + 0.001)
            
            return result
            
        except RateLimitExceeded as e:
            # 违规请求，降低信任分数
            self._violation_counts[identifier] += 1
            penalty = 0.1 * min(self._violation_counts[identifier], 5)  # 最多降低0.5
            self._trust_scores[identifier] = max(0.0, trust_score - penalty)
            
            self._logger.warning(
                f"客户端 {identifier} 违规 {self._violation_counts[identifier]} 次，"
                f"信任分数降至 {self._trust_scores[identifier]:.3f}"
            )
            
            raise e
    
    def get_trust_score(self, identifier: str) -> float:
        """获取客户端信任分数"""
        return self._trust_scores[identifier]
    
    def set_trust_score(self, identifier: str, score: float):
        """设置客户端信任分数"""
        self._trust_scores[identifier] = max(0.0, min(1.0, score))


class DistributedRateLimiter:
    """分布式速率限制器（使用Redis）"""
    
    def __init__(
        self,
        redis_client,
        requests_per_minute: int = 60,
        burst_size: int = 10,
        key_prefix: str = "rate_limit:",
    ):
        """
        初始化分布式速率限制器
        
        Args:
            redis_client: Redis客户端
            requests_per_minute: 每分钟请求数
            burst_size: 突发大小
            key_prefix: Redis键前缀
        """
        self.redis = redis_client
        self.requests_per_minute = requests_per_minute
        self.burst_size = burst_size
        self.key_prefix = key_prefix
        
        self._logger = logging_system.get_logger("distributed_rate_limiter")
    
    async def check_rate_limit(self, identifier: str) -> bool:
        """检查分布式速率限制"""
        current_time = int(time.time())
        minute_key = f"{self.key_prefix}{identifier}:minute:{current_time // 60}"
        burst_key = f"{self.key_prefix}{identifier}:burst:{current_time // 10}"
        
        try:
            # 使用Redis管道提高性能
            pipe = self.redis.pipeline()
            
            # 检查每分钟限制
            pipe.incr(minute_key)
            pipe.expire(minute_key, 60)
            
            # 检查突发限制
            pipe.incr(burst_key)
            pipe.expire(burst_key, 10)
            
            results = await pipe.execute()
            
            minute_count = results[0]
            burst_count = results[2]
            
            # 检查限制
            if minute_count > self.requests_per_minute:
                retry_after = 60 - (current_time % 60)
                raise RateLimitExceeded(
                    f"超出每分钟请求限制 ({self.requests_per_minute})",
                    retry_after
                )
            
            if burst_count > self.burst_size:
                retry_after = 10 - (current_time % 10)
                raise RateLimitExceeded(
                    f"超出突发请求限制 ({self.burst_size})",
                    retry_after
                )
            
            return True
            
        except Exception as e:
            if isinstance(e, RateLimitExceeded):
                raise
            
            self._logger.error(f"分布式速率限制检查失败: {e}")
            # 在Redis失败时，允许请求通过（fail-open策略）
            return True
    
    async def get_rate_limit_status(self, identifier: str) -> Dict[str, int]:
        """获取分布式速率限制状态"""
        current_time = int(time.time())
        minute_key = f"{self.key_prefix}{identifier}:minute:{current_time // 60}"
        burst_key = f"{self.key_prefix}{identifier}:burst:{current_time // 10}"
        
        try:
            pipe = self.redis.pipeline()
            pipe.get(minute_key)
            pipe.get(burst_key)
            results = await pipe.execute()
            
            minute_count = int(results[0] or 0)
            burst_count = int(results[1] or 0)
            
            return {
                "requests_per_minute_limit": self.requests_per_minute,
                "requests_per_minute_remaining": max(0, self.requests_per_minute - minute_count),
                "burst_limit": self.burst_size,
                "burst_remaining": max(0, self.burst_size - burst_count),
                "reset_time": (current_time // 60 + 1) * 60,
            }
            
        except Exception as e:
            self._logger.error(f"获取速率限制状态失败: {e}")
            return {
                "requests_per_minute_limit": self.requests_per_minute,
                "requests_per_minute_remaining": self.requests_per_minute,
                "burst_limit": self.burst_size,
                "burst_remaining": self.burst_size,
                "reset_time": (current_time // 60 + 1) * 60,
            }
