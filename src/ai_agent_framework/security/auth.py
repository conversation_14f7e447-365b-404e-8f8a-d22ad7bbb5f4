"""
身份验证和授权模块

提供JWT、API Key等多种身份验证方式。
"""

import hashlib
import hmac
import json
import time
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

import jwt
from pydantic import BaseModel

from ai_agent_framework.utils.logging_system import logging_system


class User(BaseModel):
    """用户模型"""
    
    id: str
    username: str
    email: Optional[str] = None
    roles: List[str] = []
    permissions: List[str] = []
    is_active: bool = True
    created_at: datetime
    last_login: Optional[datetime] = None


class AuthResult(BaseModel):
    """认证结果"""
    
    success: bool
    user: Optional[User] = None
    token: Optional[str] = None
    error: Optional[str] = None
    expires_at: Optional[datetime] = None


class AuthProvider(ABC):
    """认证提供者抽象基类"""
    
    @abstractmethod
    async def authenticate(self, credentials: Dict[str, Any]) -> AuthResult:
        """认证用户"""
        pass
    
    @abstractmethod
    async def validate_token(self, token: str) -> AuthResult:
        """验证令牌"""
        pass
    
    @abstractmethod
    async def revoke_token(self, token: str) -> bool:
        """撤销令牌"""
        pass


class JWTAuth(AuthProvider):
    """JWT身份验证"""
    
    def __init__(
        self,
        secret_key: str,
        algorithm: str = "HS256",
        expiration_hours: int = 24,
        issuer: str = "ai-agent-framework",
    ):
        """
        初始化JWT认证
        
        Args:
            secret_key: JWT签名密钥
            algorithm: 签名算法
            expiration_hours: 令牌过期时间（小时）
            issuer: 令牌发行者
        """
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.expiration_hours = expiration_hours
        self.issuer = issuer
        
        self._logger = logging_system.get_logger("jwt_auth")
        self._revoked_tokens: set = set()  # 简单的令牌撤销列表
    
    async def authenticate(self, credentials: Dict[str, Any]) -> AuthResult:
        """
        认证用户并生成JWT令牌
        
        Args:
            credentials: 包含username和password的凭据
            
        Returns:
            AuthResult: 认证结果
        """
        try:
            username = credentials.get("username")
            password = credentials.get("password")
            
            if not username or not password:
                return AuthResult(
                    success=False,
                    error="用户名和密码不能为空"
                )
            
            # 这里应该连接到实际的用户数据库
            # 为了演示，我们使用简单的硬编码验证
            if await self._verify_user(username, password):
                user = User(
                    id=f"user_{username}",
                    username=username,
                    email=f"{username}@example.com",
                    roles=["user"],
                    permissions=["read", "write"],
                    created_at=datetime.now(),
                    last_login=datetime.now()
                )
                
                # 生成JWT令牌
                token = self._generate_token(user)
                expires_at = datetime.now() + timedelta(hours=self.expiration_hours)
                
                self._logger.info(f"用户 {username} 认证成功")
                
                return AuthResult(
                    success=True,
                    user=user,
                    token=token,
                    expires_at=expires_at
                )
            else:
                self._logger.warning(f"用户 {username} 认证失败")
                return AuthResult(
                    success=False,
                    error="用户名或密码错误"
                )
                
        except Exception as e:
            self._logger.error(f"认证过程出错: {e}")
            return AuthResult(
                success=False,
                error=f"认证失败: {str(e)}"
            )
    
    async def validate_token(self, token: str) -> AuthResult:
        """
        验证JWT令牌
        
        Args:
            token: JWT令牌
            
        Returns:
            AuthResult: 验证结果
        """
        try:
            # 检查令牌是否已被撤销
            if token in self._revoked_tokens:
                return AuthResult(
                    success=False,
                    error="令牌已被撤销"
                )
            
            # 解码JWT令牌
            payload = jwt.decode(
                token,
                self.secret_key,
                algorithms=[self.algorithm],
                issuer=self.issuer
            )
            
            # 检查令牌是否过期
            exp = payload.get("exp")
            if exp and datetime.fromtimestamp(exp) < datetime.now():
                return AuthResult(
                    success=False,
                    error="令牌已过期"
                )
            
            # 重建用户对象
            user_data = payload.get("user")
            if not user_data:
                return AuthResult(
                    success=False,
                    error="令牌格式无效"
                )
            
            user = User(**user_data)
            
            return AuthResult(
                success=True,
                user=user,
                token=token
            )
            
        except jwt.ExpiredSignatureError:
            return AuthResult(
                success=False,
                error="令牌已过期"
            )
        except jwt.InvalidTokenError as e:
            return AuthResult(
                success=False,
                error=f"无效令牌: {str(e)}"
            )
        except Exception as e:
            self._logger.error(f"令牌验证出错: {e}")
            return AuthResult(
                success=False,
                error=f"令牌验证失败: {str(e)}"
            )
    
    async def revoke_token(self, token: str) -> bool:
        """
        撤销JWT令牌
        
        Args:
            token: 要撤销的令牌
            
        Returns:
            bool: 撤销是否成功
        """
        try:
            # 将令牌添加到撤销列表
            self._revoked_tokens.add(token)
            self._logger.info("令牌已撤销")
            return True
        except Exception as e:
            self._logger.error(f"撤销令牌失败: {e}")
            return False
    
    def _generate_token(self, user: User) -> str:
        """生成JWT令牌"""
        now = datetime.now()

        # 转换用户数据为可序列化的格式
        user_data = user.model_dump()
        # 转换datetime对象为ISO字符串
        if "created_at" in user_data:
            user_data["created_at"] = user_data["created_at"].isoformat()
        if "last_login" in user_data and user_data["last_login"]:
            user_data["last_login"] = user_data["last_login"].isoformat()

        payload = {
            "iss": self.issuer,
            "sub": user.id,
            "iat": int(now.timestamp()),
            "exp": int((now + timedelta(hours=self.expiration_hours)).timestamp()),
            "user": user_data
        }

        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    async def _verify_user(self, username: str, password: str) -> bool:
        """验证用户凭据（示例实现）"""
        # 这里应该连接到实际的用户数据库
        # 为了演示，我们使用简单的硬编码验证
        test_users = {
            "admin": "admin123",
            "user": "user123",
            "test": "test123"
        }
        
        return test_users.get(username) == password


class APIKeyAuth(AuthProvider):
    """API Key身份验证"""
    
    def __init__(self, api_keys: Dict[str, Dict[str, Any]]):
        """
        初始化API Key认证
        
        Args:
            api_keys: API密钥配置，格式为 {key: {user_info}}
        """
        self.api_keys = api_keys
        self._logger = logging_system.get_logger("api_key_auth")
    
    async def authenticate(self, credentials: Dict[str, Any]) -> AuthResult:
        """
        使用API Key认证
        
        Args:
            credentials: 包含api_key的凭据
            
        Returns:
            AuthResult: 认证结果
        """
        try:
            api_key = credentials.get("api_key")
            
            if not api_key:
                return AuthResult(
                    success=False,
                    error="API Key不能为空"
                )
            
            # 验证API Key
            if api_key in self.api_keys:
                user_info = self.api_keys[api_key]
                user = User(
                    id=user_info["id"],
                    username=user_info["username"],
                    email=user_info.get("email"),
                    roles=user_info.get("roles", []),
                    permissions=user_info.get("permissions", []),
                    created_at=datetime.now(),
                    last_login=datetime.now()
                )
                
                self._logger.info(f"API Key认证成功: {user.username}")
                
                return AuthResult(
                    success=True,
                    user=user,
                    token=api_key
                )
            else:
                self._logger.warning(f"无效的API Key: {api_key[:8]}...")
                return AuthResult(
                    success=False,
                    error="无效的API Key"
                )
                
        except Exception as e:
            self._logger.error(f"API Key认证出错: {e}")
            return AuthResult(
                success=False,
                error=f"认证失败: {str(e)}"
            )
    
    async def validate_token(self, token: str) -> AuthResult:
        """验证API Key令牌"""
        return await self.authenticate({"api_key": token})
    
    async def revoke_token(self, token: str) -> bool:
        """撤销API Key"""
        try:
            if token in self.api_keys:
                del self.api_keys[token]
                self._logger.info("API Key已撤销")
                return True
            return False
        except Exception as e:
            self._logger.error(f"撤销API Key失败: {e}")
            return False


class AuthManager:
    """认证管理器"""
    
    def __init__(self, providers: List[AuthProvider]):
        """
        初始化认证管理器
        
        Args:
            providers: 认证提供者列表
        """
        self.providers = providers
        self._logger = logging_system.get_logger("auth_manager")
    
    async def authenticate(
        self,
        credentials: Dict[str, Any],
        provider_type: Optional[str] = None
    ) -> AuthResult:
        """
        认证用户
        
        Args:
            credentials: 用户凭据
            provider_type: 指定的认证提供者类型
            
        Returns:
            AuthResult: 认证结果
        """
        if provider_type:
            # 使用指定的认证提供者
            for provider in self.providers:
                if provider.__class__.__name__.lower().startswith(provider_type.lower()):
                    return await provider.authenticate(credentials)
            
            return AuthResult(
                success=False,
                error=f"未找到认证提供者: {provider_type}"
            )
        else:
            # 尝试所有认证提供者
            for provider in self.providers:
                result = await provider.authenticate(credentials)
                if result.success:
                    return result
            
            return AuthResult(
                success=False,
                error="所有认证方式都失败"
            )
    
    async def validate_token(self, token: str) -> AuthResult:
        """验证令牌"""
        for provider in self.providers:
            result = await provider.validate_token(token)
            if result.success:
                return result
        
        return AuthResult(
            success=False,
            error="令牌验证失败"
        )
    
    def check_permission(self, user: User, required_permission: str) -> bool:
        """检查用户权限"""
        return required_permission in user.permissions or "admin" in user.roles
