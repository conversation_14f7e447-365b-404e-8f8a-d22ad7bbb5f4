"""
MCP协议适配器

实现MCP协议的通信和消息处理功能。
"""

import asyncio
import json
import logging
from typing import Any, AsyncIterator, Dict, List, Optional
from urllib.parse import urlparse

try:
    import aiohttp
except ImportError:
    aiohttp = None

try:
    import websockets
    from websockets.exceptions import ConnectionClosed, WebSocketException
except ImportError:
    websockets = None
    ConnectionClosed = Exception
    WebSocketException = Exception

from ai_agent_framework.utils.logging_system import logging_system

from .mcp_service import (
    MCPCapability,
    MCPConnectionError,
    MCPProtocolError,
    MCPRequest,
    MCPResponse,
    MCPService,
)


class MCPAdapter:
    """MCP协议适配器"""

    def __init__(
        self,
        connection_timeout: int = 30,
        request_timeout: int = 60,
        max_retries: int = 3,
    ):
        """
        初始化MCP适配器

        Args:
            connection_timeout: 连接超时时间（秒）
            request_timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
        """
        self.connection_timeout = connection_timeout
        self.request_timeout = request_timeout
        self.max_retries = max_retries

        self._logger = logging_system.get_logger("mcp_adapter")
        self._connections: Dict[str, Any] = {}
        self._session: Optional[aiohttp.ClientSession] = None

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()

    async def start(self) -> None:
        """启动适配器"""
        if aiohttp is None:
            raise ImportError("需要安装aiohttp: pip install aiohttp")

        if not self._session:
            self._session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.request_timeout)
            )
        self._logger.info("MCP适配器已启动")

    async def close(self) -> None:
        """关闭适配器"""
        # 关闭所有连接
        for connection in self._connections.values():
            try:
                if hasattr(connection, "close"):
                    await connection.close()
            except Exception as e:
                self._logger.warning(f"关闭连接时出错: {e}")

        self._connections.clear()

        # 关闭HTTP会话
        if self._session:
            await self._session.close()
            self._session = None

        self._logger.info("MCP适配器已关闭")

    async def connect_websocket(self, url: str, service_id: str) -> bool:
        """
        连接到WebSocket MCP服务

        Args:
            url: WebSocket URL
            service_id: 服务ID

        Returns:
            bool: 连接是否成功
        """
        if websockets is None:
            raise ImportError("需要安装websockets: pip install websockets")

        try:
            websocket = await websockets.connect(
                url,
                timeout=self.connection_timeout,
                ping_interval=20,
                ping_timeout=10,
            )

            self._connections[service_id] = {
                "type": "websocket",
                "connection": websocket,
                "url": url,
            }

            self._logger.info(f"已连接到WebSocket MCP服务: {url}")
            return True

        except Exception as e:
            self._logger.error(f"连接WebSocket MCP服务失败: {e}")
            raise MCPConnectionError(f"WebSocket连接失败: {str(e)}")

    async def connect_http(self, base_url: str, service_id: str) -> bool:
        """
        连接到HTTP MCP服务

        Args:
            base_url: HTTP基础URL
            service_id: 服务ID

        Returns:
            bool: 连接是否成功
        """
        try:
            # 测试连接
            async with self._session.get(f"{base_url}/health") as response:
                if response.status == 200:
                    self._connections[service_id] = {
                        "type": "http",
                        "base_url": base_url,
                    }

                    self._logger.info(f"已连接到HTTP MCP服务: {base_url}")
                    return True
                else:
                    raise MCPConnectionError(f"HTTP服务健康检查失败: {response.status}")

        except Exception as e:
            self._logger.error(f"连接HTTP MCP服务失败: {e}")
            raise MCPConnectionError(f"HTTP连接失败: {str(e)}")

    async def disconnect(self, service_id: str) -> bool:
        """
        断开与MCP服务的连接

        Args:
            service_id: 服务ID

        Returns:
            bool: 断开是否成功
        """
        if service_id not in self._connections:
            return True

        try:
            connection_info = self._connections[service_id]

            if connection_info["type"] == "websocket":
                websocket = connection_info["connection"]
                await websocket.close()

            del self._connections[service_id]
            self._logger.info(f"已断开MCP服务连接: {service_id}")
            return True

        except Exception as e:
            self._logger.error(f"断开MCP服务连接失败: {e}")
            return False

    async def send_request(
        self,
        service_id: str,
        request: MCPRequest,
    ) -> MCPResponse:
        """
        发送MCP请求

        Args:
            service_id: 服务ID
            request: MCP请求

        Returns:
            MCPResponse: MCP响应
        """
        if service_id not in self._connections:
            raise MCPConnectionError(f"未找到服务连接: {service_id}")

        connection_info = self._connections[service_id]

        for attempt in range(self.max_retries):
            try:
                if connection_info["type"] == "websocket":
                    return await self._send_websocket_request(connection_info, request)
                elif connection_info["type"] == "http":
                    return await self._send_http_request(connection_info, request)
                else:
                    raise MCPProtocolError(
                        f"不支持的连接类型: {connection_info['type']}"
                    )

            except Exception as e:
                if attempt == self.max_retries - 1:
                    raise

                self._logger.warning(
                    f"请求失败，正在重试 ({attempt + 1}/{self.max_retries}): {e}"
                )
                await asyncio.sleep(2**attempt)  # 指数退避

    async def _send_websocket_request(
        self,
        connection_info: Dict[str, Any],
        request: MCPRequest,
    ) -> MCPResponse:
        """发送WebSocket请求"""
        websocket = connection_info["connection"]

        try:
            # 发送请求
            request_data = request.model_dump_json()
            await websocket.send(request_data)

            # 接收响应
            response_data = await asyncio.wait_for(
                websocket.recv(), timeout=self.request_timeout
            )

            response_dict = json.loads(response_data)
            return MCPResponse(**response_dict)

        except ConnectionClosed:
            raise MCPConnectionError("WebSocket连接已关闭")
        except asyncio.TimeoutError:
            raise MCPConnectionError("WebSocket请求超时")
        except json.JSONDecodeError as e:
            raise MCPProtocolError(f"响应JSON解析失败: {e}")

    async def _send_http_request(
        self,
        connection_info: Dict[str, Any],
        request: MCPRequest,
    ) -> MCPResponse:
        """发送HTTP请求"""
        base_url = connection_info["base_url"]
        url = f"{base_url}/mcp"

        try:
            request_data = request.model_dump()

            async with self._session.post(url, json=request_data) as response:
                if response.status == 200:
                    response_data = await response.json()
                    return MCPResponse(**response_data)
                else:
                    error_text = await response.text()
                    raise MCPProtocolError(
                        f"HTTP请求失败: {response.status} - {error_text}"
                    )

        except aiohttp.ClientError as e:
            raise MCPConnectionError(f"HTTP请求错误: {e}")
        except json.JSONDecodeError as e:
            raise MCPProtocolError(f"响应JSON解析失败: {e}")

    async def discover_capabilities(self, service_id: str) -> List[MCPCapability]:
        """
        发现服务能力

        Args:
            service_id: 服务ID

        Returns:
            List[MCPCapability]: 服务能力列表
        """
        request = MCPRequest(method="discover_capabilities", params={})

        response = await self.send_request(service_id, request)

        if response.is_error:
            raise MCPProtocolError(f"发现服务能力失败: {response.error}")

        capabilities = []
        for cap_str in response.result.get("capabilities", []):
            try:
                capabilities.append(MCPCapability(cap_str))
            except ValueError:
                self._logger.warning(f"未知的服务能力: {cap_str}")

        return capabilities

    def is_connected(self, service_id: str) -> bool:
        """检查是否已连接到服务"""
        return service_id in self._connections

    def get_connection_info(self, service_id: str) -> Optional[Dict[str, Any]]:
        """获取连接信息"""
        return self._connections.get(service_id)
