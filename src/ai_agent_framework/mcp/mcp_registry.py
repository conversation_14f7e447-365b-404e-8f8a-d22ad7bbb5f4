"""
MCP服务注册表

管理MCP服务的注册、发现和生命周期。
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set

from ai_agent_framework.utils.logging_system import logging_system

from .mcp_adapter import MCPAdapter
from .mcp_service import (
    MCPCapability,
    MCPService,
    MCPServiceError,
    MCPServiceInfo,
    MCPServiceStatus,
)


class MCPServiceRegistry:
    """MCP服务注册表"""

    def __init__(
        self,
        health_check_interval: int = 60,
        service_timeout: int = 300,
    ):
        """
        初始化服务注册表

        Args:
            health_check_interval: 健康检查间隔（秒）
            service_timeout: 服务超时时间（秒）
        """
        self.health_check_interval = health_check_interval
        self.service_timeout = service_timeout

        self._logger = logging_system.get_logger("mcp_registry")
        self._services: Dict[str, MCPService] = {}
        self._service_info: Dict[str, MCPServiceInfo] = {}
        self._adapter = MCPAdapter()
        self._health_check_task: Optional[asyncio.Task] = None
        self._running = False

    async def start(self) -> None:
        """启动服务注册表"""
        if self._running:
            return

        await self._adapter.start()
        self._running = True

        # 启动健康检查任务
        self._health_check_task = asyncio.create_task(self._health_check_loop())

        self._logger.info("MCP服务注册表已启动")

    async def stop(self) -> None:
        """停止服务注册表"""
        if not self._running:
            return

        self._running = False

        # 停止健康检查任务
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass

        # 停止所有服务
        for service in self._services.values():
            try:
                await service.stop()
            except Exception as e:
                self._logger.warning(f"停止服务时出错: {e}")

        await self._adapter.close()

        self._logger.info("MCP服务注册表已停止")

    async def register_service(
        self,
        service: MCPService,
        endpoint: Optional[str] = None,
    ) -> bool:
        """
        注册MCP服务

        Args:
            service: MCP服务实例
            endpoint: 服务端点（可选）

        Returns:
            bool: 注册是否成功
        """
        try:
            # 检查服务是否已存在
            if service.name in self._services:
                raise MCPServiceError(f"服务已存在: {service.name}")

            # 启动服务
            if not await service.start():
                raise MCPServiceError(f"服务启动失败: {service.name}")

            # 注册服务
            self._services[service.name] = service
            self._service_info[service.name] = MCPServiceInfo(
                name=service.name,
                description=service.description,
                version=service.version,
                capabilities=[cap.value for cap in service.capabilities],
                status=MCPServiceStatus.RUNNING,
                endpoint=endpoint,
                last_health_check=datetime.now(),
            )

            self._logger.info(f"已注册MCP服务: {service.name}")
            return True

        except Exception as e:
            self._logger.error(f"注册MCP服务失败: {e}")
            return False

    async def unregister_service(self, service_name: str) -> bool:
        """
        注销MCP服务

        Args:
            service_name: 服务名称

        Returns:
            bool: 注销是否成功
        """
        try:
            if service_name not in self._services:
                self._logger.warning(f"服务不存在: {service_name}")
                return True

            # 停止服务
            service = self._services[service_name]
            await service.stop()

            # 移除服务
            del self._services[service_name]
            del self._service_info[service_name]

            self._logger.info(f"已注销MCP服务: {service_name}")
            return True

        except Exception as e:
            self._logger.error(f"注销MCP服务失败: {e}")
            return False

    def get_service(self, service_name: str) -> Optional[MCPService]:
        """获取服务实例"""
        return self._services.get(service_name)

    def get_service_info(self, service_name: str) -> Optional[MCPServiceInfo]:
        """获取服务信息"""
        return self._service_info.get(service_name)

    def list_services(self) -> List[MCPServiceInfo]:
        """列出所有服务"""
        return list(self._service_info.values())

    def discover_services(self, capability: MCPCapability) -> List[MCPServiceInfo]:
        """
        发现具有特定能力的服务

        Args:
            capability: 所需能力

        Returns:
            List[MCPServiceInfo]: 匹配的服务列表
        """
        matching_services = []

        for service_info in self._service_info.values():
            if capability.value in service_info.capabilities:
                matching_services.append(service_info)

        return matching_services

    def discover_services_by_capabilities(
        self,
        capabilities: List[MCPCapability],
        require_all: bool = True,
    ) -> List[MCPServiceInfo]:
        """
        发现具有多个能力的服务

        Args:
            capabilities: 所需能力列表
            require_all: 是否需要所有能力（True）还是任一能力（False）

        Returns:
            List[MCPServiceInfo]: 匹配的服务列表
        """
        matching_services = []
        capability_values = [cap.value for cap in capabilities]

        for service_info in self._service_info.values():
            service_caps = set(service_info.capabilities)
            required_caps = set(capability_values)

            if require_all:
                # 需要所有能力
                if required_caps.issubset(service_caps):
                    matching_services.append(service_info)
            else:
                # 需要任一能力
                if required_caps.intersection(service_caps):
                    matching_services.append(service_info)

        return matching_services

    async def health_check(self, service_name: Optional[str] = None) -> Dict[str, bool]:
        """
        执行健康检查

        Args:
            service_name: 特定服务名称，None表示检查所有服务

        Returns:
            Dict[str, bool]: 服务健康状态
        """
        results = {}

        services_to_check = (
            {service_name: self._services[service_name]}
            if service_name and service_name in self._services
            else self._services
        )

        for name, service in services_to_check.items():
            try:
                is_healthy = await service.health_check()
                results[name] = is_healthy

                # 更新服务信息
                if name in self._service_info:
                    self._service_info[name].last_health_check = datetime.now()
                    if is_healthy:
                        self._service_info[name].status = MCPServiceStatus.RUNNING
                    else:
                        self._service_info[name].status = MCPServiceStatus.ERROR

            except Exception as e:
                self._logger.error(f"服务健康检查失败 {name}: {e}")
                results[name] = False

                if name in self._service_info:
                    self._service_info[name].status = MCPServiceStatus.ERROR

        return results

    async def _health_check_loop(self) -> None:
        """健康检查循环"""
        while self._running:
            try:
                await asyncio.sleep(self.health_check_interval)

                if not self._running:
                    break

                # 执行健康检查
                health_results = await self.health_check()

                # 记录不健康的服务
                unhealthy_services = [
                    name
                    for name, is_healthy in health_results.items()
                    if not is_healthy
                ]

                if unhealthy_services:
                    self._logger.warning(f"发现不健康的服务: {unhealthy_services}")

                # 清理超时的服务
                await self._cleanup_timeout_services()

            except asyncio.CancelledError:
                break
            except Exception as e:
                self._logger.error(f"健康检查循环出错: {e}")

    async def _cleanup_timeout_services(self) -> None:
        """清理超时的服务"""
        current_time = datetime.now()
        timeout_threshold = current_time - timedelta(seconds=self.service_timeout)

        timeout_services = []

        for name, service_info in self._service_info.items():
            if (
                service_info.last_health_check
                and service_info.last_health_check < timeout_threshold
            ):
                timeout_services.append(name)

        for service_name in timeout_services:
            self._logger.warning(f"服务超时，正在移除: {service_name}")
            await self.unregister_service(service_name)

    def get_statistics(self) -> Dict[str, any]:
        """获取注册表统计信息"""
        total_services = len(self._services)
        running_services = sum(
            1
            for info in self._service_info.values()
            if info.status == MCPServiceStatus.RUNNING
        )
        error_services = sum(
            1
            for info in self._service_info.values()
            if info.status == MCPServiceStatus.ERROR
        )

        # 统计能力分布
        capability_count = {}
        for service_info in self._service_info.values():
            for cap in service_info.capabilities:
                capability_count[cap] = capability_count.get(cap, 0) + 1

        return {
            "total_services": total_services,
            "running_services": running_services,
            "error_services": error_services,
            "capability_distribution": capability_count,
        }
