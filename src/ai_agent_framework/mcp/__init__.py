"""
AI Agent Framework - MCP (Model Context Protocol) 模块

本模块实现了MCP协议的支持，包括：
- MCP协议适配器
- MCP服务注册表
- 常用MCP服务集成
- MCP服务管理和监控

MCP协议是一个用于模型上下文管理的标准协议，允许AI模型与外部服务进行标准化通信。
"""

from .mcp_adapter import MCPAdapter
from .mcp_registry import MCPServiceRegistry
from .mcp_service import MCPService, MCPCapability, MCPRequest, MCPResponse

__all__ = [
    "MCPAdapter",
    "MCPServiceRegistry",
    "MCPService",
    "MCPCapability",
    "MCPRequest",
    "MCPResponse",
]
