"""
数据库查询MCP服务

提供多种数据库的SQL查询执行和结果格式化功能。
"""

import asyncio
import json
from typing import Any, Dict, List, Optional
from datetime import datetime

from ai_agent_framework.utils.logging_system import logging_system

from ..mcp_service import (
    MCPCapability,
    MCPRequest,
    MCPResponse,
    MCPService,
    MCPServiceError,
)


class DatabaseMCPService(MCPService):
    """数据库查询MCP服务"""
    
    def __init__(
        self,
        connections: Dict[str, Dict[str, Any]],
        max_query_timeout: int = 30,
        max_result_rows: int = 1000,
    ):
        """
        初始化数据库MCP服务
        
        Args:
            connections: 数据库连接配置字典
            max_query_timeout: 最大查询超时时间（秒）
            max_result_rows: 最大结果行数
        """
        capabilities = [
            MCPCapability.DB_QUERY,
            MCPCapability.DB_EXECUTE,
            MCPCapability.DB_SCHEMA,
            MCPCapability.DB_CONNECTIONS,
        ]
        
        super().__init__(
            name="database",
            description="数据库查询和操作服务",
            capabilities=capabilities,
            version="1.0.0",
        )
        
        self.connections = connections
        self.max_query_timeout = max_query_timeout
        self.max_result_rows = max_result_rows
        
        self._logger = logging_system.get_logger("database_mcp")
        self._active_connections: Dict[str, Any] = {}
    
    async def start(self) -> bool:
        """启动服务"""
        try:
            # 初始化数据库连接
            for conn_name, config in self.connections.items():
                await self._initialize_connection(conn_name, config)
            
            self.is_running = True
            self._logger.info(f"数据库MCP服务已启动，支持{len(self.connections)}个连接")
            return True
            
        except Exception as e:
            self._logger.error(f"启动数据库MCP服务失败: {e}")
            return False
    
    async def stop(self) -> bool:
        """停止服务"""
        try:
            # 关闭所有数据库连接
            for conn_name, connection in self._active_connections.items():
                await self._close_connection(conn_name, connection)
            
            self._active_connections.clear()
            self.is_running = False
            self._logger.info("数据库MCP服务已停止")
            return True
            
        except Exception as e:
            self._logger.error(f"停止数据库MCP服务失败: {e}")
            return False
    
    async def health_check(self) -> bool:
        """健康检查"""
        if not self.is_running:
            return False
        
        try:
            # 检查所有连接的健康状态
            for conn_name in self.connections.keys():
                if not await self._check_connection_health(conn_name):
                    return False
            return True
        except Exception:
            return False
    
    async def handle_request(self, request: MCPRequest) -> MCPResponse:
        """处理MCP请求"""
        try:
            method = request.method
            params = request.params
            
            if method == "db.query":
                result = await self._execute_query(params)
            elif method == "db.execute":
                result = await self._execute_statement(params)
            elif method == "db.schema":
                result = await self._get_schema(params)
            elif method == "db.connections":
                result = await self._list_connections()
            elif method == "discover_capabilities":
                result = {"capabilities": [cap.value for cap in self.capabilities]}
            else:
                raise MCPServiceError(f"不支持的方法: {method}")
            
            return MCPResponse(
                id=request.id,
                result=result,
            )
            
        except Exception as e:
            self._logger.error(f"处理数据库请求失败: {e}")
            return MCPResponse(
                id=request.id,
                error={
                    "code": "DATABASE_ERROR",
                    "message": str(e),
                }
            )
    
    async def _initialize_connection(self, conn_name: str, config: Dict[str, Any]):
        """初始化数据库连接"""
        db_type = config.get("type", "sqlite")
        
        if db_type == "sqlite":
            import sqlite3
            db_path = config.get("path", ":memory:")
            connection = sqlite3.connect(db_path)
            connection.row_factory = sqlite3.Row
            
        elif db_type == "postgresql":
            try:
                import asyncpg
                connection = await asyncpg.connect(
                    host=config.get("host", "localhost"),
                    port=config.get("port", 5432),
                    user=config.get("user"),
                    password=config.get("password"),
                    database=config.get("database"),
                )
            except ImportError:
                raise MCPServiceError("需要安装asyncpg: pip install asyncpg")
                
        elif db_type == "mysql":
            try:
                import aiomysql
                connection = await aiomysql.connect(
                    host=config.get("host", "localhost"),
                    port=config.get("port", 3306),
                    user=config.get("user"),
                    password=config.get("password"),
                    db=config.get("database"),
                )
            except ImportError:
                raise MCPServiceError("需要安装aiomysql: pip install aiomysql")
                
        else:
            raise MCPServiceError(f"不支持的数据库类型: {db_type}")
        
        self._active_connections[conn_name] = {
            "connection": connection,
            "type": db_type,
            "config": config,
        }
        
        self._logger.info(f"已初始化{db_type}数据库连接: {conn_name}")
    
    async def _close_connection(self, conn_name: str, connection_info: Dict[str, Any]):
        """关闭数据库连接"""
        try:
            connection = connection_info["connection"]
            db_type = connection_info["type"]
            
            if db_type == "sqlite":
                connection.close()
            elif db_type in ["postgresql", "mysql"]:
                await connection.close()
                
        except Exception as e:
            self._logger.warning(f"关闭连接{conn_name}时出错: {e}")
    
    async def _check_connection_health(self, conn_name: str) -> bool:
        """检查连接健康状态"""
        try:
            if conn_name not in self._active_connections:
                return False
            
            connection_info = self._active_connections[conn_name]
            connection = connection_info["connection"]
            db_type = connection_info["type"]
            
            if db_type == "sqlite":
                cursor = connection.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                return True
                
            elif db_type == "postgresql":
                await connection.fetchval("SELECT 1")
                return True
                
            elif db_type == "mysql":
                async with connection.cursor() as cursor:
                    await cursor.execute("SELECT 1")
                    await cursor.fetchone()
                return True
                
            return False
            
        except Exception:
            return False
    
    async def _execute_query(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行查询"""
        connection_name = params.get("connection", "default")
        sql = params.get("sql")
        limit = params.get("limit", self.max_result_rows)
        
        if not sql:
            raise MCPServiceError("SQL语句不能为空")
        
        if connection_name not in self._active_connections:
            raise MCPServiceError(f"连接不存在: {connection_name}")
        
        connection_info = self._active_connections[connection_name]
        connection = connection_info["connection"]
        db_type = connection_info["type"]
        
        try:
            start_time = datetime.now()
            
            if db_type == "sqlite":
                cursor = connection.cursor()
                cursor.execute(sql)
                rows = cursor.fetchmany(limit)
                
                # 转换为字典列表
                columns = [description[0] for description in cursor.description] if cursor.description else []
                results = [dict(zip(columns, row)) for row in rows]
                
            elif db_type == "postgresql":
                rows = await asyncio.wait_for(
                    connection.fetch(sql),
                    timeout=self.max_query_timeout
                )
                results = [dict(row) for row in rows[:limit]]
                columns = list(rows[0].keys()) if rows else []
                
            elif db_type == "mysql":
                async with connection.cursor() as cursor:
                    await asyncio.wait_for(
                        cursor.execute(sql),
                        timeout=self.max_query_timeout
                    )
                    rows = await cursor.fetchmany(limit)
                    
                    # 获取列名
                    columns = [desc[0] for desc in cursor.description] if cursor.description else []
                    results = [dict(zip(columns, row)) for row in rows]
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return {
                "connection": connection_name,
                "sql": sql,
                "results": results,
                "columns": columns,
                "row_count": len(results),
                "execution_time": execution_time,
                "truncated": len(results) >= limit,
            }
            
        except asyncio.TimeoutError:
            raise MCPServiceError(f"查询超时（>{self.max_query_timeout}秒）")
        except Exception as e:
            raise MCPServiceError(f"查询执行失败: {str(e)}")
    
    async def _execute_statement(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行SQL语句（INSERT、UPDATE、DELETE等）"""
        connection_name = params.get("connection", "default")
        sql = params.get("sql")
        
        if not sql:
            raise MCPServiceError("SQL语句不能为空")
        
        if connection_name not in self._active_connections:
            raise MCPServiceError(f"连接不存在: {connection_name}")
        
        connection_info = self._active_connections[connection_name]
        connection = connection_info["connection"]
        db_type = connection_info["type"]
        
        try:
            start_time = datetime.now()
            
            if db_type == "sqlite":
                cursor = connection.cursor()
                cursor.execute(sql)
                connection.commit()
                affected_rows = cursor.rowcount
                
            elif db_type == "postgresql":
                result = await asyncio.wait_for(
                    connection.execute(sql),
                    timeout=self.max_query_timeout
                )
                # PostgreSQL返回格式如 "INSERT 0 5"
                affected_rows = int(result.split()[-1]) if result else 0
                
            elif db_type == "mysql":
                async with connection.cursor() as cursor:
                    await asyncio.wait_for(
                        cursor.execute(sql),
                        timeout=self.max_query_timeout
                    )
                    affected_rows = cursor.rowcount
                await connection.commit()
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return {
                "connection": connection_name,
                "sql": sql,
                "affected_rows": affected_rows,
                "execution_time": execution_time,
                "success": True,
            }
            
        except asyncio.TimeoutError:
            raise MCPServiceError(f"语句执行超时（>{self.max_query_timeout}秒）")
        except Exception as e:
            raise MCPServiceError(f"语句执行失败: {str(e)}")
    
    async def _get_schema(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取数据库架构信息"""
        connection_name = params.get("connection", "default")
        
        if connection_name not in self._active_connections:
            raise MCPServiceError(f"连接不存在: {connection_name}")
        
        connection_info = self._active_connections[connection_name]
        db_type = connection_info["type"]
        
        try:
            if db_type == "sqlite":
                schema_sql = "SELECT name, sql FROM sqlite_master WHERE type='table'"
            elif db_type == "postgresql":
                schema_sql = """
                SELECT table_name, column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_schema = 'public'
                ORDER BY table_name, ordinal_position
                """
            elif db_type == "mysql":
                schema_sql = """
                SELECT table_name, column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_schema = DATABASE()
                ORDER BY table_name, ordinal_position
                """
            
            # 执行架构查询
            schema_result = await self._execute_query({
                "connection": connection_name,
                "sql": schema_sql,
                "limit": 10000,  # 架构信息通常不会太多
            })
            
            return {
                "connection": connection_name,
                "database_type": db_type,
                "schema": schema_result["results"],
            }
            
        except Exception as e:
            raise MCPServiceError(f"获取架构信息失败: {str(e)}")
    
    async def _list_connections(self) -> Dict[str, Any]:
        """列出所有可用连接"""
        connections = []
        
        for conn_name, connection_info in self._active_connections.items():
            config = connection_info["config"]
            connections.append({
                "name": conn_name,
                "type": connection_info["type"],
                "host": config.get("host", "localhost"),
                "database": config.get("database", config.get("path")),
                "is_healthy": await self._check_connection_health(conn_name),
            })
        
        return {
            "connections": connections,
            "total_count": len(connections),
        }
