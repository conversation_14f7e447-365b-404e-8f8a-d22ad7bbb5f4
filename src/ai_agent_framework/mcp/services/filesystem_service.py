"""
文件系统MCP服务

提供文件系统操作的MCP服务实现。
"""

import os
import json
import glob
import asyncio
from pathlib import Path
from typing import Any, Dict, List, Optional
from datetime import datetime

from ai_agent_framework.utils.logging_system import logging_system

from ..mcp_service import (
    MCPCapability,
    MCPRequest,
    MCPResponse,
    MCPService,
    MCPServiceError,
)


class FileSystemMCPService(MCPService):
    """文件系统MCP服务"""

    def __init__(
        self,
        allowed_paths: Optional[List[str]] = None,
        max_file_size: int = 10 * 1024 * 1024,  # 10MB
        read_only: bool = False,
    ):
        """
        初始化文件系统服务

        Args:
            allowed_paths: 允许访问的路径列表，None表示允许所有路径
            max_file_size: 最大文件大小（字节）
            read_only: 是否只读模式
        """
        capabilities = [
            MCPCapability.FILE_READ,
            MCPCapability.FILE_LIST,
            MCPCapability.FILE_SEARCH,
        ]

        if not read_only:
            capabilities.append(MCPCapability.FILE_WRITE)

        super().__init__(
            name="filesystem",
            description="文件系统操作服务",
            capabilities=capabilities,
            version="1.0.0",
        )

        self.allowed_paths = allowed_paths or []
        self.max_file_size = max_file_size
        self.read_only = read_only

        self._logger = logging_system.get_logger("filesystem_mcp")

    async def start(self) -> bool:
        """启动服务"""
        try:
            # 验证允许的路径
            for path in self.allowed_paths:
                if not os.path.exists(path):
                    self._logger.warning(f"允许的路径不存在: {path}")

            self.is_running = True
            self._logger.info("文件系统MCP服务已启动")
            return True

        except Exception as e:
            self._logger.error(f"启动文件系统MCP服务失败: {e}")
            return False

    async def stop(self) -> bool:
        """停止服务"""
        self.is_running = False
        self._logger.info("文件系统MCP服务已停止")
        return True

    async def health_check(self) -> bool:
        """健康检查"""
        return self.is_running

    async def handle_request(self, request: MCPRequest) -> MCPResponse:
        """处理MCP请求"""
        try:
            method = request.method
            params = request.params

            if method == "file.read":
                result = await self._read_file(params)
            elif method == "file.write":
                result = await self._write_file(params)
            elif method == "file.list":
                result = await self._list_directory(params)
            elif method == "file.search":
                result = await self._search_files(params)
            elif method == "discover_capabilities":
                result = {"capabilities": [cap.value for cap in self.capabilities]}
            else:
                raise MCPServiceError(f"不支持的方法: {method}")

            return MCPResponse(
                id=request.id,
                result=result,
            )

        except Exception as e:
            self._logger.error(f"处理请求失败: {e}")
            return MCPResponse(
                id=request.id,
                error={
                    "code": "SERVICE_ERROR",
                    "message": str(e),
                },
            )

    def _check_path_allowed(self, path: str) -> bool:
        """检查路径是否被允许"""
        if not self.allowed_paths:
            return True

        abs_path = os.path.abspath(path)

        for allowed_path in self.allowed_paths:
            abs_allowed = os.path.abspath(allowed_path)
            if abs_path.startswith(abs_allowed):
                return True

        return False

    async def _read_file(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """读取文件"""
        path = params.get("path")
        if not path:
            raise MCPServiceError("缺少path参数")

        if not self._check_path_allowed(path):
            raise MCPServiceError(f"路径不被允许: {path}")

        if not os.path.exists(path):
            raise MCPServiceError(f"文件不存在: {path}")

        if not os.path.isfile(path):
            raise MCPServiceError(f"不是文件: {path}")

        # 检查文件大小
        file_size = os.path.getsize(path)
        if file_size > self.max_file_size:
            raise MCPServiceError(f"文件过大: {file_size} > {self.max_file_size}")

        try:
            # 尝试以文本模式读取
            with open(path, "r", encoding="utf-8") as f:
                content = f.read()
                is_binary = False
        except UnicodeDecodeError:
            # 如果是二进制文件，返回base64编码
            import base64

            with open(path, "rb") as f:
                content = base64.b64encode(f.read()).decode("ascii")
                is_binary = True

        return {
            "path": path,
            "content": content,
            "is_binary": is_binary,
            "size": file_size,
            "modified_time": os.path.getmtime(path),
        }

    async def _write_file(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """写入文件"""
        if self.read_only:
            raise MCPServiceError("服务处于只读模式")

        path = params.get("path")
        content = params.get("content")
        is_binary = params.get("is_binary", False)

        if not path:
            raise MCPServiceError("缺少path参数")

        if content is None:
            raise MCPServiceError("缺少content参数")

        if not self._check_path_allowed(path):
            raise MCPServiceError(f"路径不被允许: {path}")

        # 创建目录（如果不存在）
        os.makedirs(os.path.dirname(path), exist_ok=True)

        try:
            if is_binary:
                # 解码base64内容
                import base64

                binary_content = base64.b64decode(content)
                with open(path, "wb") as f:
                    f.write(binary_content)
            else:
                with open(path, "w", encoding="utf-8") as f:
                    f.write(content)

            file_size = os.path.getsize(path)

            return {
                "path": path,
                "size": file_size,
                "modified_time": os.path.getmtime(path),
                "success": True,
            }

        except Exception as e:
            raise MCPServiceError(f"写入文件失败: {e}")

    async def _list_directory(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """列出目录内容"""
        path = params.get("path", ".")
        recursive = params.get("recursive", False)
        include_hidden = params.get("include_hidden", False)

        if not self._check_path_allowed(path):
            raise MCPServiceError(f"路径不被允许: {path}")

        if not os.path.exists(path):
            raise MCPServiceError(f"路径不存在: {path}")

        if not os.path.isdir(path):
            raise MCPServiceError(f"不是目录: {path}")

        files = []
        directories = []

        try:
            if recursive:
                # 递归列出所有文件
                for root, dirs, filenames in os.walk(path):
                    # 过滤隐藏文件夹
                    if not include_hidden:
                        dirs[:] = [d for d in dirs if not d.startswith(".")]

                    for filename in filenames:
                        if not include_hidden and filename.startswith("."):
                            continue

                        file_path = os.path.join(root, filename)
                        rel_path = os.path.relpath(file_path, path)

                        files.append(
                            {
                                "name": filename,
                                "path": file_path,
                                "relative_path": rel_path,
                                "size": os.path.getsize(file_path),
                                "modified_time": os.path.getmtime(file_path),
                                "is_file": True,
                            }
                        )

                    for dirname in dirs:
                        if not include_hidden and dirname.startswith("."):
                            continue

                        dir_path = os.path.join(root, dirname)
                        rel_path = os.path.relpath(dir_path, path)

                        directories.append(
                            {
                                "name": dirname,
                                "path": dir_path,
                                "relative_path": rel_path,
                                "modified_time": os.path.getmtime(dir_path),
                                "is_directory": True,
                            }
                        )
            else:
                # 只列出当前目录
                for item in os.listdir(path):
                    if not include_hidden and item.startswith("."):
                        continue

                    item_path = os.path.join(path, item)

                    if os.path.isfile(item_path):
                        files.append(
                            {
                                "name": item,
                                "path": item_path,
                                "size": os.path.getsize(item_path),
                                "modified_time": os.path.getmtime(item_path),
                                "is_file": True,
                            }
                        )
                    elif os.path.isdir(item_path):
                        directories.append(
                            {
                                "name": item,
                                "path": item_path,
                                "modified_time": os.path.getmtime(item_path),
                                "is_directory": True,
                            }
                        )

            return {
                "path": path,
                "files": files,
                "directories": directories,
                "total_files": len(files),
                "total_directories": len(directories),
            }

        except Exception as e:
            raise MCPServiceError(f"列出目录失败: {e}")

    async def _search_files(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """搜索文件"""
        path = params.get("path", ".")
        pattern = params.get("pattern", "*")
        recursive = params.get("recursive", True)
        include_content = params.get("include_content", False)

        if not self._check_path_allowed(path):
            raise MCPServiceError(f"路径不被允许: {path}")

        if not os.path.exists(path):
            raise MCPServiceError(f"路径不存在: {path}")

        try:
            matches = []

            if recursive:
                search_pattern = os.path.join(path, "**", pattern)
                file_paths = glob.glob(search_pattern, recursive=True)
            else:
                search_pattern = os.path.join(path, pattern)
                file_paths = glob.glob(search_pattern)

            for file_path in file_paths:
                if os.path.isfile(file_path):
                    match_info = {
                        "path": file_path,
                        "name": os.path.basename(file_path),
                        "size": os.path.getsize(file_path),
                        "modified_time": os.path.getmtime(file_path),
                    }

                    if include_content:
                        try:
                            with open(file_path, "r", encoding="utf-8") as f:
                                match_info["content"] = f.read()
                        except (UnicodeDecodeError, PermissionError):
                            match_info["content"] = "[二进制文件或无权限]"

                    matches.append(match_info)

            return {
                "path": path,
                "pattern": pattern,
                "matches": matches,
                "total_matches": len(matches),
            }

        except Exception as e:
            raise MCPServiceError(f"搜索文件失败: {e}")
