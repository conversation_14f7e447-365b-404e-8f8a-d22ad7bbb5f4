"""
Web搜索MCP服务

提供多搜索引擎支持、结果聚合和网页内容提取功能。
"""

import asyncio
import json
import re
from typing import Any, Dict, List, Optional
from urllib.parse import urljoin, urlparse
from datetime import datetime

try:
    import aiohttp
    from bs4 import BeautifulSoup
except ImportError:
    aiohttp = None
    BeautifulSoup = None

from ai_agent_framework.utils.logging_system import logging_system

from ..mcp_service import (
    MCPCapability,
    MCPRequest,
    MCPResponse,
    MCPService,
    MCPServiceError,
)


class WebSearchMCPService(MCPService):
    """Web搜索MCP服务"""
    
    def __init__(
        self,
        search_engines: Dict[str, Dict[str, Any]],
        max_results: int = 10,
        request_timeout: int = 30,
        user_agent: str = "AI-Agent-Framework/1.0",
    ):
        """
        初始化Web搜索MCP服务
        
        Args:
            search_engines: 搜索引擎配置
            max_results: 最大结果数量
            request_timeout: 请求超时时间
            user_agent: 用户代理字符串
        """
        capabilities = [
            MCPCapability.WEB_SEARCH,
            MCPCapability.WEB_SCRAPE,
            MCPCapability.WEB_BROWSE,
        ]
        
        super().__init__(
            name="web_search",
            description="Web搜索和内容提取服务",
            capabilities=capabilities,
            version="1.0.0",
        )
        
        self.search_engines = search_engines
        self.max_results = max_results
        self.request_timeout = request_timeout
        self.user_agent = user_agent
        
        self._logger = logging_system.get_logger("web_search_mcp")
        self._session: Optional[aiohttp.ClientSession] = None
    
    async def start(self) -> bool:
        """启动服务"""
        try:
            if aiohttp is None:
                raise ImportError("需要安装aiohttp和beautifulsoup4: pip install aiohttp beautifulsoup4")
            
            # 创建HTTP会话
            timeout = aiohttp.ClientTimeout(total=self.request_timeout)
            headers = {"User-Agent": self.user_agent}
            
            self._session = aiohttp.ClientSession(
                timeout=timeout,
                headers=headers
            )
            
            self.is_running = True
            self._logger.info(f"Web搜索MCP服务已启动，支持{len(self.search_engines)}个搜索引擎")
            return True
            
        except Exception as e:
            self._logger.error(f"启动Web搜索MCP服务失败: {e}")
            return False
    
    async def stop(self) -> bool:
        """停止服务"""
        try:
            if self._session:
                await self._session.close()
                self._session = None
            
            self.is_running = False
            self._logger.info("Web搜索MCP服务已停止")
            return True
            
        except Exception as e:
            self._logger.error(f"停止Web搜索MCP服务失败: {e}")
            return False
    
    async def health_check(self) -> bool:
        """健康检查"""
        return self.is_running and self._session is not None
    
    async def handle_request(self, request: MCPRequest) -> MCPResponse:
        """处理MCP请求"""
        try:
            method = request.method
            params = request.params
            
            if method == "web.search":
                result = await self._search(params)
            elif method == "web.scrape":
                result = await self._scrape_url(params)
            elif method == "web.browse":
                result = await self._browse_url(params)
            elif method == "discover_capabilities":
                result = {"capabilities": [cap.value for cap in self.capabilities]}
            else:
                raise MCPServiceError(f"不支持的方法: {method}")
            
            return MCPResponse(
                id=request.id,
                result=result,
            )
            
        except Exception as e:
            self._logger.error(f"处理Web搜索请求失败: {e}")
            return MCPResponse(
                id=request.id,
                error={
                    "code": "WEB_SEARCH_ERROR",
                    "message": str(e),
                }
            )
    
    async def _search(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行Web搜索"""
        query = params.get("query")
        engine = params.get("engine", "duckduckgo")
        max_results = params.get("max_results", self.max_results)
        
        if not query:
            raise MCPServiceError("搜索查询不能为空")
        
        if engine not in self.search_engines:
            raise MCPServiceError(f"不支持的搜索引擎: {engine}")
        
        try:
            # 执行搜索
            if engine == "duckduckgo":
                results = await self._search_duckduckgo(query, max_results)
            elif engine == "bing":
                results = await self._search_bing(query, max_results)
            else:
                raise MCPServiceError(f"搜索引擎{engine}尚未实现")
            
            return {
                "query": query,
                "engine": engine,
                "results": results,
                "total_results": len(results),
                "timestamp": datetime.now().isoformat(),
            }
            
        except Exception as e:
            raise MCPServiceError(f"搜索失败: {str(e)}")
    
    async def _search_duckduckgo(self, query: str, max_results: int) -> List[Dict[str, Any]]:
        """使用DuckDuckGo搜索"""
        # 简化的DuckDuckGo搜索实现
        search_url = "https://html.duckduckgo.com/html/"
        params = {"q": query}
        
        try:
            async with self._session.get(search_url, params=params) as response:
                if response.status != 200:
                    raise MCPServiceError(f"搜索请求失败: {response.status}")
                
                html_content = await response.text()
                
                # 解析搜索结果
                if BeautifulSoup is None:
                    raise ImportError("需要安装beautifulsoup4: pip install beautifulsoup4")
                
                soup = BeautifulSoup(html_content, 'html.parser')
                results = []
                
                # 查找搜索结果
                for result_div in soup.find_all('div', class_='result')[:max_results]:
                    title_link = result_div.find('a', class_='result__a')
                    snippet_div = result_div.find('div', class_='result__snippet')
                    
                    if title_link and snippet_div:
                        results.append({
                            "title": title_link.get_text(strip=True),
                            "url": title_link.get('href'),
                            "snippet": snippet_div.get_text(strip=True),
                            "source": "DuckDuckGo"
                        })
                
                return results
                
        except Exception as e:
            raise MCPServiceError(f"DuckDuckGo搜索失败: {str(e)}")
    
    async def _search_bing(self, query: str, max_results: int) -> List[Dict[str, Any]]:
        """使用Bing搜索（需要API密钥）"""
        # 这里需要Bing Search API密钥
        api_key = self.search_engines.get("bing", {}).get("api_key")
        if not api_key:
            raise MCPServiceError("Bing搜索需要API密钥")
        
        search_url = "https://api.bing.microsoft.com/v7.0/search"
        headers = {"Ocp-Apim-Subscription-Key": api_key}
        params = {"q": query, "count": max_results}
        
        try:
            async with self._session.get(search_url, headers=headers, params=params) as response:
                if response.status != 200:
                    raise MCPServiceError(f"Bing搜索请求失败: {response.status}")
                
                data = await response.json()
                results = []
                
                for item in data.get("webPages", {}).get("value", []):
                    results.append({
                        "title": item.get("name", ""),
                        "url": item.get("url", ""),
                        "snippet": item.get("snippet", ""),
                        "source": "Bing"
                    })
                
                return results
                
        except Exception as e:
            raise MCPServiceError(f"Bing搜索失败: {str(e)}")
    
    async def _scrape_url(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """抓取网页内容"""
        url = params.get("url")
        extract_text = params.get("extract_text", True)
        include_links = params.get("include_links", False)
        
        if not url:
            raise MCPServiceError("URL不能为空")
        
        try:
            async with self._session.get(url) as response:
                if response.status != 200:
                    raise MCPServiceError(f"网页请求失败: {response.status}")
                
                content = await response.text()
                
                result = {
                    "url": url,
                    "status_code": response.status,
                    "content_type": response.headers.get("content-type", ""),
                    "content_length": len(content),
                }
                
                if extract_text and BeautifulSoup:
                    soup = BeautifulSoup(content, 'html.parser')
                    
                    # 提取文本内容
                    result["title"] = soup.title.string if soup.title else ""
                    result["text"] = soup.get_text(strip=True, separator='\n')
                    
                    # 提取链接（如果需要）
                    if include_links:
                        links = []
                        for link in soup.find_all('a', href=True):
                            href = link['href']
                            # 转换相对链接为绝对链接
                            absolute_url = urljoin(url, href)
                            links.append({
                                "text": link.get_text(strip=True),
                                "url": absolute_url
                            })
                        result["links"] = links
                else:
                    result["raw_content"] = content
                
                return result
                
        except Exception as e:
            raise MCPServiceError(f"网页抓取失败: {str(e)}")
    
    async def _browse_url(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """浏览网页（获取基本信息）"""
        url = params.get("url")
        
        if not url:
            raise MCPServiceError("URL不能为空")
        
        try:
            async with self._session.head(url) as response:
                return {
                    "url": url,
                    "status_code": response.status,
                    "headers": dict(response.headers),
                    "content_type": response.headers.get("content-type", ""),
                    "content_length": response.headers.get("content-length"),
                    "last_modified": response.headers.get("last-modified"),
                    "is_accessible": response.status == 200,
                }
                
        except Exception as e:
            raise MCPServiceError(f"网页浏览失败: {str(e)}")
