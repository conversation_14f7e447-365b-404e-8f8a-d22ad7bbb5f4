"""
MCP服务基础类和数据结构

定义了MCP协议的基础数据结构和服务接口。
"""

import json
from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from uuid import uuid4

from pydantic import BaseModel, ConfigDict, Field


class MCPCapability(str, Enum):
    """MCP服务能力枚举"""

    # 文件系统操作
    FILE_READ = "file.read"
    FILE_WRITE = "file.write"
    FILE_LIST = "file.list"
    FILE_SEARCH = "file.search"
    FILE_WATCH = "file.watch"

    # 数据库操作
    DB_QUERY = "db.query"
    DB_EXECUTE = "db.execute"
    DB_SCHEMA = "db.schema"
    DB_CONNECTIONS = "db.connections"

    # Web搜索
    WEB_SEARCH = "web.search"
    WEB_SCRAPE = "web.scrape"
    WEB_BROWSE = "web.browse"

    # 代码执行
    CODE_EXECUTE = "code.execute"
    CODE_VALIDATE = "code.validate"
    CODE_FORMAT = "code.format"

    # API调用
    API_CALL = "api.call"
    API_VALIDATE = "api.validate"
    API_DOCS = "api.docs"


class MCPRequest(BaseModel):
    """MCP请求数据结构"""

    id: str = Field(default_factory=lambda: str(uuid4()), description="请求ID")
    method: str = Field(..., description="请求方法")
    params: Dict[str, Any] = Field(default_factory=dict, description="请求参数")
    timestamp: datetime = Field(default_factory=datetime.now, description="请求时间戳")

    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat(),
        }
    )


class MCPResponse(BaseModel):
    """MCP响应数据结构"""

    id: str = Field(..., description="对应的请求ID")
    result: Optional[Any] = Field(None, description="响应结果")
    error: Optional[Dict[str, Any]] = Field(None, description="错误信息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")

    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat(),
        }
    )

    @property
    def is_success(self) -> bool:
        """检查响应是否成功"""
        return self.error is None

    @property
    def is_error(self) -> bool:
        """检查响应是否有错误"""
        return self.error is not None


class MCPService(ABC):
    """MCP服务抽象基类"""

    def __init__(
        self,
        name: str,
        description: str,
        capabilities: List[MCPCapability],
        version: str = "1.0.0",
    ):
        """
        初始化MCP服务

        Args:
            name: 服务名称
            description: 服务描述
            capabilities: 服务能力列表
            version: 服务版本
        """
        self.name = name
        self.description = description
        self.capabilities = capabilities
        self.version = version
        self.is_running = False

    @abstractmethod
    async def start(self) -> bool:
        """启动服务"""
        pass

    @abstractmethod
    async def stop(self) -> bool:
        """停止服务"""
        pass

    @abstractmethod
    async def health_check(self) -> bool:
        """健康检查"""
        pass

    @abstractmethod
    async def handle_request(self, request: MCPRequest) -> MCPResponse:
        """处理MCP请求"""
        pass

    def supports_capability(self, capability: MCPCapability) -> bool:
        """检查是否支持指定能力"""
        return capability in self.capabilities

    def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        return {
            "name": self.name,
            "description": self.description,
            "version": self.version,
            "capabilities": [cap.value for cap in self.capabilities],
            "is_running": self.is_running,
        }


class MCPServiceStatus(str, Enum):
    """MCP服务状态枚举"""

    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


class MCPServiceInfo(BaseModel):
    """MCP服务信息"""

    name: str = Field(..., description="服务名称")
    description: str = Field(..., description="服务描述")
    version: str = Field(..., description="服务版本")
    capabilities: List[str] = Field(..., description="服务能力列表")
    status: MCPServiceStatus = Field(..., description="服务状态")
    endpoint: Optional[str] = Field(None, description="服务端点")
    last_health_check: Optional[datetime] = Field(None, description="最后健康检查时间")

    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat(),
        }
    )


class MCPError(Exception):
    """MCP错误基类"""

    def __init__(
        self,
        message: str,
        error_code: str = "MCP_ERROR",
        details: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "message": self.message,
            "error_code": self.error_code,
            "details": self.details,
        }


class MCPConnectionError(MCPError):
    """MCP连接错误"""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, "MCP_CONNECTION_ERROR", details)


class MCPServiceError(MCPError):
    """MCP服务错误"""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, "MCP_SERVICE_ERROR", details)


class MCPProtocolError(MCPError):
    """MCP协议错误"""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, "MCP_PROTOCOL_ERROR", details)
