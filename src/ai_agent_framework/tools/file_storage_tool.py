"""
文件存储服务工具

提供统一的文件存储操作接口，支持本地文件系统、云存储（AWS S3、阿里云OSS等）。
适用于AI Agent的文件上传、下载、管理等场景。
"""

import asyncio
import hashlib
import mimetypes
import os
import shutil
from abc import ABC, abstractmethod
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, BinaryIO
from datetime import datetime
from urllib.parse import urlparse

from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult
from ai_agent_framework.utils.logging_system import logging_system


class StorageType(Enum):
    """存储类型枚举"""
    LOCAL = "local"
    AWS_S3 = "aws_s3"
    ALIYUN_OSS = "aliyun_oss"
    TENCENT_COS = "tencent_cos"
    MEMORY = "memory"


class FileInfo:
    """文件信息数据结构"""
    
    def __init__(
        self,
        path: str,
        name: str,
        size: int,
        content_type: Optional[str] = None,
        created_at: Optional[datetime] = None,
        modified_at: Optional[datetime] = None,
        checksum: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """
        初始化文件信息
        
        Args:
            path: 文件路径
            name: 文件名
            size: 文件大小（字节）
            content_type: MIME类型
            created_at: 创建时间
            modified_at: 修改时间
            checksum: 文件校验和
            metadata: 元数据
        """
        self.path = path
        self.name = name
        self.size = size
        self.content_type = content_type or self._guess_content_type(name)
        self.created_at = created_at or datetime.now()
        self.modified_at = modified_at or datetime.now()
        self.checksum = checksum
        self.metadata = metadata or {}
    
    def _guess_content_type(self, filename: str) -> str:
        """根据文件名猜测MIME类型"""
        content_type, _ = mimetypes.guess_type(filename)
        return content_type or "application/octet-stream"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "path": self.path,
            "name": self.name,
            "size": self.size,
            "content_type": self.content_type,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "modified_at": self.modified_at.isoformat() if self.modified_at else None,
            "checksum": self.checksum,
            "metadata": self.metadata,
        }


class StorageAdapter(ABC):
    """存储适配器抽象基类"""
    
    @abstractmethod
    async def connect(self) -> bool:
        """连接到存储系统"""
        pass
    
    @abstractmethod
    async def disconnect(self) -> bool:
        """断开连接"""
        pass
    
    @abstractmethod
    async def upload_file(
        self,
        local_path: str,
        remote_path: str,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> FileInfo:
        """上传文件"""
        pass
    
    @abstractmethod
    async def download_file(
        self,
        remote_path: str,
        local_path: str,
    ) -> bool:
        """下载文件"""
        pass
    
    @abstractmethod
    async def delete_file(self, remote_path: str) -> bool:
        """删除文件"""
        pass
    
    @abstractmethod
    async def list_files(
        self,
        prefix: str = "",
        limit: Optional[int] = None,
    ) -> List[FileInfo]:
        """列出文件"""
        pass
    
    @abstractmethod
    async def get_file_info(self, remote_path: str) -> Optional[FileInfo]:
        """获取文件信息"""
        pass
    
    @abstractmethod
    async def file_exists(self, remote_path: str) -> bool:
        """检查文件是否存在"""
        pass
    
    @abstractmethod
    async def create_directory(self, directory_path: str) -> bool:
        """创建目录"""
        pass
    
    @abstractmethod
    async def delete_directory(self, directory_path: str) -> bool:
        """删除目录"""
        pass


class LocalStorageAdapter(StorageAdapter):
    """本地文件系统存储适配器"""
    
    def __init__(self, base_path: str = "./storage"):
        """
        初始化本地存储适配器
        
        Args:
            base_path: 基础存储路径
        """
        self.base_path = Path(base_path).resolve()
        self._connected = False
        self._logger = logging_system.get_logger("local_storage")
    
    async def connect(self) -> bool:
        """连接到本地存储"""
        try:
            # 确保基础目录存在
            self.base_path.mkdir(parents=True, exist_ok=True)
            self._connected = True
            self._logger.info(f"已连接到本地存储: {self.base_path}")
            return True
        except Exception as e:
            self._logger.error(f"连接本地存储失败: {str(e)}")
            return False
    
    async def disconnect(self) -> bool:
        """断开连接"""
        self._connected = False
        self._logger.info("已断开本地存储连接")
        return True
    
    def _get_full_path(self, remote_path: str) -> Path:
        """获取完整的本地路径"""
        # 移除开头的斜杠，确保是相对路径
        clean_path = remote_path.lstrip("/")
        return self.base_path / clean_path
    
    def _calculate_checksum(self, file_path: Path) -> str:
        """计算文件MD5校验和"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    async def upload_file(
        self,
        local_path: str,
        remote_path: str,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> FileInfo:
        """上传文件"""
        if not self._connected:
            raise RuntimeError("存储适配器未连接")
        
        local_file = Path(local_path)
        if not local_file.exists():
            raise FileNotFoundError(f"本地文件不存在: {local_path}")
        
        remote_file = self._get_full_path(remote_path)
        
        # 确保目标目录存在
        remote_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 复制文件
        shutil.copy2(local_file, remote_file)
        
        # 获取文件信息
        stat = remote_file.stat()
        checksum = self._calculate_checksum(remote_file)
        
        file_info = FileInfo(
            path=remote_path,
            name=remote_file.name,
            size=stat.st_size,
            created_at=datetime.fromtimestamp(stat.st_ctime),
            modified_at=datetime.fromtimestamp(stat.st_mtime),
            checksum=checksum,
            metadata=metadata,
        )
        
        self._logger.info(f"文件已上传: {local_path} -> {remote_path}")
        return file_info
    
    async def download_file(self, remote_path: str, local_path: str) -> bool:
        """下载文件"""
        if not self._connected:
            return False
        
        remote_file = self._get_full_path(remote_path)
        if not remote_file.exists():
            return False
        
        local_file = Path(local_path)
        
        # 确保本地目录存在
        local_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 复制文件
        shutil.copy2(remote_file, local_file)
        
        self._logger.info(f"文件已下载: {remote_path} -> {local_path}")
        return True
    
    async def delete_file(self, remote_path: str) -> bool:
        """删除文件"""
        if not self._connected:
            return False
        
        remote_file = self._get_full_path(remote_path)
        if not remote_file.exists():
            return False
        
        try:
            remote_file.unlink()
            self._logger.info(f"文件已删除: {remote_path}")
            return True
        except Exception as e:
            self._logger.error(f"删除文件失败: {str(e)}")
            return False
    
    async def list_files(
        self,
        prefix: str = "",
        limit: Optional[int] = None,
    ) -> List[FileInfo]:
        """列出文件"""
        if not self._connected:
            return []
        
        files = []
        search_path = self._get_full_path(prefix) if prefix else self.base_path
        
        try:
            if search_path.is_file():
                # 如果是单个文件
                stat = search_path.stat()
                relative_path = str(search_path.relative_to(self.base_path))
                file_info = FileInfo(
                    path=relative_path,
                    name=search_path.name,
                    size=stat.st_size,
                    created_at=datetime.fromtimestamp(stat.st_ctime),
                    modified_at=datetime.fromtimestamp(stat.st_mtime),
                    checksum=self._calculate_checksum(search_path),
                )
                files.append(file_info)
            elif search_path.is_dir():
                # 如果是目录，递归列出所有文件
                for file_path in search_path.rglob("*"):
                    if file_path.is_file():
                        stat = file_path.stat()
                        relative_path = str(file_path.relative_to(self.base_path))
                        file_info = FileInfo(
                            path=relative_path,
                            name=file_path.name,
                            size=stat.st_size,
                            created_at=datetime.fromtimestamp(stat.st_ctime),
                            modified_at=datetime.fromtimestamp(stat.st_mtime),
                        )
                        files.append(file_info)
                        
                        if limit and len(files) >= limit:
                            break
        except Exception as e:
            self._logger.error(f"列出文件失败: {str(e)}")
        
        return files
    
    async def get_file_info(self, remote_path: str) -> Optional[FileInfo]:
        """获取文件信息"""
        if not self._connected:
            return None
        
        remote_file = self._get_full_path(remote_path)
        if not remote_file.exists() or not remote_file.is_file():
            return None
        
        try:
            stat = remote_file.stat()
            checksum = self._calculate_checksum(remote_file)
            
            return FileInfo(
                path=remote_path,
                name=remote_file.name,
                size=stat.st_size,
                created_at=datetime.fromtimestamp(stat.st_ctime),
                modified_at=datetime.fromtimestamp(stat.st_mtime),
                checksum=checksum,
            )
        except Exception as e:
            self._logger.error(f"获取文件信息失败: {str(e)}")
            return None
    
    async def file_exists(self, remote_path: str) -> bool:
        """检查文件是否存在"""
        if not self._connected:
            return False
        
        remote_file = self._get_full_path(remote_path)
        return remote_file.exists() and remote_file.is_file()
    
    async def create_directory(self, directory_path: str) -> bool:
        """创建目录"""
        if not self._connected:
            return False
        
        try:
            directory = self._get_full_path(directory_path)
            directory.mkdir(parents=True, exist_ok=True)
            self._logger.info(f"目录已创建: {directory_path}")
            return True
        except Exception as e:
            self._logger.error(f"创建目录失败: {str(e)}")
            return False
    
    async def delete_directory(self, directory_path: str) -> bool:
        """删除目录"""
        if not self._connected:
            return False
        
        try:
            directory = self._get_full_path(directory_path)
            if directory.exists() and directory.is_dir():
                shutil.rmtree(directory)
                self._logger.info(f"目录已删除: {directory_path}")
                return True
            return False
        except Exception as e:
            self._logger.error(f"删除目录失败: {str(e)}")
            return False


class FileStorageTool(ToolInterface):
    """
    文件存储服务工具
    
    提供统一的文件存储操作接口，支持多种存储系统。
    适用于AI Agent的文件上传、下载、管理等场景。
    """
    
    def __init__(
        self,
        storage_type: StorageType = StorageType.LOCAL,
        connection_config: Optional[Dict[str, Any]] = None,
    ):
        """
        初始化文件存储工具
        
        Args:
            storage_type: 存储类型
            connection_config: 连接配置
        """
        self.storage_type = storage_type
        self.connection_config = connection_config or {}
        
        self._logger = logging_system.get_logger("file_storage_tool")
        self._adapter: Optional[StorageAdapter] = None
        self._initialize_adapter()
    
    def _initialize_adapter(self) -> None:
        """初始化存储适配器"""
        if self.storage_type == StorageType.LOCAL:
            base_path = self.connection_config.get("base_path", "./storage")
            self._adapter = LocalStorageAdapter(base_path)
        elif self.storage_type == StorageType.AWS_S3:
            # TODO: 实现AWS S3适配器
            raise NotImplementedError("AWS S3适配器尚未实现")
        elif self.storage_type == StorageType.ALIYUN_OSS:
            # TODO: 实现阿里云OSS适配器
            raise NotImplementedError("阿里云OSS适配器尚未实现")
        elif self.storage_type == StorageType.TENCENT_COS:
            # TODO: 实现腾讯云COS适配器
            raise NotImplementedError("腾讯云COS适配器尚未实现")
        else:
            raise ValueError(f"不支持的存储类型: {self.storage_type}")
    
    @property
    def name(self) -> str:
        return "file_storage"
    
    @property
    def description(self) -> str:
        return "文件存储服务工具，支持文件上传、下载、管理等操作"

    @property
    def parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "enum": [
                        "connect", "disconnect", "upload", "download", "delete",
                        "list", "info", "exists", "create_dir", "delete_dir",
                        "copy", "move", "get_url"
                    ],
                    "description": "要执行的操作"
                },
                "local_path": {
                    "type": "string",
                    "description": "本地文件路径"
                },
                "remote_path": {
                    "type": "string",
                    "description": "远程文件路径"
                },
                "source_path": {
                    "type": "string",
                    "description": "源文件路径（用于复制和移动）"
                },
                "destination_path": {
                    "type": "string",
                    "description": "目标文件路径（用于复制和移动）"
                },
                "directory_path": {
                    "type": "string",
                    "description": "目录路径"
                },
                "prefix": {
                    "type": "string",
                    "default": "",
                    "description": "文件路径前缀（用于列出文件）"
                },
                "limit": {
                    "type": "integer",
                    "minimum": 1,
                    "description": "返回文件数量限制"
                },
                "metadata": {
                    "type": "object",
                    "description": "文件元数据"
                },
                "recursive": {
                    "type": "boolean",
                    "default": False,
                    "description": "是否递归操作"
                }
            },
            "required": ["action"],
            "additionalProperties": False
        }

    async def execute(
        self,
        arguments: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None,
    ) -> ToolResult:
        """
        执行文件存储操作

        Args:
            arguments: 工具参数
            context: 执行上下文

        Returns:
            ToolResult: 执行结果
        """
        try:
            action = arguments.get("action")
            if not action:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少必需参数: action"
                )

            # 确保适配器已初始化
            if not self._adapter:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="存储适配器未初始化"
                )

            # 执行对应的操作
            if action == "connect":
                result = await self._handle_connect()
            elif action == "disconnect":
                result = await self._handle_disconnect()
            elif action == "upload":
                result = await self._handle_upload(arguments)
            elif action == "download":
                result = await self._handle_download(arguments)
            elif action == "delete":
                result = await self._handle_delete(arguments)
            elif action == "list":
                result = await self._handle_list(arguments)
            elif action == "info":
                result = await self._handle_info(arguments)
            elif action == "exists":
                result = await self._handle_exists(arguments)
            elif action == "create_dir":
                result = await self._handle_create_dir(arguments)
            elif action == "delete_dir":
                result = await self._handle_delete_dir(arguments)
            elif action == "copy":
                result = await self._handle_copy(arguments)
            elif action == "move":
                result = await self._handle_move(arguments)
            elif action == "get_url":
                result = await self._handle_get_url(arguments)
            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"不支持的操作: {action}"
                )

            return result

        except Exception as e:
            self._logger.error(f"文件存储操作失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"操作失败: {str(e)}"
            )

    async def _handle_connect(self) -> ToolResult:
        """处理连接操作"""
        success = await self._adapter.connect()
        return ToolResult(
            tool_call_id="",
            success=success,
            result={"connected": success, "storage_type": self.storage_type.value}
        )

    async def _handle_disconnect(self) -> ToolResult:
        """处理断开连接操作"""
        success = await self._adapter.disconnect()
        return ToolResult(
            tool_call_id="",
            success=success,
            result={"disconnected": success}
        )

    async def _handle_upload(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理上传文件操作"""
        local_path = arguments.get("local_path")
        remote_path = arguments.get("remote_path")
        metadata = arguments.get("metadata", {})

        if not local_path:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: local_path"
            )

        if not remote_path:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: remote_path"
            )

        try:
            file_info = await self._adapter.upload_file(local_path, remote_path, metadata)
            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "uploaded": True,
                    "local_path": local_path,
                    "remote_path": remote_path,
                    "file_info": file_info.to_dict(),
                }
            )
        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"上传失败: {str(e)}"
            )

    async def _handle_download(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理下载文件操作"""
        remote_path = arguments.get("remote_path")
        local_path = arguments.get("local_path")

        if not remote_path:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: remote_path"
            )

        if not local_path:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: local_path"
            )

        success = await self._adapter.download_file(remote_path, local_path)

        return ToolResult(
            tool_call_id="",
            success=success,
            result={
                "downloaded": success,
                "remote_path": remote_path,
                "local_path": local_path,
            }
        )

    async def _handle_delete(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理删除文件操作"""
        remote_path = arguments.get("remote_path")

        if not remote_path:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: remote_path"
            )

        success = await self._adapter.delete_file(remote_path)

        return ToolResult(
            tool_call_id="",
            success=success,
            result={
                "deleted": success,
                "remote_path": remote_path,
            }
        )

    async def _handle_list(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理列出文件操作"""
        prefix = arguments.get("prefix", "")
        limit = arguments.get("limit")

        files = await self._adapter.list_files(prefix, limit)

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "files": [file_info.to_dict() for file_info in files],
                "count": len(files),
                "prefix": prefix,
                "limit": limit,
            }
        )

    async def _handle_info(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理获取文件信息操作"""
        remote_path = arguments.get("remote_path")

        if not remote_path:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: remote_path"
            )

        file_info = await self._adapter.get_file_info(remote_path)

        if file_info:
            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "found": True,
                    "file_info": file_info.to_dict(),
                }
            )
        else:
            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "found": False,
                    "remote_path": remote_path,
                }
            )

    async def _handle_exists(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理检查文件存在操作"""
        remote_path = arguments.get("remote_path")

        if not remote_path:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: remote_path"
            )

        exists = await self._adapter.file_exists(remote_path)

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "exists": exists,
                "remote_path": remote_path,
            }
        )

    async def _handle_create_dir(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理创建目录操作"""
        directory_path = arguments.get("directory_path")

        if not directory_path:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: directory_path"
            )

        success = await self._adapter.create_directory(directory_path)

        return ToolResult(
            tool_call_id="",
            success=success,
            result={
                "created": success,
                "directory_path": directory_path,
            }
        )

    async def _handle_delete_dir(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理删除目录操作"""
        directory_path = arguments.get("directory_path")

        if not directory_path:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: directory_path"
            )

        success = await self._adapter.delete_directory(directory_path)

        return ToolResult(
            tool_call_id="",
            success=success,
            result={
                "deleted": success,
                "directory_path": directory_path,
            }
        )

    async def _handle_copy(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理复制文件操作"""
        source_path = arguments.get("source_path")
        destination_path = arguments.get("destination_path")

        if not source_path:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: source_path"
            )

        if not destination_path:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: destination_path"
            )

        # 对于本地存储，实现复制功能
        if isinstance(self._adapter, LocalStorageAdapter):
            try:
                source_file = self._adapter._get_full_path(source_path)
                dest_file = self._adapter._get_full_path(destination_path)

                if not source_file.exists():
                    return ToolResult(
                        tool_call_id="",
                        success=False,
                        error=f"源文件不存在: {source_path}"
                    )

                # 确保目标目录存在
                dest_file.parent.mkdir(parents=True, exist_ok=True)

                # 复制文件
                shutil.copy2(source_file, dest_file)

                return ToolResult(
                    tool_call_id="",
                    success=True,
                    result={
                        "copied": True,
                        "source_path": source_path,
                        "destination_path": destination_path,
                    }
                )
            except Exception as e:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"复制失败: {str(e)}"
                )
        else:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="当前存储类型不支持复制操作"
            )

    async def _handle_move(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理移动文件操作"""
        source_path = arguments.get("source_path")
        destination_path = arguments.get("destination_path")

        if not source_path:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: source_path"
            )

        if not destination_path:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: destination_path"
            )

        # 对于本地存储，实现移动功能
        if isinstance(self._adapter, LocalStorageAdapter):
            try:
                source_file = self._adapter._get_full_path(source_path)
                dest_file = self._adapter._get_full_path(destination_path)

                if not source_file.exists():
                    return ToolResult(
                        tool_call_id="",
                        success=False,
                        error=f"源文件不存在: {source_path}"
                    )

                # 确保目标目录存在
                dest_file.parent.mkdir(parents=True, exist_ok=True)

                # 移动文件
                shutil.move(str(source_file), str(dest_file))

                return ToolResult(
                    tool_call_id="",
                    success=True,
                    result={
                        "moved": True,
                        "source_path": source_path,
                        "destination_path": destination_path,
                    }
                )
            except Exception as e:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"移动失败: {str(e)}"
                )
        else:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="当前存储类型不支持移动操作"
            )

    async def _handle_get_url(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理获取文件URL操作"""
        remote_path = arguments.get("remote_path")

        if not remote_path:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: remote_path"
            )

        # 对于本地存储，返回文件路径
        if isinstance(self._adapter, LocalStorageAdapter):
            full_path = self._adapter._get_full_path(remote_path)
            if full_path.exists():
                return ToolResult(
                    tool_call_id="",
                    success=True,
                    result={
                        "url": f"file://{full_path.absolute()}",
                        "remote_path": remote_path,
                        "type": "local_file",
                    }
                )
            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"文件不存在: {remote_path}"
                )
        else:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="当前存储类型不支持获取URL操作"
            )

    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """
        验证工具参数

        Args:
            arguments: 工具参数

        Returns:
            bool: 参数是否有效
        """
        action = arguments.get("action")
        if not action:
            return False

        valid_actions = [
            "connect", "disconnect", "upload", "download", "delete",
            "list", "info", "exists", "create_dir", "delete_dir",
            "copy", "move", "get_url"
        ]

        if action not in valid_actions:
            return False

        # 验证需要路径参数的操作
        path_required_actions = {
            "upload": ["local_path", "remote_path"],
            "download": ["remote_path", "local_path"],
            "delete": ["remote_path"],
            "info": ["remote_path"],
            "exists": ["remote_path"],
            "create_dir": ["directory_path"],
            "delete_dir": ["directory_path"],
            "copy": ["source_path", "destination_path"],
            "move": ["source_path", "destination_path"],
            "get_url": ["remote_path"],
        }

        if action in path_required_actions:
            required_params = path_required_actions[action]
            for param in required_params:
                if not arguments.get(param):
                    return False

        return True
