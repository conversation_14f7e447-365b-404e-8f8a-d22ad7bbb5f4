"""
HTTP客户端工具

提供HTTP请求功能，支持GET、POST、PUT、DELETE等方法。
"""

import asyncio
import json
from typing import Any, Dict, List, Optional
from urllib.parse import urlparse

try:
    import aiohttp
except ImportError:
    aiohttp = None

from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult
from ai_agent_framework.utils.logging_system import logging_system


class HTTPClientTool(ToolInterface):
    """HTTP客户端工具"""
    
    def __init__(
        self,
        timeout: int = 30,
        max_response_size: int = 10 * 1024 * 1024,  # 10MB
        allowed_domains: Optional[List[str]] = None,
        blocked_domains: Optional[List[str]] = None,
        user_agent: str = "AI-Agent-Framework/1.0",
    ):
        """
        初始化HTTP客户端工具
        
        Args:
            timeout: 请求超时时间（秒）
            max_response_size: 最大响应大小（字节）
            allowed_domains: 允许的域名列表
            blocked_domains: 禁止的域名列表
            user_agent: 用户代理字符串
        """
        self.timeout = timeout
        self.max_response_size = max_response_size
        self.allowed_domains = allowed_domains or []
        self.blocked_domains = blocked_domains or []
        self.user_agent = user_agent
        
        self._logger = logging_system.get_logger("http_client_tool")
        self._session: Optional[aiohttp.ClientSession] = None
    
    @property
    def name(self) -> str:
        return "http_client"
    
    @property
    def description(self) -> str:
        return "HTTP客户端工具，支持GET、POST、PUT、DELETE等HTTP请求"
    
    @property
    def parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "method": {
                    "type": "string",
                    "enum": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"],
                    "description": "HTTP方法"
                },
                "url": {
                    "type": "string",
                    "description": "请求URL"
                },
                "headers": {
                    "type": "object",
                    "description": "请求头（可选）"
                },
                "params": {
                    "type": "object",
                    "description": "URL参数（可选）"
                },
                "data": {
                    "type": "object",
                    "description": "请求体数据（可选）"
                },
                "json": {
                    "type": "object",
                    "description": "JSON请求体（可选）"
                },
                "auth": {
                    "type": "object",
                    "properties": {
                        "type": {"type": "string", "enum": ["basic", "bearer"]},
                        "username": {"type": "string"},
                        "password": {"type": "string"},
                        "token": {"type": "string"}
                    },
                    "description": "认证信息（可选）"
                },
                "follow_redirects": {
                    "type": "boolean",
                    "default": True,
                    "description": "是否跟随重定向"
                },
                "verify_ssl": {
                    "type": "boolean",
                    "default": True,
                    "description": "是否验证SSL证书"
                }
            },
            "required": ["method", "url"]
        }
    
    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """验证工具参数"""
        try:
            method = arguments.get("method")
            url = arguments.get("url")
            
            if not method or not url:
                return False
            
            # 验证URL格式
            parsed_url = urlparse(url)
            if not parsed_url.scheme or not parsed_url.netloc:
                return False
            
            # 检查域名限制
            domain = parsed_url.netloc.lower()
            
            # 检查禁止的域名
            if self.blocked_domains:
                for blocked in self.blocked_domains:
                    if blocked.lower() in domain:
                        self._logger.warning(f"请求被阻止的域名: {domain}")
                        return False
            
            # 检查允许的域名
            if self.allowed_domains:
                allowed = False
                for allowed_domain in self.allowed_domains:
                    if allowed_domain.lower() in domain:
                        allowed = True
                        break
                if not allowed:
                    self._logger.warning(f"请求不被允许的域名: {domain}")
                    return False
            
            return True
        except Exception:
            return False
    
    async def execute(self, arguments: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> ToolResult:
        """执行HTTP请求"""
        try:
            if aiohttp is None:
                return ToolResult(
                    success=False,
                    result="需要安装aiohttp: pip install aiohttp"
                )
            
            # 验证参数
            if not await self.validate_arguments(arguments):
                return ToolResult(
                    success=False,
                    result="请求参数验证失败"
                )
            
            # 初始化会话（如果需要）
            if not self._session:
                await self._init_session()
            
            # 执行请求
            result = await self._make_request(arguments)
            
            return ToolResult(
                success=result["success"],
                result=result
            )
            
        except Exception as e:
            self._logger.error(f"HTTP请求失败: {e}")
            return ToolResult(
                success=False,
                result=f"HTTP请求失败: {str(e)}"
            )
    
    async def _init_session(self):
        """初始化HTTP会话"""
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        headers = {"User-Agent": self.user_agent}
        
        self._session = aiohttp.ClientSession(
            timeout=timeout,
            headers=headers
        )
    
    async def _make_request(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """执行HTTP请求"""
        method = arguments["method"].upper()
        url = arguments["url"]
        headers = arguments.get("headers", {})
        params = arguments.get("params", {})
        data = arguments.get("data")
        json_data = arguments.get("json")
        auth = arguments.get("auth")
        follow_redirects = arguments.get("follow_redirects", True)
        verify_ssl = arguments.get("verify_ssl", True)
        
        # 准备请求参数
        request_kwargs = {
            "url": url,
            "headers": headers,
            "params": params,
            "allow_redirects": follow_redirects,
            "ssl": verify_ssl,
        }
        
        # 处理请求体
        if json_data:
            request_kwargs["json"] = json_data
        elif data:
            request_kwargs["data"] = data
        
        # 处理认证
        if auth:
            auth_type = auth.get("type", "").lower()
            if auth_type == "basic":
                username = auth.get("username", "")
                password = auth.get("password", "")
                request_kwargs["auth"] = aiohttp.BasicAuth(username, password)
            elif auth_type == "bearer":
                token = auth.get("token", "")
                headers["Authorization"] = f"Bearer {token}"
        
        try:
            # 执行请求
            async with self._session.request(method, **request_kwargs) as response:
                # 获取响应信息
                status_code = response.status
                response_headers = dict(response.headers)
                
                # 检查响应大小
                content_length = response_headers.get("content-length")
                if content_length and int(content_length) > self.max_response_size:
                    return {
                        "success": False,
                        "error": f"响应过大: {content_length} > {self.max_response_size}",
                        "status_code": status_code,
                        "headers": response_headers,
                    }
                
                # 读取响应内容
                try:
                    # 尝试解析为JSON
                    if "application/json" in response_headers.get("content-type", ""):
                        response_data = await response.json()
                        content_type = "json"
                    else:
                        # 读取为文本
                        response_data = await response.text()
                        content_type = "text"
                        
                        # 限制文本长度
                        if len(response_data) > self.max_response_size:
                            response_data = response_data[:self.max_response_size] + "... (内容被截断)"
                
                except Exception as e:
                    # 如果解析失败，尝试读取原始字节
                    response_bytes = await response.read()
                    if len(response_bytes) > self.max_response_size:
                        response_data = f"<二进制数据，大小: {len(response_bytes)} 字节>"
                    else:
                        response_data = response_bytes.decode('utf-8', errors='replace')
                    content_type = "binary"
                
                return {
                    "success": 200 <= status_code < 400,
                    "status_code": status_code,
                    "headers": response_headers,
                    "data": response_data,
                    "content_type": content_type,
                    "url": str(response.url),
                    "method": method,
                }
                
        except asyncio.TimeoutError:
            return {
                "success": False,
                "error": f"请求超时（>{self.timeout}秒）",
                "url": url,
                "method": method,
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"请求失败: {str(e)}",
                "url": url,
                "method": method,
            }
    
    async def close(self):
        """关闭HTTP会话"""
        if self._session:
            await self._session.close()
            self._session = None
    
    def __del__(self):
        """析构函数"""
        if self._session and not self._session.closed:
            # 在事件循环中关闭会话
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self.close())
            except Exception:
                pass
