"""
数据库连接工具

提供数据库连接和查询操作的工具实现。
"""

import json
import sqlite3
from typing import Any, Dict, List, Optional, Union

from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult
from ai_agent_framework.utils.logging_system import logging_system


def create_tool_result(success: bool, result: Any, error: str = None) -> ToolResult:
    """创建工具结果的辅助函数"""
    return ToolResult(tool_call_id="", success=success, result=result, error=error)


class DatabaseTool(ToolInterface):
    """数据库连接工具"""

    def __init__(self, db_path: str = ":memory:"):
        """
        初始化数据库工具

        Args:
            db_path: 数据库路径，默认为内存数据库
        """
        self.db_path = db_path
        self._logger = logging_system.get_logger("database_tool")
        self._connection: Optional[sqlite3.Connection] = None

    @property
    def name(self) -> str:
        return "database"

    @property
    def description(self) -> str:
        return "数据库连接工具，支持SQLite数据库的查询和操作"

    @property
    def parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "enum": [
                        "connect",
                        "disconnect",
                        "query",
                        "execute",
                        "create_table",
                        "insert",
                        "update",
                        "delete",
                        "schema",
                        "tables",
                        "describe",
                    ],
                    "description": "数据库操作类型",
                },
                "sql": {"type": "string", "description": "SQL语句"},
                "table": {"type": "string", "description": "表名"},
                "data": {"type": "object", "description": "数据（用于插入和更新操作）"},
                "where": {"type": "string", "description": "WHERE条件"},
                "limit": {
                    "type": "integer",
                    "description": "限制结果数量",
                    "default": 100,
                },
            },
            "required": ["action"],
        }

    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """验证工具参数"""
        try:
            action = arguments.get("action")
            if not action:
                return False

            valid_actions = [
                "connect",
                "disconnect",
                "query",
                "execute",
                "create_table",
                "insert",
                "update",
                "delete",
                "schema",
                "tables",
                "describe",
            ]

            return action in valid_actions
        except Exception:
            return False

    async def execute(
        self, arguments: Dict[str, Any], context: Optional[Dict[str, Any]] = None
    ) -> ToolResult:
        """执行数据库操作"""
        try:
            action = arguments.get("action")

            if action == "connect":
                return await self._connect()
            elif action == "disconnect":
                return await self._disconnect()
            elif action == "query":
                sql = arguments.get("sql", "")
                limit = arguments.get("limit", 100)
                return await self._query(sql, limit)
            elif action == "execute":
                sql = arguments.get("sql", "")
                return await self._execute_sql(sql)
            elif action == "create_table":
                sql = arguments.get("sql", "")
                return await self._create_table(sql)
            elif action == "insert":
                table = arguments.get("table", "")
                data = arguments.get("data", {})
                return await self._insert_data(table, data)
            elif action == "update":
                table = arguments.get("table", "")
                data = arguments.get("data", {})
                where = arguments.get("where", "")
                return await self._update_data(table, data, where)
            elif action == "delete":
                table = arguments.get("table", "")
                where = arguments.get("where", "")
                return await self._delete_data(table, where)
            elif action == "schema":
                return await self._get_schema()
            elif action == "tables":
                return await self._get_tables()
            elif action == "describe":
                table = arguments.get("table", "")
                return await self._describe_table(table)
            else:
                return ToolResult(success=False, result=f"不支持的数据库操作: {action}")

        except Exception as e:
            self._logger.error(f"数据库操作失败: {e}")
            return ToolResult(success=False, result=f"数据库操作失败: {str(e)}")

    async def _connect(self) -> ToolResult:
        """连接数据库"""
        try:
            if self._connection:
                return ToolResult(success=True, result="数据库已连接")

            self._connection = sqlite3.connect(self.db_path)
            self._connection.row_factory = sqlite3.Row  # 使结果可以按列名访问

            return ToolResult(success=True, result=f"成功连接到数据库: {self.db_path}")

        except Exception as e:
            return ToolResult(success=False, result=f"连接数据库失败: {str(e)}")

    async def _disconnect(self) -> ToolResult:
        """断开数据库连接"""
        try:
            if self._connection:
                self._connection.close()
                self._connection = None

            return ToolResult(success=True, result="数据库连接已断开")

        except Exception as e:
            return ToolResult(success=False, result=f"断开数据库连接失败: {str(e)}")

    async def _ensure_connected(self) -> bool:
        """确保数据库已连接"""
        if not self._connection:
            result = await self._connect()
            return result.success
        return True

    async def _query(self, sql: str, limit: int) -> ToolResult:
        """执行查询"""
        if not sql:
            return ToolResult(success=False, result="SQL语句不能为空")

        if not await self._ensure_connected():
            return ToolResult(success=False, result="数据库连接失败")

        try:
            cursor = self._connection.cursor()
            cursor.execute(sql)

            # 获取结果
            rows = cursor.fetchmany(limit)

            # 转换为字典列表
            results = []
            if rows:
                columns = [description[0] for description in cursor.description]
                for row in rows:
                    results.append(dict(zip(columns, row)))

            return ToolResult(
                success=True,
                result={
                    "rows": results,
                    "count": len(results),
                    "columns": columns if rows else [],
                },
            )

        except Exception as e:
            return ToolResult(success=False, result=f"查询失败: {str(e)}")

    async def _execute_sql(self, sql: str) -> ToolResult:
        """执行SQL语句"""
        if not sql:
            return ToolResult(success=False, result="SQL语句不能为空")

        if not await self._ensure_connected():
            return ToolResult(success=False, result="数据库连接失败")

        try:
            cursor = self._connection.cursor()
            cursor.execute(sql)
            self._connection.commit()

            return ToolResult(
                success=True, result=f"SQL执行成功，影响行数: {cursor.rowcount}"
            )

        except Exception as e:
            return ToolResult(success=False, result=f"SQL执行失败: {str(e)}")

    async def _create_table(self, sql: str) -> ToolResult:
        """创建表"""
        if not sql:
            return ToolResult(success=False, result="CREATE TABLE语句不能为空")

        return await self._execute_sql(sql)

    async def _insert_data(self, table: str, data: Dict[str, Any]) -> ToolResult:
        """插入数据"""
        if not table:
            return ToolResult(success=False, result="表名不能为空")

        if not data:
            return ToolResult(success=False, result="插入数据不能为空")

        # 构建INSERT语句
        columns = list(data.keys())
        placeholders = ["?" for _ in columns]
        values = list(data.values())

        sql = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"

        if not await self._ensure_connected():
            return ToolResult(success=False, result="数据库连接失败")

        try:
            cursor = self._connection.cursor()
            cursor.execute(sql, values)
            self._connection.commit()

            return ToolResult(
                success=True,
                result=f"成功插入数据到表 {table}，行ID: {cursor.lastrowid}",
            )

        except Exception as e:
            return ToolResult(success=False, result=f"插入数据失败: {str(e)}")

    async def _update_data(
        self, table: str, data: Dict[str, Any], where: str
    ) -> ToolResult:
        """更新数据"""
        if not table:
            return ToolResult(success=False, result="表名不能为空")

        if not data:
            return ToolResult(success=False, result="更新数据不能为空")

        # 构建UPDATE语句
        set_clauses = [f"{col} = ?" for col in data.keys()]
        values = list(data.values())

        sql = f"UPDATE {table} SET {', '.join(set_clauses)}"
        if where:
            sql += f" WHERE {where}"

        return await self._execute_sql(sql)

    async def _delete_data(self, table: str, where: str) -> ToolResult:
        """删除数据"""
        if not table:
            return ToolResult(success=False, result="表名不能为空")

        sql = f"DELETE FROM {table}"
        if where:
            sql += f" WHERE {where}"
        else:
            return ToolResult(
                success=False, result="删除操作必须指定WHERE条件以确保安全"
            )

        return await self._execute_sql(sql)

    async def _get_schema(self) -> ToolResult:
        """获取数据库架构"""
        sql = "SELECT name, sql FROM sqlite_master WHERE type='table'"
        return await self._query(sql, 1000)

    async def _get_tables(self) -> ToolResult:
        """获取所有表"""
        sql = "SELECT name FROM sqlite_master WHERE type='table'"
        result = await self._query(sql, 1000)

        if result.success:
            tables = [row["name"] for row in result.result["rows"]]
            return ToolResult(success=True, result={"tables": tables})

        return result

    async def _describe_table(self, table: str) -> ToolResult:
        """描述表结构"""
        if not table:
            return ToolResult(success=False, result="表名不能为空")

        sql = f"PRAGMA table_info({table})"
        result = await self._query(sql, 1000)

        if result.success:
            columns = []
            for row in result.result["rows"]:
                columns.append(
                    {
                        "name": row["name"],
                        "type": row["type"],
                        "not_null": bool(row["notnull"]),
                        "default_value": row["dflt_value"],
                        "primary_key": bool(row["pk"]),
                    }
                )

            return ToolResult(success=True, result={"table": table, "columns": columns})

        return result
