"""
数据处理工具

提供统一的数据处理操作接口，支持JSON/XML解析、CSV处理、数据转换、格式验证等。
适用于AI Agent的数据清洗、格式转换、结构化提取等场景。
"""

import csv
import json
import re
import xml.etree.ElementTree as ET
from io import StringIO
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from urllib.parse import urlparse, parse_qs

from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult
from ai_agent_framework.utils.logging_system import logging_system


class DataProcessingTool(ToolInterface):
    """
    数据处理工具
    
    提供统一的数据处理操作接口，支持多种数据格式的解析、转换和验证。
    适用于AI Agent的数据清洗、格式转换、结构化提取等场景。
    """
    
    def __init__(self):
        """初始化数据处理工具"""
        self._logger = logging_system.get_logger("data_processing_tool")
    
    @property
    def name(self) -> str:
        return "data_processing"
    
    @property
    def description(self) -> str:
        return "数据处理工具，支持JSON/XML解析、CSV处理、数据转换、格式验证等操作"
    
    @property
    def parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "enum": [
                        "parse_json", "format_json", "validate_json",
                        "parse_xml", "format_xml", "xml_to_dict",
                        "parse_csv", "dict_to_csv", "csv_to_dict",
                        "extract_urls", "extract_emails", "extract_phones",
                        "clean_text", "normalize_text", "split_text",
                        "validate_email", "validate_url", "validate_phone",
                        "convert_encoding", "hash_data", "base64_encode",
                        "base64_decode", "url_encode", "url_decode",
                        "parse_query_string", "format_date", "parse_date"
                    ],
                    "description": "要执行的数据处理操作"
                },
                "data": {
                    "type": ["string", "object", "array"],
                    "description": "要处理的数据"
                },
                "format": {
                    "type": "string",
                    "enum": ["json", "xml", "csv", "text", "html"],
                    "description": "数据格式"
                },
                "encoding": {
                    "type": "string",
                    "default": "utf-8",
                    "description": "字符编码"
                },
                "delimiter": {
                    "type": "string",
                    "default": ",",
                    "description": "CSV分隔符"
                },
                "headers": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "CSV列标题"
                },
                "indent": {
                    "type": "integer",
                    "default": 2,
                    "description": "JSON格式化缩进"
                },
                "pattern": {
                    "type": "string",
                    "description": "正则表达式模式"
                },
                "replacement": {
                    "type": "string",
                    "description": "替换字符串"
                },
                "date_format": {
                    "type": "string",
                    "description": "日期格式字符串"
                },
                "options": {
                    "type": "object",
                    "description": "额外选项"
                }
            },
            "required": ["action"],
            "additionalProperties": False
        }
    
    async def execute(
        self,
        arguments: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None,
    ) -> ToolResult:
        """
        执行数据处理操作
        
        Args:
            arguments: 工具参数
            context: 执行上下文
            
        Returns:
            ToolResult: 执行结果
        """
        try:
            action = arguments.get("action")
            if not action:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少必需参数: action"
                )
            
            # 执行对应的操作
            if action == "parse_json":
                result = await self._handle_parse_json(arguments)
            elif action == "format_json":
                result = await self._handle_format_json(arguments)
            elif action == "validate_json":
                result = await self._handle_validate_json(arguments)
            elif action == "parse_xml":
                result = await self._handle_parse_xml(arguments)
            elif action == "format_xml":
                result = await self._handle_format_xml(arguments)
            elif action == "xml_to_dict":
                result = await self._handle_xml_to_dict(arguments)
            elif action == "parse_csv":
                result = await self._handle_parse_csv(arguments)
            elif action == "dict_to_csv":
                result = await self._handle_dict_to_csv(arguments)
            elif action == "csv_to_dict":
                result = await self._handle_csv_to_dict(arguments)
            elif action == "extract_urls":
                result = await self._handle_extract_urls(arguments)
            elif action == "extract_emails":
                result = await self._handle_extract_emails(arguments)
            elif action == "extract_phones":
                result = await self._handle_extract_phones(arguments)
            elif action == "clean_text":
                result = await self._handle_clean_text(arguments)
            elif action == "normalize_text":
                result = await self._handle_normalize_text(arguments)
            elif action == "split_text":
                result = await self._handle_split_text(arguments)
            elif action == "validate_email":
                result = await self._handle_validate_email(arguments)
            elif action == "validate_url":
                result = await self._handle_validate_url(arguments)
            elif action == "validate_phone":
                result = await self._handle_validate_phone(arguments)
            elif action == "convert_encoding":
                result = await self._handle_convert_encoding(arguments)
            elif action == "hash_data":
                result = await self._handle_hash_data(arguments)
            elif action == "base64_encode":
                result = await self._handle_base64_encode(arguments)
            elif action == "base64_decode":
                result = await self._handle_base64_decode(arguments)
            elif action == "url_encode":
                result = await self._handle_url_encode(arguments)
            elif action == "url_decode":
                result = await self._handle_url_decode(arguments)
            elif action == "parse_query_string":
                result = await self._handle_parse_query_string(arguments)
            elif action == "format_date":
                result = await self._handle_format_date(arguments)
            elif action == "parse_date":
                result = await self._handle_parse_date(arguments)
            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"不支持的操作: {action}"
                )
            
            return result
            
        except Exception as e:
            self._logger.error(f"数据处理操作失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"操作失败: {str(e)}"
            )
    
    async def _handle_parse_json(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理JSON解析操作"""
        data = arguments.get("data")
        if data is None:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: data"
            )
        
        try:
            if isinstance(data, str):
                parsed_data = json.loads(data)
            else:
                parsed_data = data
            
            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "parsed": True,
                    "data": parsed_data,
                    "type": type(parsed_data).__name__,
                }
            )
        except json.JSONDecodeError as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"JSON解析失败: {str(e)}"
            )
    
    async def _handle_format_json(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理JSON格式化操作"""
        data = arguments.get("data")
        indent = arguments.get("indent", 2)
        
        if data is None:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: data"
            )
        
        try:
            if isinstance(data, str):
                # 先解析再格式化
                parsed_data = json.loads(data)
            else:
                parsed_data = data
            
            formatted_json = json.dumps(
                parsed_data,
                indent=indent,
                ensure_ascii=False,
                sort_keys=True
            )
            
            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "formatted": True,
                    "data": formatted_json,
                    "indent": indent,
                }
            )
        except (json.JSONDecodeError, TypeError) as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"JSON格式化失败: {str(e)}"
            )
    
    async def _handle_validate_json(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理JSON验证操作"""
        data = arguments.get("data")
        if data is None:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: data"
            )
        
        try:
            if isinstance(data, str):
                json.loads(data)
            else:
                json.dumps(data)
            
            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "valid": True,
                    "message": "JSON格式有效",
                }
            )
        except (json.JSONDecodeError, TypeError) as e:
            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "valid": False,
                    "error": str(e),
                    "message": "JSON格式无效",
                }
            )
    
    async def _handle_parse_xml(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理XML解析操作"""
        data = arguments.get("data")
        if not isinstance(data, str):
            return ToolResult(
                tool_call_id="",
                success=False,
                error="XML数据必须是字符串格式"
            )
        
        try:
            root = ET.fromstring(data)
            
            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "parsed": True,
                    "root_tag": root.tag,
                    "attributes": root.attrib,
                    "text": root.text,
                    "children_count": len(list(root)),
                }
            )
        except ET.ParseError as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"XML解析失败: {str(e)}"
            )
    
    async def _handle_xml_to_dict(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理XML转字典操作"""
        data = arguments.get("data")
        if not isinstance(data, str):
            return ToolResult(
                tool_call_id="",
                success=False,
                error="XML数据必须是字符串格式"
            )
        
        try:
            root = ET.fromstring(data)
            result_dict = self._xml_element_to_dict(root)
            
            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "converted": True,
                    "data": result_dict,
                }
            )
        except ET.ParseError as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"XML转换失败: {str(e)}"
            )
    
    def _xml_element_to_dict(self, element: ET.Element) -> Dict[str, Any]:
        """将XML元素转换为字典"""
        result = {}
        
        # 添加属性
        if element.attrib:
            result["@attributes"] = element.attrib
        
        # 添加文本内容
        if element.text and element.text.strip():
            result["text"] = element.text.strip()
        
        # 添加子元素
        children = {}
        for child in element:
            child_dict = self._xml_element_to_dict(child)
            if child.tag in children:
                # 如果标签已存在，转换为列表
                if not isinstance(children[child.tag], list):
                    children[child.tag] = [children[child.tag]]
                children[child.tag].append(child_dict)
            else:
                children[child.tag] = child_dict
        
        if children:
            result.update(children)
        
        return result if result else element.text
    
    async def _handle_parse_csv(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理CSV解析操作"""
        data = arguments.get("data")
        delimiter = arguments.get("delimiter", ",")
        headers = arguments.get("headers")
        
        if not isinstance(data, str):
            return ToolResult(
                tool_call_id="",
                success=False,
                error="CSV数据必须是字符串格式"
            )
        
        try:
            csv_reader = csv.reader(StringIO(data), delimiter=delimiter)
            rows = list(csv_reader)
            
            if not rows:
                return ToolResult(
                    tool_call_id="",
                    success=True,
                    result={
                        "parsed": True,
                        "rows": [],
                        "row_count": 0,
                        "column_count": 0,
                    }
                )
            
            # 如果没有提供headers，使用第一行作为headers
            if headers is None:
                headers = rows[0]
                data_rows = rows[1:]
            else:
                data_rows = rows
            
            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "parsed": True,
                    "headers": headers,
                    "rows": data_rows,
                    "row_count": len(data_rows),
                    "column_count": len(headers) if headers else 0,
                }
            )
        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"CSV解析失败: {str(e)}"
            )

    async def _handle_extract_urls(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理URL提取操作"""
        data = arguments.get("data")
        if not isinstance(data, str):
            return ToolResult(
                tool_call_id="",
                success=False,
                error="数据必须是字符串格式"
            )

        # URL正则表达式
        url_pattern = r'https?://(?:[-\w.])+(?:[:\d]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:[\w.])*)?)?'
        urls = re.findall(url_pattern, data)

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "extracted": True,
                "urls": urls,
                "count": len(urls),
            }
        )

    async def _handle_extract_emails(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理邮箱提取操作"""
        data = arguments.get("data")
        if not isinstance(data, str):
            return ToolResult(
                tool_call_id="",
                success=False,
                error="数据必须是字符串格式"
            )

        # 邮箱正则表达式
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, data)

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "extracted": True,
                "emails": emails,
                "count": len(emails),
            }
        )

    async def _handle_extract_phones(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理电话号码提取操作"""
        data = arguments.get("data")
        if not isinstance(data, str):
            return ToolResult(
                tool_call_id="",
                success=False,
                error="数据必须是字符串格式"
            )

        # 中国手机号码正则表达式
        phone_patterns = [
            r'1[3-9]\d{9}',  # 11位手机号
            r'1[3-9]\d-\d{4}-\d{4}',  # 带连字符的手机号
            r'\d{3,4}-\d{7,8}',  # 固定电话
        ]

        phones = []
        for pattern in phone_patterns:
            phones.extend(re.findall(pattern, data))

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "extracted": True,
                "phones": phones,
                "count": len(phones),
            }
        )

    async def _handle_clean_text(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理文本清洗操作"""
        data = arguments.get("data")
        if not isinstance(data, str):
            return ToolResult(
                tool_call_id="",
                success=False,
                error="数据必须是字符串格式"
            )

        # 基本文本清洗
        cleaned_text = data.strip()
        # 移除多余的空白字符
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
        # 移除特殊字符（可选）
        options = arguments.get("options", {})
        if options.get("remove_special_chars", False):
            cleaned_text = re.sub(r'[^\w\s\u4e00-\u9fff]', '', cleaned_text)

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "cleaned": True,
                "original_length": len(data),
                "cleaned_length": len(cleaned_text),
                "data": cleaned_text,
            }
        )

    async def _handle_normalize_text(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理文本标准化操作"""
        data = arguments.get("data")
        if not isinstance(data, str):
            return ToolResult(
                tool_call_id="",
                success=False,
                error="数据必须是字符串格式"
            )

        # 文本标准化
        normalized_text = data.lower().strip()

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "normalized": True,
                "data": normalized_text,
            }
        )

    async def _handle_split_text(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理文本分割操作"""
        data = arguments.get("data")
        pattern = arguments.get("pattern", r'\s+')

        if not isinstance(data, str):
            return ToolResult(
                tool_call_id="",
                success=False,
                error="数据必须是字符串格式"
            )

        try:
            parts = re.split(pattern, data)
            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "split": True,
                    "parts": parts,
                    "count": len(parts),
                    "pattern": pattern,
                }
            )
        except re.error as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"正则表达式错误: {str(e)}"
            )

    async def _handle_validate_email(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理邮箱验证操作"""
        data = arguments.get("data")
        if not isinstance(data, str):
            return ToolResult(
                tool_call_id="",
                success=False,
                error="邮箱地址必须是字符串格式"
            )

        email_pattern = r'^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}$'
        is_valid = bool(re.match(email_pattern, data))

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "valid": is_valid,
                "email": data,
                "message": "邮箱格式有效" if is_valid else "邮箱格式无效",
            }
        )

    async def _handle_extract_urls(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理URL提取操作"""
        data = arguments.get("data")
        if not isinstance(data, str):
            return ToolResult(
                tool_call_id="",
                success=False,
                error="数据必须是字符串格式"
            )

        # URL正则表达式
        url_pattern = r'https?://(?:[-\w.])+(?:[:\d]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:[\w.])*)?)?'
        urls = re.findall(url_pattern, data)

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "extracted": True,
                "urls": urls,
                "count": len(urls),
            }
        )

    async def _handle_extract_emails(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理邮箱提取操作"""
        data = arguments.get("data")
        if not isinstance(data, str):
            return ToolResult(
                tool_call_id="",
                success=False,
                error="数据必须是字符串格式"
            )

        # 邮箱正则表达式
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, data)

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "extracted": True,
                "emails": emails,
                "count": len(emails),
            }
        )

    async def _handle_extract_phones(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理电话号码提取操作"""
        data = arguments.get("data")
        if not isinstance(data, str):
            return ToolResult(
                tool_call_id="",
                success=False,
                error="数据必须是字符串格式"
            )

        # 中国手机号码正则表达式
        phone_patterns = [
            r'1[3-9]\d{9}',  # 11位手机号
            r'1[3-9]\d-\d{4}-\d{4}',  # 带连字符的手机号
            r'\d{3,4}-\d{7,8}',  # 固定电话
        ]

        phones = []
        for pattern in phone_patterns:
            phones.extend(re.findall(pattern, data))

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "extracted": True,
                "phones": phones,
                "count": len(phones),
            }
        )

    async def _handle_clean_text(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理文本清洗操作"""
        data = arguments.get("data")
        if not isinstance(data, str):
            return ToolResult(
                tool_call_id="",
                success=False,
                error="数据必须是字符串格式"
            )

        # 基本文本清洗
        cleaned_text = data.strip()
        # 移除多余的空白字符
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
        # 移除特殊字符（可选）
        options = arguments.get("options", {})
        if options.get("remove_special_chars", False):
            cleaned_text = re.sub(r'[^\w\s\u4e00-\u9fff]', '', cleaned_text)

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "cleaned": True,
                "original_length": len(data),
                "cleaned_length": len(cleaned_text),
                "data": cleaned_text,
            }
        )

    async def _handle_normalize_text(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理文本标准化操作"""
        data = arguments.get("data")
        if not isinstance(data, str):
            return ToolResult(
                tool_call_id="",
                success=False,
                error="数据必须是字符串格式"
            )

        # 文本标准化
        normalized_text = data.lower().strip()

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "normalized": True,
                "data": normalized_text,
            }
        )

    async def _handle_split_text(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理文本分割操作"""
        data = arguments.get("data")
        pattern = arguments.get("pattern", r'\s+')

        if not isinstance(data, str):
            return ToolResult(
                tool_call_id="",
                success=False,
                error="数据必须是字符串格式"
            )

        try:
            parts = re.split(pattern, data)
            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "split": True,
                    "parts": parts,
                    "count": len(parts),
                    "pattern": pattern,
                }
            )
        except re.error as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"正则表达式错误: {str(e)}"
            )

    async def _handle_validate_email(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理邮箱验证操作"""
        data = arguments.get("data")
        if not isinstance(data, str):
            return ToolResult(
                tool_call_id="",
                success=False,
                error="邮箱地址必须是字符串格式"
            )

        email_pattern = r'^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}$'
        is_valid = bool(re.match(email_pattern, data))

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "valid": is_valid,
                "email": data,
                "message": "邮箱格式有效" if is_valid else "邮箱格式无效",
            }
        )

    async def _handle_validate_url(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理URL验证操作"""
        data = arguments.get("data")
        if not isinstance(data, str):
            return ToolResult(
                tool_call_id="",
                success=False,
                error="URL必须是字符串格式"
            )

        try:
            parsed = urlparse(data)
            is_valid = bool(parsed.scheme and parsed.netloc)

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "valid": is_valid,
                    "url": data,
                    "scheme": parsed.scheme,
                    "netloc": parsed.netloc,
                    "message": "URL格式有效" if is_valid else "URL格式无效",
                }
            )
        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "valid": False,
                    "url": data,
                    "error": str(e),
                    "message": "URL格式无效",
                }
            )

    async def _handle_validate_phone(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理电话号码验证操作"""
        data = arguments.get("data")
        if not isinstance(data, str):
            return ToolResult(
                tool_call_id="",
                success=False,
                error="电话号码必须是字符串格式"
            )

        # 中国手机号码验证
        phone_pattern = r'^1[3-9]\d{9}$'
        is_valid = bool(re.match(phone_pattern, data))

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "valid": is_valid,
                "phone": data,
                "message": "手机号码格式有效" if is_valid else "手机号码格式无效",
            }
        )
