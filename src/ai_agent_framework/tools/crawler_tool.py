"""
本地爬虫服务调用工具

支持调用本地部署的爬虫应用（如crawl4ai），提供网页抓取、内容提取、
结构化数据解析等功能。支持多种爬虫服务接口和配置选项。
"""

import asyncio
import json
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urljoin, urlparse
from enum import Enum

try:
    import aiohttp
except ImportError:
    aiohttp = None

from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult
from ai_agent_framework.utils.logging_system import logging_system


class CrawlerServiceType(Enum):
    """爬虫服务类型枚举"""
    CRAWL4AI = "crawl4ai"
    SCRAPY_SPLASH = "scrapy_splash"
    SELENIUM_GRID = "selenium_grid"
    CUSTOM = "custom"


class CrawlerTool(ToolInterface):
    """
    本地爬虫服务调用工具
    
    支持调用本地部署的爬虫服务，提供网页抓取、内容提取、
    结构化数据解析等功能。
    """
    
    def __init__(
        self,
        service_url: str = "http://localhost:8000",
        service_type: CrawlerServiceType = CrawlerServiceType.CRAWL4AI,
        timeout: int = 60,
        max_response_size: int = 50 * 1024 * 1024,  # 50MB
        user_agent: str = "AI-Agent-Framework-Crawler/1.0",
        enable_javascript: bool = True,
        enable_images: bool = False,
        wait_for_selector: Optional[str] = None,
        custom_headers: Optional[Dict[str, str]] = None,
        proxy_config: Optional[Dict[str, str]] = None,
    ):
        """
        初始化爬虫工具
        
        Args:
            service_url: 爬虫服务的基础URL
            service_type: 爬虫服务类型
            timeout: 请求超时时间（秒）
            max_response_size: 最大响应大小（字节）
            user_agent: 用户代理字符串
            enable_javascript: 是否启用JavaScript执行
            enable_images: 是否加载图片
            wait_for_selector: 等待特定CSS选择器出现
            custom_headers: 自定义请求头
            proxy_config: 代理配置
        """
        self.service_url = service_url.rstrip('/')
        self.service_type = service_type
        self.timeout = timeout
        self.max_response_size = max_response_size
        self.user_agent = user_agent
        self.enable_javascript = enable_javascript
        self.enable_images = enable_images
        self.wait_for_selector = wait_for_selector
        self.custom_headers = custom_headers or {}
        self.proxy_config = proxy_config or {}
        
        self._logger = logging_system.get_logger("crawler_tool")
        self._session: Optional[aiohttp.ClientSession] = None
    
    @property
    def name(self) -> str:
        """工具名称"""
        return "web_crawler"
    
    @property
    def description(self) -> str:
        """工具描述"""
        return (
            "本地爬虫服务调用工具，支持网页抓取、内容提取、结构化数据解析。"
            "可以处理JavaScript渲染的页面，提取文本、链接、图片等信息。"
        )
    
    @property
    def parameters_schema(self) -> Dict[str, Any]:
        """工具参数JSON Schema"""
        return {
            "type": "object",
            "properties": {
                "url": {
                    "type": "string",
                    "description": "要抓取的网页URL"
                },
                "action": {
                    "type": "string",
                    "enum": [
                        "crawl",           # 基础爬取
                        "extract_text",    # 提取文本内容
                        "extract_links",   # 提取链接
                        "extract_images",  # 提取图片
                        "extract_data",    # 结构化数据提取
                        "screenshot",      # 截图
                        "pdf_export"       # 导出PDF
                    ],
                    "default": "crawl",
                    "description": "执行的操作类型"
                },
                "selectors": {
                    "type": "object",
                    "properties": {
                        "title": {"type": "string", "description": "标题选择器"},
                        "content": {"type": "string", "description": "内容选择器"},
                        "links": {"type": "string", "description": "链接选择器"},
                        "images": {"type": "string", "description": "图片选择器"},
                        "custom": {"type": "object", "description": "自定义选择器"}
                    },
                    "description": "CSS选择器配置"
                },
                "options": {
                    "type": "object",
                    "properties": {
                        "wait_time": {
                            "type": "integer",
                            "default": 3,
                            "description": "页面加载等待时间（秒）"
                        },
                        "enable_javascript": {
                            "type": "boolean",
                            "description": "是否启用JavaScript（覆盖默认设置）"
                        },
                        "enable_images": {
                            "type": "boolean",
                            "description": "是否加载图片（覆盖默认设置）"
                        },
                        "viewport": {
                            "type": "object",
                            "properties": {
                                "width": {"type": "integer", "default": 1920},
                                "height": {"type": "integer", "default": 1080}
                            },
                            "description": "浏览器视口大小"
                        },
                        "user_agent": {
                            "type": "string",
                            "description": "自定义用户代理（覆盖默认设置）"
                        },
                        "headers": {
                            "type": "object",
                            "description": "自定义请求头"
                        },
                        "cookies": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "name": {"type": "string"},
                                    "value": {"type": "string"},
                                    "domain": {"type": "string"},
                                    "path": {"type": "string"}
                                }
                            },
                            "description": "Cookie设置"
                        },
                        "extract_schema": {
                            "type": "object",
                            "description": "结构化数据提取模式"
                        }
                    },
                    "description": "爬取选项配置"
                },
                "format": {
                    "type": "string",
                    "enum": ["json", "html", "text", "markdown"],
                    "default": "json",
                    "description": "返回数据格式"
                }
            },
            "required": ["url"]
        }
    
    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """验证工具参数"""
        try:
            url = arguments.get("url")
            if not url:
                return False
            
            # 验证URL格式
            parsed_url = urlparse(url)
            if not parsed_url.scheme or not parsed_url.netloc:
                self._logger.warning(f"无效的URL格式: {url}")
                return False
            
            # 验证操作类型
            action = arguments.get("action", "crawl")
            valid_actions = [
                "crawl", "extract_text", "extract_links", 
                "extract_images", "extract_data", "screenshot", "pdf_export"
            ]
            if action not in valid_actions:
                self._logger.warning(f"无效的操作类型: {action}")
                return False
            
            return True
        except Exception as e:
            self._logger.error(f"参数验证失败: {e}")
            return False
    
    async def execute(
        self, 
        arguments: Dict[str, Any], 
        context: Optional[Dict[str, Any]] = None
    ) -> ToolResult:
        """执行爬虫任务"""
        try:
            if aiohttp is None:
                return ToolResult(
                    success=False,
                    result="需要安装aiohttp: pip install aiohttp"
                )
            
            # 验证参数
            if not await self.validate_arguments(arguments):
                return ToolResult(
                    success=False,
                    result="爬虫参数验证失败"
                )
            
            # 初始化会话（如果需要）
            if not self._session:
                await self._init_session()
            
            # 执行爬虫任务
            result = await self._execute_crawl_task(arguments)
            
            return ToolResult(
                success=result["success"],
                result=result
            )
            
        except Exception as e:
            self._logger.error(f"爬虫任务执行失败: {e}")
            return ToolResult(
                success=False,
                result=f"爬虫任务执行失败: {str(e)}"
            )
    
    async def _init_session(self):
        """初始化HTTP会话"""
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        headers = {
            "User-Agent": self.user_agent,
            "Content-Type": "application/json",
            **self.custom_headers
        }
        
        self._session = aiohttp.ClientSession(
            timeout=timeout,
            headers=headers
        )
        
        self._logger.info("HTTP会话已初始化")

    async def _execute_crawl_task(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """执行爬虫任务"""
        url = arguments["url"]
        action = arguments.get("action", "crawl")
        selectors = arguments.get("selectors", {})
        options = arguments.get("options", {})
        format_type = arguments.get("format", "json")

        # 构建请求数据
        request_data = self._build_request_data(url, action, selectors, options, format_type)

        try:
            # 根据服务类型选择不同的API端点
            endpoint = self._get_api_endpoint(action)
            full_url = f"{self.service_url}{endpoint}"

            self._logger.info(f"发送爬虫请求到: {full_url}")
            self._logger.debug(f"请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")

            # 发送请求到爬虫服务
            async with self._session.post(full_url, json=request_data) as response:
                status_code = response.status
                response_headers = dict(response.headers)

                # 检查响应状态
                if status_code != 200:
                    error_text = await response.text()
                    return {
                        "success": False,
                        "error": f"爬虫服务返回错误状态: {status_code}",
                        "details": error_text,
                        "url": url,
                        "action": action
                    }

                # 检查响应大小
                content_length = response_headers.get("content-length")
                if content_length and int(content_length) > self.max_response_size:
                    return {
                        "success": False,
                        "error": f"响应过大: {content_length} > {self.max_response_size}",
                        "url": url,
                        "action": action
                    }

                # 解析响应数据
                try:
                    response_data = await response.json()
                except json.JSONDecodeError:
                    # 如果不是JSON，尝试读取为文本
                    response_data = await response.text()
                    if len(response_data) > self.max_response_size:
                        response_data = response_data[:self.max_response_size] + "... (内容被截断)"

                # 处理响应数据
                processed_result = self._process_response_data(response_data, action, format_type)

                self._logger.info(f"爬虫任务完成: {url} ({action})")

                return {
                    "success": True,
                    "url": url,
                    "action": action,
                    "format": format_type,
                    "data": processed_result,
                    "metadata": {
                        "status_code": status_code,
                        "response_headers": response_headers,
                        "service_type": self.service_type.value,
                        "timestamp": asyncio.get_event_loop().time()
                    }
                }

        except asyncio.TimeoutError:
            return {
                "success": False,
                "error": f"爬虫请求超时（>{self.timeout}秒）",
                "url": url,
                "action": action
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"爬虫请求失败: {str(e)}",
                "url": url,
                "action": action
            }

    def _build_request_data(
        self,
        url: str,
        action: str,
        selectors: Dict[str, Any],
        options: Dict[str, Any],
        format_type: str
    ) -> Dict[str, Any]:
        """构建发送给爬虫服务的请求数据"""

        # 基础请求数据
        request_data = {
            "url": url,
            "action": action,
            "format": format_type
        }

        # 根据服务类型构建不同的请求格式
        if self.service_type == CrawlerServiceType.CRAWL4AI:
            request_data.update(self._build_crawl4ai_request(selectors, options))
        elif self.service_type == CrawlerServiceType.SCRAPY_SPLASH:
            request_data.update(self._build_scrapy_splash_request(selectors, options))
        elif self.service_type == CrawlerServiceType.SELENIUM_GRID:
            request_data.update(self._build_selenium_grid_request(selectors, options))
        else:
            # 自定义服务类型，使用通用格式
            request_data.update({
                "selectors": selectors,
                "options": options
            })

        return request_data

    def _build_crawl4ai_request(self, selectors: Dict[str, Any], options: Dict[str, Any]) -> Dict[str, Any]:
        """构建Crawl4AI服务的请求数据"""
        crawl4ai_data = {}

        # JavaScript执行配置
        js_enabled = options.get("enable_javascript", self.enable_javascript)
        crawl4ai_data["js_code"] = None if not js_enabled else ""

        # 等待时间配置
        wait_time = options.get("wait_time", 3)
        crawl4ai_data["wait_for"] = wait_time

        # 用户代理配置
        user_agent = options.get("user_agent", self.user_agent)
        crawl4ai_data["user_agent"] = user_agent

        # 视口配置
        viewport = options.get("viewport", {"width": 1920, "height": 1080})
        crawl4ai_data["viewport_width"] = viewport.get("width", 1920)
        crawl4ai_data["viewport_height"] = viewport.get("height", 1080)

        # 请求头配置
        headers = options.get("headers", {})
        if headers:
            crawl4ai_data["headers"] = headers

        # Cookie配置
        cookies = options.get("cookies", [])
        if cookies:
            crawl4ai_data["cookies"] = cookies

        # CSS选择器配置
        if selectors:
            crawl4ai_data["css_selector"] = selectors.get("content", "body")
            if "custom" in selectors:
                crawl4ai_data["extraction_strategy"] = {
                    "type": "css_extractor",
                    "selectors": selectors["custom"]
                }

        # 结构化数据提取配置
        extract_schema = options.get("extract_schema")
        if extract_schema:
            crawl4ai_data["extraction_strategy"] = {
                "type": "json_css_extractor",
                "schema": extract_schema
            }

        return crawl4ai_data

    def _build_scrapy_splash_request(self, selectors: Dict[str, Any], options: Dict[str, Any]) -> Dict[str, Any]:
        """构建Scrapy Splash服务的请求数据"""
        splash_data = {}

        # 等待时间配置
        wait_time = options.get("wait_time", 3)
        splash_data["wait"] = wait_time

        # JavaScript执行配置
        js_enabled = options.get("enable_javascript", self.enable_javascript)
        if js_enabled:
            splash_data["html"] = 1
            splash_data["png"] = 0
        else:
            splash_data["html"] = 1
            splash_data["png"] = 0
            splash_data["js"] = 0

        # 图片加载配置
        images_enabled = options.get("enable_images", self.enable_images)
        splash_data["images"] = 1 if images_enabled else 0

        # 视口配置
        viewport = options.get("viewport", {"width": 1920, "height": 1080})
        splash_data["viewport"] = f"{viewport.get('width', 1920)}x{viewport.get('height', 1080)}"

        # 用户代理配置
        user_agent = options.get("user_agent", self.user_agent)
        splash_data["user_agent"] = user_agent

        # 请求头配置
        headers = options.get("headers", {})
        if headers:
            splash_data["headers"] = headers

        # Lua脚本配置（用于复杂的页面交互）
        if selectors or options.get("extract_schema"):
            lua_script = self._generate_lua_script(selectors, options)
            splash_data["lua_source"] = lua_script

        return splash_data

    def _build_selenium_grid_request(self, selectors: Dict[str, Any], options: Dict[str, Any]) -> Dict[str, Any]:
        """构建Selenium Grid服务的请求数据"""
        selenium_data = {}

        # 浏览器配置
        selenium_data["browser"] = "chrome"  # 默认使用Chrome

        # 等待时间配置
        wait_time = options.get("wait_time", 3)
        selenium_data["implicit_wait"] = wait_time

        # 视口配置
        viewport = options.get("viewport", {"width": 1920, "height": 1080})
        selenium_data["window_size"] = {
            "width": viewport.get("width", 1920),
            "height": viewport.get("height", 1080)
        }

        # 用户代理配置
        user_agent = options.get("user_agent", self.user_agent)
        selenium_data["user_agent"] = user_agent

        # Chrome选项配置
        chrome_options = []
        if not options.get("enable_images", self.enable_images):
            chrome_options.append("--blink-settings=imagesEnabled=false")
        if not options.get("enable_javascript", self.enable_javascript):
            chrome_options.append("--disable-javascript")

        selenium_data["chrome_options"] = chrome_options

        # 选择器配置
        if selectors:
            selenium_data["selectors"] = selectors

        # 结构化数据提取配置
        extract_schema = options.get("extract_schema")
        if extract_schema:
            selenium_data["extract_schema"] = extract_schema

        return selenium_data

    def _generate_lua_script(self, selectors: Dict[str, Any], options: Dict[str, Any]) -> str:
        """为Scrapy Splash生成Lua脚本"""
        lua_script = """
function main(splash, args)
    splash:go(args.url)
    splash:wait(args.wait or 3)

    local result = {}
    result.html = splash:html()
    result.url = splash:url()

    -- 提取选择器数据
    if args.selectors then
        result.extracted = {}
        for key, selector in pairs(args.selectors) do
            local elements = splash:select_all(selector)
            local values = {}
            for i, element in ipairs(elements) do
                table.insert(values, element:text())
            end
            result.extracted[key] = values
        end
    end

    return result
end
"""
        return lua_script

    def _get_api_endpoint(self, action: str) -> str:
        """根据操作类型和服务类型获取API端点"""
        if self.service_type == CrawlerServiceType.CRAWL4AI:
            endpoint_map = {
                "crawl": "/crawl",
                "extract_text": "/extract/text",
                "extract_links": "/extract/links",
                "extract_images": "/extract/images",
                "extract_data": "/extract/data",
                "screenshot": "/screenshot",
                "pdf_export": "/pdf"
            }
        elif self.service_type == CrawlerServiceType.SCRAPY_SPLASH:
            endpoint_map = {
                "crawl": "/render.json",
                "extract_text": "/render.json",
                "extract_links": "/render.json",
                "extract_images": "/render.json",
                "extract_data": "/execute",
                "screenshot": "/render.png",
                "pdf_export": "/render.pdf"
            }
        elif self.service_type == CrawlerServiceType.SELENIUM_GRID:
            endpoint_map = {
                "crawl": "/session",
                "extract_text": "/session",
                "extract_links": "/session",
                "extract_images": "/session",
                "extract_data": "/session",
                "screenshot": "/screenshot",
                "pdf_export": "/pdf"
            }
        else:
            # 自定义服务类型，使用通用端点
            endpoint_map = {
                "crawl": "/crawl",
                "extract_text": "/extract/text",
                "extract_links": "/extract/links",
                "extract_images": "/extract/images",
                "extract_data": "/extract/data",
                "screenshot": "/screenshot",
                "pdf_export": "/pdf"
            }

        return endpoint_map.get(action, "/crawl")

    def _process_response_data(self, response_data: Any, action: str, format_type: str) -> Any:
        """处理爬虫服务返回的响应数据"""
        try:
            # 如果响应数据是字符串，尝试解析为JSON
            if isinstance(response_data, str):
                try:
                    response_data = json.loads(response_data)
                except json.JSONDecodeError:
                    # 如果不是JSON，直接返回文本
                    return {"content": response_data, "type": "text"}

            # 根据操作类型处理数据
            if action == "crawl":
                return self._process_crawl_response(response_data, format_type)
            elif action == "extract_text":
                return self._process_text_extraction(response_data)
            elif action == "extract_links":
                return self._process_links_extraction(response_data)
            elif action == "extract_images":
                return self._process_images_extraction(response_data)
            elif action == "extract_data":
                return self._process_data_extraction(response_data)
            elif action == "screenshot":
                return self._process_screenshot_response(response_data)
            elif action == "pdf_export":
                return self._process_pdf_response(response_data)
            else:
                return response_data

        except Exception as e:
            self._logger.error(f"响应数据处理失败: {e}")
            return {"error": f"响应数据处理失败: {str(e)}", "raw_data": response_data}

    def _process_crawl_response(self, data: Any, format_type: str) -> Dict[str, Any]:
        """处理基础爬取响应"""
        result = {
            "type": "crawl",
            "format": format_type
        }

        if isinstance(data, dict):
            # 提取常见字段
            result["title"] = data.get("title", "")
            result["content"] = data.get("content", data.get("html", data.get("text", "")))
            result["url"] = data.get("url", "")
            result["links"] = data.get("links", [])
            result["images"] = data.get("images", [])
            result["metadata"] = data.get("metadata", {})

            # 如果有提取的数据
            if "extracted" in data:
                result["extracted_data"] = data["extracted"]
        else:
            result["content"] = str(data)

        return result

    def _process_text_extraction(self, data: Any) -> Dict[str, Any]:
        """处理文本提取响应"""
        result = {"type": "text_extraction"}

        if isinstance(data, dict):
            result["text"] = data.get("text", data.get("content", ""))
            result["word_count"] = len(result["text"].split()) if result["text"] else 0
            result["char_count"] = len(result["text"]) if result["text"] else 0
        else:
            result["text"] = str(data)
            result["word_count"] = len(result["text"].split())
            result["char_count"] = len(result["text"])

        return result

    def _process_links_extraction(self, data: Any) -> Dict[str, Any]:
        """处理链接提取响应"""
        result = {"type": "links_extraction"}

        if isinstance(data, dict):
            links = data.get("links", [])
        elif isinstance(data, list):
            links = data
        else:
            links = []

        # 处理链接数据
        processed_links = []
        for link in links:
            if isinstance(link, dict):
                processed_links.append({
                    "url": link.get("url", link.get("href", "")),
                    "text": link.get("text", link.get("title", "")),
                    "type": link.get("type", "unknown")
                })
            elif isinstance(link, str):
                processed_links.append({
                    "url": link,
                    "text": "",
                    "type": "unknown"
                })

        result["links"] = processed_links
        result["count"] = len(processed_links)

        return result

    def _process_images_extraction(self, data: Any) -> Dict[str, Any]:
        """处理图片提取响应"""
        result = {"type": "images_extraction"}

        if isinstance(data, dict):
            images = data.get("images", [])
        elif isinstance(data, list):
            images = data
        else:
            images = []

        # 处理图片数据
        processed_images = []
        for image in images:
            if isinstance(image, dict):
                processed_images.append({
                    "url": image.get("url", image.get("src", "")),
                    "alt": image.get("alt", ""),
                    "title": image.get("title", ""),
                    "width": image.get("width"),
                    "height": image.get("height")
                })
            elif isinstance(image, str):
                processed_images.append({
                    "url": image,
                    "alt": "",
                    "title": "",
                    "width": None,
                    "height": None
                })

        result["images"] = processed_images
        result["count"] = len(processed_images)

        return result

    def _process_data_extraction(self, data: Any) -> Dict[str, Any]:
        """处理结构化数据提取响应"""
        result = {"type": "data_extraction"}

        if isinstance(data, dict):
            result["extracted_data"] = data.get("extracted", data.get("data", data))
        else:
            result["extracted_data"] = data

        return result

    def _process_screenshot_response(self, data: Any) -> Dict[str, Any]:
        """处理截图响应"""
        result = {"type": "screenshot"}

        if isinstance(data, dict):
            result["image_data"] = data.get("image", data.get("screenshot", ""))
            result["format"] = data.get("format", "png")
            result["size"] = data.get("size", {})
        else:
            result["image_data"] = str(data)
            result["format"] = "png"
            result["size"] = {}

        return result

    def _process_pdf_response(self, data: Any) -> Dict[str, Any]:
        """处理PDF导出响应"""
        result = {"type": "pdf_export"}

        if isinstance(data, dict):
            result["pdf_data"] = data.get("pdf", data.get("data", ""))
            result["size"] = data.get("size", 0)
        else:
            result["pdf_data"] = str(data)
            result["size"] = len(result["pdf_data"])

        return result

    async def close(self):
        """关闭HTTP会话"""
        if self._session:
            await self._session.close()
            self._session = None
            self._logger.info("HTTP会话已关闭")

    def __del__(self):
        """析构函数"""
        if self._session and not self._session.closed:
            # 在事件循环中关闭会话
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self.close())
            except Exception:
                pass
