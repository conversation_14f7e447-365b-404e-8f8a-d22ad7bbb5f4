"""
缓存服务工具

提供统一的缓存操作接口，支持Redis、Memcached、本地缓存等多种缓存系统。
适用于AI Agent的数据缓存、会话状态管理、计算结果缓存等场景。
"""

import asyncio
import json
import pickle
import time
from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta

from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult
from ai_agent_framework.utils.logging_system import logging_system


class CacheType(Enum):
    """缓存类型枚举"""
    MEMORY = "memory"
    REDIS = "redis"
    MEMCACHED = "memcached"
    FILE = "file"


class CacheEntry:
    """缓存条目数据结构"""
    
    def __init__(
        self,
        key: str,
        value: Any,
        ttl_seconds: Optional[int] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """
        初始化缓存条目
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl_seconds: 生存时间（秒）
            tags: 标签列表，用于批量操作
            metadata: 元数据
        """
        self.key = key
        self.value = value
        self.ttl_seconds = ttl_seconds
        self.tags = tags or []
        self.metadata = metadata or {}
        self.created_at = time.time()
        self.accessed_at = self.created_at
        self.access_count = 0
    
    def is_expired(self) -> bool:
        """检查是否已过期"""
        if self.ttl_seconds is None:
            return False
        return time.time() - self.created_at > self.ttl_seconds
    
    def touch(self) -> None:
        """更新访问时间和计数"""
        self.accessed_at = time.time()
        self.access_count += 1
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "key": self.key,
            "value": self.value,
            "ttl_seconds": self.ttl_seconds,
            "tags": self.tags,
            "metadata": self.metadata,
            "created_at": self.created_at,
            "accessed_at": self.accessed_at,
            "access_count": self.access_count,
            "is_expired": self.is_expired(),
        }


class CacheAdapter(ABC):
    """缓存适配器抽象基类"""
    
    @abstractmethod
    async def connect(self) -> bool:
        """连接到缓存系统"""
        pass
    
    @abstractmethod
    async def disconnect(self) -> bool:
        """断开连接"""
        pass
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        pass
    
    @abstractmethod
    async def set(
        self,
        key: str,
        value: Any,
        ttl_seconds: Optional[int] = None,
        tags: Optional[List[str]] = None,
    ) -> bool:
        """设置缓存值"""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """删除缓存项"""
        pass
    
    @abstractmethod
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        pass
    
    @abstractmethod
    async def clear(self) -> int:
        """清空所有缓存，返回清除的项目数"""
        pass
    
    @abstractmethod
    async def keys(self, pattern: str = "*") -> List[str]:
        """获取匹配模式的键列表"""
        pass
    
    @abstractmethod
    async def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        pass


class MemoryCacheAdapter(CacheAdapter):
    """内存缓存适配器"""
    
    def __init__(self, max_size: int = 1000):
        """
        初始化内存缓存适配器
        
        Args:
            max_size: 最大缓存项数量
        """
        self.max_size = max_size
        self._cache: Dict[str, CacheEntry] = {}
        self._connected = False
        self._logger = logging_system.get_logger("memory_cache")
        self._hits = 0
        self._misses = 0
    
    async def connect(self) -> bool:
        """连接到内存缓存"""
        self._connected = True
        self._logger.info("已连接到内存缓存")
        return True
    
    async def disconnect(self) -> bool:
        """断开连接"""
        self._connected = False
        self._logger.info("已断开内存缓存连接")
        return True
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if not self._connected:
            return None
        
        entry = self._cache.get(key)
        if entry is None:
            self._misses += 1
            return None
        
        if entry.is_expired():
            del self._cache[key]
            self._misses += 1
            return None
        
        entry.touch()
        self._hits += 1
        return entry.value
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl_seconds: Optional[int] = None,
        tags: Optional[List[str]] = None,
    ) -> bool:
        """设置缓存值"""
        if not self._connected:
            return False
        
        # 如果缓存已满，删除最旧的项
        if len(self._cache) >= self.max_size and key not in self._cache:
            self._evict_oldest()
        
        entry = CacheEntry(key, value, ttl_seconds, tags)
        self._cache[key] = entry
        
        self._logger.debug(f"缓存已设置: {key}")
        return True
    
    async def delete(self, key: str) -> bool:
        """删除缓存项"""
        if not self._connected:
            return False
        
        if key in self._cache:
            del self._cache[key]
            self._logger.debug(f"缓存已删除: {key}")
            return True
        return False
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        if not self._connected:
            return False
        
        entry = self._cache.get(key)
        if entry is None:
            return False
        
        if entry.is_expired():
            del self._cache[key]
            return False
        
        return True
    
    async def clear(self) -> int:
        """清空所有缓存"""
        if not self._connected:
            return 0
        
        count = len(self._cache)
        self._cache.clear()
        self._hits = 0
        self._misses = 0
        
        self._logger.info(f"已清空缓存，清除 {count} 个项目")
        return count
    
    async def keys(self, pattern: str = "*") -> List[str]:
        """获取匹配模式的键列表"""
        if not self._connected:
            return []
        
        # 清理过期项
        expired_keys = [
            key for key, entry in self._cache.items()
            if entry.is_expired()
        ]
        for key in expired_keys:
            del self._cache[key]
        
        # 简单的模式匹配（支持*通配符）
        if pattern == "*":
            return list(self._cache.keys())
        
        import fnmatch
        return [key for key in self._cache.keys() if fnmatch.fnmatch(key, pattern)]
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self._hits + self._misses
        hit_rate = self._hits / total_requests if total_requests > 0 else 0
        
        return {
            "type": "memory",
            "connected": self._connected,
            "total_items": len(self._cache),
            "max_size": self.max_size,
            "hits": self._hits,
            "misses": self._misses,
            "hit_rate": hit_rate,
            "memory_usage_mb": self._estimate_memory_usage(),
        }
    
    def _evict_oldest(self) -> None:
        """删除最旧的缓存项"""
        if not self._cache:
            return
        
        oldest_key = min(
            self._cache.keys(),
            key=lambda k: self._cache[k].accessed_at
        )
        del self._cache[oldest_key]
        self._logger.debug(f"已驱逐最旧的缓存项: {oldest_key}")
    
    def _estimate_memory_usage(self) -> float:
        """估算内存使用量（MB）"""
        try:
            import sys
            total_size = 0
            for entry in self._cache.values():
                total_size += sys.getsizeof(entry.key)
                total_size += sys.getsizeof(entry.value)
                total_size += sys.getsizeof(entry)
            return total_size / (1024 * 1024)  # 转换为MB
        except Exception:
            return 0.0


class CacheTool(ToolInterface):
    """
    缓存服务工具
    
    提供统一的缓存操作接口，支持多种缓存系统。
    适用于AI Agent的数据缓存、会话状态管理、计算结果缓存等场景。
    """
    
    def __init__(
        self,
        cache_type: CacheType = CacheType.MEMORY,
        connection_config: Optional[Dict[str, Any]] = None,
        default_ttl: Optional[int] = None,
    ):
        """
        初始化缓存工具
        
        Args:
            cache_type: 缓存类型
            connection_config: 连接配置
            default_ttl: 默认生存时间（秒）
        """
        self.cache_type = cache_type
        self.connection_config = connection_config or {}
        self.default_ttl = default_ttl
        
        self._logger = logging_system.get_logger("cache_tool")
        self._adapter: Optional[CacheAdapter] = None
        self._initialize_adapter()
    
    def _initialize_adapter(self) -> None:
        """初始化缓存适配器"""
        if self.cache_type == CacheType.MEMORY:
            max_size = self.connection_config.get("max_size", 1000)
            self._adapter = MemoryCacheAdapter(max_size)
        elif self.cache_type == CacheType.REDIS:
            # TODO: 实现Redis适配器
            raise NotImplementedError("Redis适配器尚未实现")
        elif self.cache_type == CacheType.MEMCACHED:
            # TODO: 实现Memcached适配器
            raise NotImplementedError("Memcached适配器尚未实现")
        elif self.cache_type == CacheType.FILE:
            # TODO: 实现文件缓存适配器
            raise NotImplementedError("文件缓存适配器尚未实现")
        else:
            raise ValueError(f"不支持的缓存类型: {self.cache_type}")
    
    @property
    def name(self) -> str:
        return "cache"
    
    @property
    def description(self) -> str:
        return "缓存服务工具，支持数据缓存、会话状态管理、计算结果缓存等操作"

    @property
    def parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "enum": [
                        "connect", "disconnect", "get", "set", "delete",
                        "exists", "clear", "keys", "stats", "get_multi",
                        "set_multi", "delete_multi", "increment", "decrement"
                    ],
                    "description": "要执行的操作"
                },
                "key": {
                    "type": "string",
                    "description": "缓存键"
                },
                "value": {
                    "type": ["string", "number", "object", "array", "boolean"],
                    "description": "缓存值"
                },
                "keys": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "缓存键列表（用于批量操作）"
                },
                "key_value_pairs": {
                    "type": "object",
                    "description": "键值对字典（用于批量设置）"
                },
                "ttl_seconds": {
                    "type": "integer",
                    "minimum": 1,
                    "description": "生存时间（秒）"
                },
                "tags": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "标签列表"
                },
                "pattern": {
                    "type": "string",
                    "default": "*",
                    "description": "键匹配模式（支持*通配符）"
                },
                "delta": {
                    "type": "number",
                    "default": 1,
                    "description": "增减量"
                }
            },
            "required": ["action"],
            "additionalProperties": False
        }

    async def execute(
        self,
        arguments: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None,
    ) -> ToolResult:
        """
        执行缓存操作

        Args:
            arguments: 工具参数
            context: 执行上下文

        Returns:
            ToolResult: 执行结果
        """
        try:
            action = arguments.get("action")
            if not action:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少必需参数: action"
                )

            # 确保适配器已初始化
            if not self._adapter:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缓存适配器未初始化"
                )

            # 执行对应的操作
            if action == "connect":
                result = await self._handle_connect()
            elif action == "disconnect":
                result = await self._handle_disconnect()
            elif action == "get":
                result = await self._handle_get(arguments)
            elif action == "set":
                result = await self._handle_set(arguments)
            elif action == "delete":
                result = await self._handle_delete(arguments)
            elif action == "exists":
                result = await self._handle_exists(arguments)
            elif action == "clear":
                result = await self._handle_clear()
            elif action == "keys":
                result = await self._handle_keys(arguments)
            elif action == "stats":
                result = await self._handle_stats()
            elif action == "get_multi":
                result = await self._handle_get_multi(arguments)
            elif action == "set_multi":
                result = await self._handle_set_multi(arguments)
            elif action == "delete_multi":
                result = await self._handle_delete_multi(arguments)
            elif action == "increment":
                result = await self._handle_increment(arguments)
            elif action == "decrement":
                result = await self._handle_decrement(arguments)
            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"不支持的操作: {action}"
                )

            return result

        except Exception as e:
            self._logger.error(f"缓存操作失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"操作失败: {str(e)}"
            )

    async def _handle_connect(self) -> ToolResult:
        """处理连接操作"""
        success = await self._adapter.connect()
        return ToolResult(
            tool_call_id="",
            success=success,
            result={"connected": success, "cache_type": self.cache_type.value}
        )

    async def _handle_disconnect(self) -> ToolResult:
        """处理断开连接操作"""
        success = await self._adapter.disconnect()
        return ToolResult(
            tool_call_id="",
            success=success,
            result={"disconnected": success}
        )

    async def _handle_get(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理获取缓存操作"""
        key = arguments.get("key")
        if not key:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: key"
            )

        value = await self._adapter.get(key)

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "key": key,
                "value": value,
                "found": value is not None,
            }
        )

    async def _handle_set(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理设置缓存操作"""
        key = arguments.get("key")
        value = arguments.get("value")

        if not key:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: key"
            )

        if value is None:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: value"
            )

        ttl_seconds = arguments.get("ttl_seconds", self.default_ttl)
        tags = arguments.get("tags", [])

        success = await self._adapter.set(key, value, ttl_seconds, tags)

        return ToolResult(
            tool_call_id="",
            success=success,
            result={
                "key": key,
                "set": success,
                "ttl_seconds": ttl_seconds,
                "tags": tags,
            }
        )

    async def _handle_delete(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理删除缓存操作"""
        key = arguments.get("key")
        if not key:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: key"
            )

        success = await self._adapter.delete(key)

        return ToolResult(
            tool_call_id="",
            success=success,
            result={
                "key": key,
                "deleted": success,
            }
        )

    async def _handle_exists(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理检查存在操作"""
        key = arguments.get("key")
        if not key:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: key"
            )

        exists = await self._adapter.exists(key)

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "key": key,
                "exists": exists,
            }
        )

    async def _handle_clear(self) -> ToolResult:
        """处理清空缓存操作"""
        count = await self._adapter.clear()

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "cleared": True,
                "items_removed": count,
            }
        )

    async def _handle_keys(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理获取键列表操作"""
        pattern = arguments.get("pattern", "*")
        keys = await self._adapter.keys(pattern)

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "pattern": pattern,
                "keys": keys,
                "count": len(keys),
            }
        )

    async def _handle_stats(self) -> ToolResult:
        """处理获取统计信息操作"""
        stats = await self._adapter.get_stats()

        return ToolResult(
            tool_call_id="",
            success=True,
            result=stats
        )

    async def _handle_get_multi(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理批量获取操作"""
        keys = arguments.get("keys", [])
        if not keys:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: keys"
            )

        results = {}
        for key in keys:
            value = await self._adapter.get(key)
            results[key] = value

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "keys": keys,
                "values": results,
                "found_count": sum(1 for v in results.values() if v is not None),
            }
        )

    async def _handle_set_multi(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理批量设置操作"""
        key_value_pairs = arguments.get("key_value_pairs", {})
        if not key_value_pairs:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: key_value_pairs"
            )

        ttl_seconds = arguments.get("ttl_seconds", self.default_ttl)
        tags = arguments.get("tags", [])

        results = {}
        for key, value in key_value_pairs.items():
            success = await self._adapter.set(key, value, ttl_seconds, tags)
            results[key] = success

        success_count = sum(1 for success in results.values() if success)

        return ToolResult(
            tool_call_id="",
            success=success_count > 0,
            result={
                "key_value_pairs": key_value_pairs,
                "results": results,
                "success_count": success_count,
                "total_count": len(key_value_pairs),
            }
        )

    async def _handle_delete_multi(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理批量删除操作"""
        keys = arguments.get("keys", [])
        if not keys:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: keys"
            )

        results = {}
        for key in keys:
            success = await self._adapter.delete(key)
            results[key] = success

        success_count = sum(1 for success in results.values() if success)

        return ToolResult(
            tool_call_id="",
            success=success_count > 0,
            result={
                "keys": keys,
                "results": results,
                "success_count": success_count,
                "total_count": len(keys),
            }
        )

    async def _handle_increment(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理数值增加操作"""
        key = arguments.get("key")
        delta = arguments.get("delta", 1)

        if not key:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: key"
            )

        # 获取当前值
        current_value = await self._adapter.get(key)
        if current_value is None:
            current_value = 0

        try:
            new_value = float(current_value) + float(delta)
            success = await self._adapter.set(key, new_value)

            return ToolResult(
                tool_call_id="",
                success=success,
                result={
                    "key": key,
                    "old_value": current_value,
                    "new_value": new_value,
                    "delta": delta,
                }
            )
        except (ValueError, TypeError) as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"无法对非数值类型执行增加操作: {str(e)}"
            )

    async def _handle_decrement(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理数值减少操作"""
        key = arguments.get("key")
        delta = arguments.get("delta", 1)

        if not key:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: key"
            )

        # 获取当前值
        current_value = await self._adapter.get(key)
        if current_value is None:
            current_value = 0

        try:
            new_value = float(current_value) - float(delta)
            success = await self._adapter.set(key, new_value)

            return ToolResult(
                tool_call_id="",
                success=success,
                result={
                    "key": key,
                    "old_value": current_value,
                    "new_value": new_value,
                    "delta": -delta,
                }
            )
        except (ValueError, TypeError) as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"无法对非数值类型执行减少操作: {str(e)}"
            )

    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """
        验证工具参数

        Args:
            arguments: 工具参数

        Returns:
            bool: 参数是否有效
        """
        action = arguments.get("action")
        if not action:
            return False

        valid_actions = [
            "connect", "disconnect", "get", "set", "delete",
            "exists", "clear", "keys", "stats", "get_multi",
            "set_multi", "delete_multi", "increment", "decrement"
        ]

        if action not in valid_actions:
            return False

        # 验证需要键的操作
        key_required_actions = ["get", "set", "delete", "exists", "increment", "decrement"]
        if action in key_required_actions and not arguments.get("key"):
            return False

        # 验证设置操作的参数
        if action == "set" and arguments.get("value") is None:
            return False

        # 验证批量操作的参数
        if action == "get_multi" and not arguments.get("keys"):
            return False

        if action == "set_multi" and not arguments.get("key_value_pairs"):
            return False

        if action == "delete_multi" and not arguments.get("keys"):
            return False

        return True
