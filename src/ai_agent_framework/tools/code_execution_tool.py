"""
代码执行工具

提供安全的代码执行环境，支持多种编程语言。
"""

import asyncio
import os
import subprocess
import tempfile
from typing import Any, Dict, List, Optional
from datetime import datetime

from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult
from ai_agent_framework.utils.logging_system import logging_system


class CodeExecutionTool(ToolInterface):
    """代码执行工具"""
    
    def __init__(
        self,
        allowed_languages: List[str] = None,
        execution_timeout: int = 30,
        max_output_size: int = 10000,
        sandbox_mode: bool = True,
    ):
        """
        初始化代码执行工具
        
        Args:
            allowed_languages: 允许的编程语言列表
            execution_timeout: 执行超时时间（秒）
            max_output_size: 最大输出大小（字符）
            sandbox_mode: 是否启用沙箱模式
        """
        self.allowed_languages = allowed_languages or ["python", "javascript", "bash"]
        self.execution_timeout = execution_timeout
        self.max_output_size = max_output_size
        self.sandbox_mode = sandbox_mode
        
        self._logger = logging_system.get_logger("code_execution_tool")
        
        # 语言配置
        self._language_configs = {
            "python": {
                "command": ["python", "-c"],
                "file_extension": ".py",
                "safe_imports": ["os", "sys", "json", "math", "datetime", "re"],
                "blocked_imports": ["subprocess", "socket", "urllib", "requests"],
            },
            "javascript": {
                "command": ["node", "-e"],
                "file_extension": ".js",
                "safe_modules": ["fs", "path", "util"],
                "blocked_modules": ["child_process", "cluster", "http", "https"],
            },
            "bash": {
                "command": ["bash", "-c"],
                "file_extension": ".sh",
                "safe_commands": ["echo", "cat", "ls", "pwd", "date"],
                "blocked_commands": ["rm", "sudo", "curl", "wget", "ssh"],
            },
        }
    
    @property
    def name(self) -> str:
        return "code_execution"
    
    @property
    def description(self) -> str:
        return "安全的代码执行工具，支持Python、JavaScript、Bash等语言"
    
    @property
    def parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "language": {
                    "type": "string",
                    "enum": self.allowed_languages,
                    "description": "编程语言"
                },
                "code": {
                    "type": "string",
                    "description": "要执行的代码"
                },
                "input_data": {
                    "type": "string",
                    "description": "输入数据（可选）"
                },
                "working_directory": {
                    "type": "string",
                    "description": "工作目录（可选）"
                },
                "environment": {
                    "type": "object",
                    "description": "环境变量（可选）"
                }
            },
            "required": ["language", "code"]
        }
    
    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """验证工具参数"""
        try:
            language = arguments.get("language")
            code = arguments.get("code")
            
            if not language or not code:
                return False
            
            if language not in self.allowed_languages:
                return False
            
            # 安全检查
            if self.sandbox_mode:
                return self._validate_code_safety(language, code)
            
            return True
        except Exception:
            return False
    
    async def execute(self, arguments: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> ToolResult:
        """执行代码"""
        try:
            language = arguments.get("language")
            code = arguments.get("code")
            input_data = arguments.get("input_data", "")
            working_directory = arguments.get("working_directory")
            environment = arguments.get("environment", {})
            
            # 安全验证
            if not await self.validate_arguments(arguments):
                return ToolResult(
                    success=False,
                    result="代码验证失败或包含不安全内容"
                )
            
            # 执行代码
            result = await self._execute_code(
                language, code, input_data, working_directory, environment
            )
            
            return ToolResult(
                success=result["success"],
                result=result
            )
            
        except Exception as e:
            self._logger.error(f"代码执行失败: {e}")
            return ToolResult(
                success=False,
                result=f"代码执行失败: {str(e)}"
            )
    
    def _validate_code_safety(self, language: str, code: str) -> bool:
        """验证代码安全性"""
        if language not in self._language_configs:
            return False
        
        config = self._language_configs[language]
        
        if language == "python":
            # 检查危险的Python导入
            blocked_imports = config.get("blocked_imports", [])
            for blocked in blocked_imports:
                if f"import {blocked}" in code or f"from {blocked}" in code:
                    self._logger.warning(f"检测到危险导入: {blocked}")
                    return False
            
            # 检查危险函数调用
            dangerous_functions = ["exec", "eval", "compile", "__import__"]
            for func in dangerous_functions:
                if func in code:
                    self._logger.warning(f"检测到危险函数: {func}")
                    return False
        
        elif language == "bash":
            # 检查危险的Bash命令
            blocked_commands = config.get("blocked_commands", [])
            for blocked in blocked_commands:
                if blocked in code:
                    self._logger.warning(f"检测到危险命令: {blocked}")
                    return False
        
        return True
    
    async def _execute_code(
        self,
        language: str,
        code: str,
        input_data: str,
        working_directory: Optional[str],
        environment: Dict[str, str],
    ) -> Dict[str, Any]:
        """执行代码"""
        config = self._language_configs[language]
        command = config["command"].copy()
        
        # 准备执行环境
        env = os.environ.copy()
        env.update(environment)
        
        # 设置工作目录
        cwd = working_directory or tempfile.gettempdir()
        
        try:
            start_time = datetime.now()
            
            if language in ["python", "javascript"]:
                # 直接执行代码
                command.append(code)
                
                process = await asyncio.create_subprocess_exec(
                    *command,
                    stdin=asyncio.subprocess.PIPE,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=cwd,
                    env=env
                )
                
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(input_data.encode() if input_data else None),
                    timeout=self.execution_timeout
                )
                
            elif language == "bash":
                # Bash脚本执行
                process = await asyncio.create_subprocess_shell(
                    code,
                    stdin=asyncio.subprocess.PIPE,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=cwd,
                    env=env
                )
                
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(input_data.encode() if input_data else None),
                    timeout=self.execution_timeout
                )
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # 处理输出
            stdout_text = stdout.decode('utf-8', errors='replace')
            stderr_text = stderr.decode('utf-8', errors='replace')
            
            # 限制输出大小
            if len(stdout_text) > self.max_output_size:
                stdout_text = stdout_text[:self.max_output_size] + "\n... (输出被截断)"
            
            if len(stderr_text) > self.max_output_size:
                stderr_text = stderr_text[:self.max_output_size] + "\n... (错误输出被截断)"
            
            return {
                "success": process.returncode == 0,
                "return_code": process.returncode,
                "stdout": stdout_text,
                "stderr": stderr_text,
                "execution_time": execution_time,
                "language": language,
                "working_directory": cwd,
            }
            
        except asyncio.TimeoutError:
            return {
                "success": False,
                "error": f"代码执行超时（>{self.execution_timeout}秒）",
                "execution_time": self.execution_timeout,
                "language": language,
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"代码执行失败: {str(e)}",
                "language": language,
            }
    
    def get_supported_languages(self) -> List[str]:
        """获取支持的编程语言列表"""
        return self.allowed_languages.copy()
    
    def get_language_info(self, language: str) -> Optional[Dict[str, Any]]:
        """获取特定语言的信息"""
        if language not in self._language_configs:
            return None
        
        config = self._language_configs[language].copy()
        return {
            "language": language,
            "command": config.get("command"),
            "file_extension": config.get("file_extension"),
            "safe_imports": config.get("safe_imports", []),
            "blocked_imports": config.get("blocked_imports", []),
            "safe_modules": config.get("safe_modules", []),
            "blocked_modules": config.get("blocked_modules", []),
            "safe_commands": config.get("safe_commands", []),
            "blocked_commands": config.get("blocked_commands", []),
        }
