"""
Git版本控制工具

提供Git版本控制操作的工具实现。
"""

import os
import subprocess
from typing import Any, Dict, List, Optional

from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult
from ai_agent_framework.utils.logging_system import logging_system


class GitTool(ToolInterface):
    """Git版本控制工具"""

    def __init__(self, repo_path: Optional[str] = None):
        """
        初始化Git工具

        Args:
            repo_path: Git仓库路径，None表示当前目录
        """
        self.repo_path = repo_path or os.getcwd()
        self._logger = logging_system.get_logger("git_tool")

    @property
    def name(self) -> str:
        return "git"

    @property
    def description(self) -> str:
        return "Git版本控制工具，支持分支管理、提交历史、合并冲突等操作"

    @property
    def parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "enum": [
                        "status",
                        "add",
                        "commit",
                        "push",
                        "pull",
                        "branch",
                        "checkout",
                        "merge",
                        "log",
                        "diff",
                        "clone",
                        "init",
                        "remote",
                    ],
                    "description": "Git操作类型",
                },
                "files": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "文件列表（用于add操作）",
                },
                "message": {
                    "type": "string",
                    "description": "提交消息（用于commit操作）",
                },
                "branch": {"type": "string", "description": "分支名称"},
                "remote": {
                    "type": "string",
                    "description": "远程仓库名称",
                    "default": "origin",
                },
                "url": {"type": "string", "description": "仓库URL（用于clone操作）"},
                "limit": {
                    "type": "integer",
                    "description": "限制结果数量（用于log操作）",
                    "default": 10,
                },
            },
            "required": ["action"],
        }

    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """验证工具参数"""
        try:
            action = arguments.get("action")
            if not action:
                return False

            valid_actions = [
                "status",
                "add",
                "commit",
                "push",
                "pull",
                "branch",
                "checkout",
                "merge",
                "log",
                "diff",
                "clone",
                "init",
                "remote",
            ]

            return action in valid_actions
        except Exception:
            return False

    async def execute(
        self, arguments: Dict[str, Any], context: Optional[Dict[str, Any]] = None
    ) -> ToolResult:
        """执行Git操作"""
        try:
            action = arguments.get("action")

            if action == "status":
                return await self._get_status()
            elif action == "add":
                files = arguments.get("files", [])
                return await self._add_files(files)
            elif action == "commit":
                message = arguments.get("message", "")
                return await self._commit_changes(message)
            elif action == "push":
                remote = arguments.get("remote", "origin")
                branch = arguments.get("branch")
                return await self._push_changes(remote, branch)
            elif action == "pull":
                remote = arguments.get("remote", "origin")
                branch = arguments.get("branch")
                return await self._pull_changes(remote, branch)
            elif action == "branch":
                branch = arguments.get("branch")
                return await self._manage_branch(branch)
            elif action == "checkout":
                branch = arguments.get("branch", "")
                return await self._checkout_branch(branch)
            elif action == "merge":
                branch = arguments.get("branch", "")
                return await self._merge_branch(branch)
            elif action == "log":
                limit = arguments.get("limit", 10)
                return await self._get_log(limit)
            elif action == "diff":
                return await self._get_diff()
            elif action == "clone":
                url = arguments.get("url", "")
                return await self._clone_repo(url)
            elif action == "init":
                return await self._init_repo()
            elif action == "remote":
                return await self._get_remotes()
            else:
                return ToolResult(success=False, result=f"不支持的Git操作: {action}")

        except Exception as e:
            self._logger.error(f"Git操作失败: {e}")
            return ToolResult(success=False, result=f"Git操作失败: {str(e)}")

    def _run_git_command(self, args: List[str]) -> subprocess.CompletedProcess:
        """运行Git命令"""
        cmd = ["git"] + args
        return subprocess.run(
            cmd, cwd=self.repo_path, capture_output=True, text=True, encoding="utf-8"
        )

    async def _get_status(self) -> ToolResult:
        """获取Git状态"""
        result = self._run_git_command(["status", "--porcelain"])

        if result.returncode != 0:
            return ToolResult(success=False, result=f"获取状态失败: {result.stderr}")

        # 解析状态输出
        status_lines = (
            result.stdout.strip().split("\n") if result.stdout.strip() else []
        )

        modified = []
        added = []
        deleted = []
        untracked = []

        for line in status_lines:
            if len(line) >= 3:
                status_code = line[:2]
                filename = line[3:]

                if status_code.startswith("M"):
                    modified.append(filename)
                elif status_code.startswith("A"):
                    added.append(filename)
                elif status_code.startswith("D"):
                    deleted.append(filename)
                elif status_code.startswith("??"):
                    untracked.append(filename)

        return ToolResult(
            success=True,
            result={
                "modified": modified,
                "added": added,
                "deleted": deleted,
                "untracked": untracked,
                "clean": len(status_lines) == 0,
            },
        )

    async def _add_files(self, files: List[str]) -> ToolResult:
        """添加文件到暂存区"""
        if not files:
            files = ["."]  # 添加所有文件

        result = self._run_git_command(["add"] + files)

        if result.returncode != 0:
            return ToolResult(success=False, result=f"添加文件失败: {result.stderr}")

        return ToolResult(success=True, result=f"成功添加文件: {', '.join(files)}")

    async def _commit_changes(self, message: str) -> ToolResult:
        """提交变更"""
        if not message:
            return ToolResult(success=False, result="提交消息不能为空")

        result = self._run_git_command(["commit", "-m", message])

        if result.returncode != 0:
            return ToolResult(success=False, result=f"提交失败: {result.stderr}")

        return ToolResult(success=True, result=f"提交成功: {message}")

    async def _push_changes(self, remote: str, branch: Optional[str]) -> ToolResult:
        """推送变更"""
        args = ["push", remote]
        if branch:
            args.append(branch)

        result = self._run_git_command(args)

        if result.returncode != 0:
            return ToolResult(success=False, result=f"推送失败: {result.stderr}")

        return ToolResult(
            success=True,
            result=f"推送成功到 {remote}" + (f"/{branch}" if branch else ""),
        )

    async def _pull_changes(self, remote: str, branch: Optional[str]) -> ToolResult:
        """拉取变更"""
        args = ["pull", remote]
        if branch:
            args.append(branch)

        result = self._run_git_command(args)

        if result.returncode != 0:
            return ToolResult(success=False, result=f"拉取失败: {result.stderr}")

        return ToolResult(
            success=True,
            result=f"拉取成功从 {remote}" + (f"/{branch}" if branch else ""),
        )

    async def _manage_branch(self, branch: Optional[str]) -> ToolResult:
        """管理分支"""
        if branch:
            # 创建新分支
            result = self._run_git_command(["branch", branch])
            if result.returncode != 0:
                return ToolResult(
                    success=False, result=f"创建分支失败: {result.stderr}"
                )
            return ToolResult(success=True, result=f"创建分支成功: {branch}")
        else:
            # 列出所有分支
            result = self._run_git_command(["branch", "-a"])
            if result.returncode != 0:
                return ToolResult(
                    success=False, result=f"获取分支列表失败: {result.stderr}"
                )

            branches = []
            current_branch = None

            for line in result.stdout.strip().split("\n"):
                line = line.strip()
                if line.startswith("* "):
                    current_branch = line[2:]
                    branches.append({"name": current_branch, "current": True})
                elif line:
                    branches.append({"name": line, "current": False})

            return ToolResult(
                success=True,
                result={"branches": branches, "current_branch": current_branch},
            )

    async def _checkout_branch(self, branch: str) -> ToolResult:
        """切换分支"""
        if not branch:
            return ToolResult(success=False, result="分支名称不能为空")

        result = self._run_git_command(["checkout", branch])

        if result.returncode != 0:
            return ToolResult(success=False, result=f"切换分支失败: {result.stderr}")

        return ToolResult(success=True, result=f"切换到分支: {branch}")

    async def _merge_branch(self, branch: str) -> ToolResult:
        """合并分支"""
        if not branch:
            return ToolResult(success=False, result="分支名称不能为空")

        result = self._run_git_command(["merge", branch])

        if result.returncode != 0:
            return ToolResult(success=False, result=f"合并分支失败: {result.stderr}")

        return ToolResult(success=True, result=f"合并分支成功: {branch}")

    async def _get_log(self, limit: int) -> ToolResult:
        """获取提交历史"""
        result = self._run_git_command(
            [
                "log",
                f"--max-count={limit}",
                "--pretty=format:%H|%an|%ae|%ad|%s",
                "--date=iso",
            ]
        )

        if result.returncode != 0:
            return ToolResult(
                success=False, result=f"获取提交历史失败: {result.stderr}"
            )

        commits = []
        for line in result.stdout.strip().split("\n"):
            if line:
                parts = line.split("|", 4)
                if len(parts) == 5:
                    commits.append(
                        {
                            "hash": parts[0],
                            "author": parts[1],
                            "email": parts[2],
                            "date": parts[3],
                            "message": parts[4],
                        }
                    )

        return ToolResult(success=True, result={"commits": commits})

    async def _get_diff(self) -> ToolResult:
        """获取差异"""
        result = self._run_git_command(["diff"])

        if result.returncode != 0:
            return ToolResult(success=False, result=f"获取差异失败: {result.stderr}")

        return ToolResult(success=True, result={"diff": result.stdout})

    async def _clone_repo(self, url: str) -> ToolResult:
        """克隆仓库"""
        if not url:
            return ToolResult(success=False, result="仓库URL不能为空")

        result = self._run_git_command(["clone", url])

        if result.returncode != 0:
            return ToolResult(success=False, result=f"克隆仓库失败: {result.stderr}")

        return ToolResult(success=True, result=f"克隆仓库成功: {url}")

    async def _init_repo(self) -> ToolResult:
        """初始化仓库"""
        result = self._run_git_command(["init"])

        if result.returncode != 0:
            return ToolResult(success=False, result=f"初始化仓库失败: {result.stderr}")

        return ToolResult(success=True, result="初始化Git仓库成功")

    async def _get_remotes(self) -> ToolResult:
        """获取远程仓库"""
        result = self._run_git_command(["remote", "-v"])

        if result.returncode != 0:
            return ToolResult(
                success=False, result=f"获取远程仓库失败: {result.stderr}"
            )

        remotes = {}
        for line in result.stdout.strip().split("\n"):
            if line:
                parts = line.split()
                if len(parts) >= 2:
                    name = parts[0]
                    url = parts[1]
                    if name not in remotes:
                        remotes[name] = url

        return ToolResult(success=True, result={"remotes": remotes})
