"""
AI Agent Framework 服务器

提供HTTP API接口用于Agent交互和管理。
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Any, Dict, Optional

import uvicorn
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from ai_agent_framework.agents.agent import Agent
from ai_agent_framework.providers.openai_provider import OpenAIProvider
from ai_agent_framework.utils.logging_system import logging_system


# 请求/响应模型
class ChatRequest(BaseModel):
    """聊天请求模型"""

    message: str
    agent_id: Optional[str] = "default"
    session_id: Optional[str] = None


class ChatResponse(BaseModel):
    """聊天响应模型"""

    response: str
    agent_id: str
    session_id: str
    timestamp: datetime


class HealthResponse(BaseModel):
    """健康检查响应模型"""

    status: str
    timestamp: datetime
    version: str


# 创建FastAPI应用
app = FastAPI(
    title="AI Agent Framework API",
    description="AI智能体框架的HTTP API接口",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
agents: Dict[str, Agent] = {}
logger = logging_system.get_logger("server")


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("启动AI Agent Framework服务器...")

    # 创建默认Agent
    try:
        # 这里可以从环境变量或配置文件读取API密钥
        provider = OpenAIProvider(
            api_key="your-api-key-here",  # 实际部署时应从环境变量读取
            model="gpt-3.5-turbo",
        )

        default_agent = Agent(
            name="默认助手", description="默认的AI助手", provider=provider
        )

        await default_agent.start()
        agents["default"] = default_agent

        logger.info("默认Agent创建成功")

    except Exception as e:
        logger.error(f"创建默认Agent失败: {e}")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("关闭AI Agent Framework服务器...")

    # 停止所有Agent
    for agent_id, agent in agents.items():
        try:
            await agent.stop()
            logger.info(f"Agent {agent_id} 已停止")
        except Exception as e:
            logger.error(f"停止Agent {agent_id} 失败: {e}")


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查端点"""
    return HealthResponse(status="healthy", timestamp=datetime.now(), version="1.0.0")


@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """聊天端点"""
    try:
        agent_id = request.agent_id or "default"

        # 检查Agent是否存在
        if agent_id not in agents:
            raise HTTPException(status_code=404, detail=f"Agent {agent_id} 不存在")

        agent = agents[agent_id]

        # 处理消息
        response = await agent.process_message(request.message)

        return ChatResponse(
            response=response.content,
            agent_id=agent_id,
            session_id=request.session_id or "default",
            timestamp=datetime.now(),
        )

    except Exception as e:
        logger.error(f"处理聊天请求失败: {e}")
        raise HTTPException(status_code=500, detail=f"处理请求失败: {str(e)}")


@app.get("/agents")
async def list_agents():
    """列出所有Agent"""
    agent_list = []

    for agent_id, agent in agents.items():
        agent_list.append(
            {
                "id": agent_id,
                "name": agent.name,
                "description": agent.description,
                "status": "running" if agent.is_running else "stopped",
            }
        )

    return {"agents": agent_list}


@app.get("/agents/{agent_id}")
async def get_agent(agent_id: str):
    """获取特定Agent信息"""
    if agent_id not in agents:
        raise HTTPException(status_code=404, detail=f"Agent {agent_id} 不存在")

    agent = agents[agent_id]

    return {
        "id": agent_id,
        "name": agent.name,
        "description": agent.description,
        "status": "running" if agent.is_running else "stopped",
        "provider": agent.provider.__class__.__name__,
        "tools": [tool.name for tool in agent.tools] if agent.tools else [],
    }


@app.get("/metrics")
async def get_metrics():
    """获取系统指标"""
    # 这里可以集成Prometheus指标
    return {
        "total_agents": len(agents),
        "running_agents": sum(1 for agent in agents.values() if agent.is_running),
        "timestamp": datetime.now(),
    }


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {exc}", exc_info=True)

    return JSONResponse(
        status_code=500,
        content={
            "error": "内部服务器错误",
            "detail": str(exc),
            "timestamp": datetime.now().isoformat(),
        },
    )


def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    # 启动服务器
    uvicorn.run(
        "ai_agent_framework.server:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info",
    )


if __name__ == "__main__":
    main()
