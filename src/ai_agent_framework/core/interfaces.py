"""
AI Agent Framework 核心接口定义模块

定义了框架的核心抽象接口，遵循SOLID原则中的依赖倒置原则。
所有具体实现都应该依赖于这些抽象接口，而不是具体实现。
"""

from abc import ABC, abstractmethod
from typing import Any, AsyncIterator, Dict, List, Optional, Union

from ai_agent_framework.core.messages import (
    Message,
    ModelResponse,
    ToolCall,
    ToolResult,
)


class ModelInterface(ABC):
    """
    模型接口抽象基类

    定义了所有AI模型适配器必须实现的接口。
    支持同步和异步调用，流式和非流式响应。
    """

    @abstractmethod
    async def generate(
        self,
        messages: List[Message],
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs,
    ) -> ModelResponse:
        """
        生成响应

        Args:
            messages: 消息历史列表
            tools: 可用工具列表
            **kwargs: 模型特定参数

        Returns:
            ModelResponse: 模型响应

        Raises:
            ModelError: 模型调用失败时抛出
        """
        pass

    @abstractmethod
    async def generate_stream(
        self,
        messages: List[Message],
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs,
    ) -> AsyncIterator[ModelResponse]:
        """
        流式生成响应

        Args:
            messages: 消息历史列表
            tools: 可用工具列表
            **kwargs: 模型特定参数

        Yields:
            ModelResponse: 流式响应片段

        Raises:
            ModelError: 模型调用失败时抛出
        """
        pass

    @abstractmethod
    async def count_tokens(
        self,
        messages: List[Message],
        tools: Optional[List[Dict[str, Any]]] = None,
    ) -> int:
        """
        计算token数量

        Args:
            messages: 消息列表
            tools: 工具列表

        Returns:
            int: token数量
        """
        pass

    @property
    @abstractmethod
    def model_name(self) -> str:
        """获取模型名称"""
        pass

    @property
    @abstractmethod
    def supports_tools(self) -> bool:
        """是否支持工具调用"""
        pass

    @property
    @abstractmethod
    def supports_streaming(self) -> bool:
        """是否支持流式响应"""
        pass

    @abstractmethod
    async def validate_config(self) -> bool:
        """
        验证模型配置

        Returns:
            bool: 配置是否有效
        """
        pass


class ToolInterface(ABC):
    """
    工具接口抽象基类

    定义了所有工具必须实现的接口。
    工具是Agent可以调用的外部功能，如搜索、计算、文件操作等。
    """

    @property
    @abstractmethod
    def name(self) -> str:
        """工具名称"""
        pass

    @property
    @abstractmethod
    def description(self) -> str:
        """工具描述"""
        pass

    @property
    @abstractmethod
    def parameters_schema(self) -> Dict[str, Any]:
        """
        工具参数JSON Schema

        Returns:
            Dict[str, Any]: JSON Schema格式的参数定义
        """
        pass

    @abstractmethod
    async def execute(
        self,
        arguments: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None,
    ) -> ToolResult:
        """
        执行工具

        Args:
            arguments: 工具参数
            context: 执行上下文

        Returns:
            ToolResult: 执行结果

        Raises:
            ToolError: 工具执行失败时抛出
        """
        pass

    @abstractmethod
    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """
        验证工具参数

        Args:
            arguments: 工具参数

        Returns:
            bool: 参数是否有效
        """
        pass

    @property
    def requires_confirmation(self) -> bool:
        """是否需要用户确认才能执行"""
        return False

    @property
    def timeout_seconds(self) -> Optional[float]:
        """执行超时时间（秒）"""
        return None

    def to_schema(self) -> Dict[str, Any]:
        """
        转换为工具schema格式

        Returns:
            Dict[str, Any]: 工具schema
        """
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": self.parameters_schema,
            },
        }


class MemoryInterface(ABC):
    """
    记忆接口抽象基类

    定义了记忆管理系统必须实现的接口。
    支持多层次记忆：短期记忆、工作记忆、长期记忆。
    """

    @abstractmethod
    async def store(
        self,
        key: str,
        value: Any,
        memory_type: str = "working",
        ttl: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        存储记忆

        Args:
            key: 记忆键
            value: 记忆值
            memory_type: 记忆类型 (short_term, working, long_term)
            ttl: 生存时间（秒）
            metadata: 元数据

        Returns:
            bool: 是否存储成功
        """
        pass

    @abstractmethod
    async def retrieve(
        self,
        key: str,
        memory_type: str = "working",
    ) -> Optional[Any]:
        """
        检索记忆

        Args:
            key: 记忆键
            memory_type: 记忆类型

        Returns:
            Optional[Any]: 记忆值，不存在时返回None
        """
        pass

    @abstractmethod
    async def search(
        self,
        query: str,
        memory_type: str = "working",
        limit: int = 10,
        similarity_threshold: float = 0.7,
    ) -> List[Dict[str, Any]]:
        """
        搜索相似记忆

        Args:
            query: 搜索查询
            memory_type: 记忆类型
            limit: 返回数量限制
            similarity_threshold: 相似度阈值

        Returns:
            List[Dict[str, Any]]: 搜索结果列表
        """
        pass

    @abstractmethod
    async def delete(
        self,
        key: str,
        memory_type: str = "working",
    ) -> bool:
        """
        删除记忆

        Args:
            key: 记忆键
            memory_type: 记忆类型

        Returns:
            bool: 是否删除成功
        """
        pass

    @abstractmethod
    async def clear(
        self,
        memory_type: Optional[str] = None,
    ) -> bool:
        """
        清空记忆

        Args:
            memory_type: 记忆类型，None表示清空所有类型

        Returns:
            bool: 是否清空成功
        """
        pass

    @abstractmethod
    async def get_stats(self) -> Dict[str, Any]:
        """
        获取记忆统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        pass
