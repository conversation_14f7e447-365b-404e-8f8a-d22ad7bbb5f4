"""
AI Agent Framework 连接池管理

提供HTTP连接池管理功能，优化网络请求性能。
支持连接复用、超时控制、重试机制等。
"""

import asyncio
import time
from contextlib import asynccontextmanager
from typing import Any, Dict, Optional, Set

import httpx

from ai_agent_framework.utils.logging_system import logging_system


class ConnectionPool:
    """
    HTTP连接池管理器

    管理HTTP连接的创建、复用和清理，提供高性能的网络请求能力。
    """

    def __init__(
        self,
        max_connections: int = 100,
        max_keepalive_connections: int = 20,
        keepalive_expiry: float = 5.0,
        timeout: float = 30.0,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        """
        初始化连接池

        Args:
            max_connections: 最大连接数
            max_keepalive_connections: 最大保持连接数
            keepalive_expiry: 连接保持时间（秒）
            timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
        """
        self.max_connections = max_connections
        self.max_keepalive_connections = max_keepalive_connections
        self.keepalive_expiry = keepalive_expiry
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay

        # 连接池实例
        self._clients: Dict[str, httpx.AsyncClient] = {}
        self._client_usage: Dict[str, int] = {}
        self._client_last_used: Dict[str, float] = {}

        # 清理任务
        self._cleanup_task: Optional[asyncio.Task] = None
        self._cleanup_interval = 60.0  # 清理间隔（秒）

        self._logger = logging_system.get_logger("connection_pool")

        # 启动清理任务
        self._start_cleanup_task()

        self._logger.info("连接池已初始化")

    def _start_cleanup_task(self):
        """启动连接清理任务"""

        async def cleanup_loop():
            while True:
                try:
                    await asyncio.sleep(self._cleanup_interval)
                    await self._cleanup_idle_connections()
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    self._logger.error(f"连接清理任务出错: {str(e)}")

        self._cleanup_task = asyncio.create_task(cleanup_loop())

    async def _cleanup_idle_connections(self):
        """清理空闲连接"""
        current_time = time.time()
        clients_to_remove = []

        for client_key, last_used in self._client_last_used.items():
            if current_time - last_used > self.keepalive_expiry:
                clients_to_remove.append(client_key)

        for client_key in clients_to_remove:
            await self._remove_client(client_key)
            self._logger.debug(f"清理空闲连接: {client_key}")

    async def _remove_client(self, client_key: str):
        """移除客户端连接"""
        if client_key in self._clients:
            client = self._clients[client_key]
            await client.aclose()

            del self._clients[client_key]
            del self._client_usage[client_key]
            del self._client_last_used[client_key]

    def _get_client_key(
        self, base_url: str, headers: Optional[Dict[str, str]] = None
    ) -> str:
        """生成客户端键"""
        key_parts = [base_url]

        if headers:
            # 将headers转换为排序的字符串
            sorted_headers = sorted(headers.items())
            headers_str = "&".join([f"{k}={v}" for k, v in sorted_headers])
            key_parts.append(headers_str)

        return "|".join(key_parts)

    def _get_or_create_client(
        self, base_url: str, headers: Optional[Dict[str, str]] = None
    ) -> httpx.AsyncClient:
        """获取或创建HTTP客户端"""
        client_key = self._get_client_key(base_url, headers)

        # 更新使用时间
        self._client_last_used[client_key] = time.time()

        if client_key in self._clients:
            # 增加使用计数
            self._client_usage[client_key] += 1
            return self._clients[client_key]

        # 检查连接数限制
        if len(self._clients) >= self.max_connections:
            # 移除最少使用的连接
            least_used_key = min(self._client_usage.items(), key=lambda x: x[1])[0]
            asyncio.create_task(self._remove_client(least_used_key))

        # 创建新客户端
        limits = httpx.Limits(
            max_connections=self.max_connections,
            max_keepalive_connections=self.max_keepalive_connections,
            keepalive_expiry=self.keepalive_expiry,
        )

        timeout = httpx.Timeout(self.timeout)

        client = httpx.AsyncClient(
            base_url=base_url,
            headers=headers,
            limits=limits,
            timeout=timeout,
            follow_redirects=True,
        )

        self._clients[client_key] = client
        self._client_usage[client_key] = 1

        self._logger.debug(f"创建新连接: {client_key}")
        return client

    @asynccontextmanager
    async def get_client(self, base_url: str, headers: Optional[Dict[str, str]] = None):
        """
        获取HTTP客户端（上下文管理器）

        Args:
            base_url: 基础URL
            headers: 请求头

        Yields:
            httpx.AsyncClient: HTTP客户端
        """
        client = self._get_or_create_client(base_url, headers)
        try:
            yield client
        finally:
            # 客户端会在连接池中复用，这里不需要关闭
            pass

    async def request(
        self, method: str, url: str, headers: Optional[Dict[str, str]] = None, **kwargs
    ) -> httpx.Response:
        """
        发送HTTP请求（带重试）

        Args:
            method: HTTP方法
            url: 请求URL
            headers: 请求头
            **kwargs: 其他参数

        Returns:
            httpx.Response: 响应对象
        """
        # 提取base_url
        from urllib.parse import urlparse

        parsed = urlparse(url)
        base_url = f"{parsed.scheme}://{parsed.netloc}"

        last_exception = None

        for attempt in range(self.max_retries + 1):
            try:
                async with self.get_client(base_url, headers) as client:
                    response = await client.request(method, url, **kwargs)

                    # 检查响应状态
                    if response.status_code >= 500 and attempt < self.max_retries:
                        # 服务器错误，重试
                        await asyncio.sleep(self.retry_delay * (2**attempt))
                        continue

                    return response

            except (httpx.ConnectError, httpx.TimeoutException) as e:
                last_exception = e
                if attempt < self.max_retries:
                    self._logger.warning(
                        f"请求失败，重试 {attempt + 1}/{self.max_retries}: {str(e)}"
                    )
                    await asyncio.sleep(self.retry_delay * (2**attempt))
                    continue
                else:
                    break
            except Exception as e:
                # 其他异常不重试
                raise e

        # 所有重试都失败
        raise last_exception or Exception("请求失败")

    async def get(self, url: str, **kwargs) -> httpx.Response:
        """GET请求"""
        return await self.request("GET", url, **kwargs)

    async def post(self, url: str, **kwargs) -> httpx.Response:
        """POST请求"""
        return await self.request("POST", url, **kwargs)

    async def put(self, url: str, **kwargs) -> httpx.Response:
        """PUT请求"""
        return await self.request("PUT", url, **kwargs)

    async def delete(self, url: str, **kwargs) -> httpx.Response:
        """DELETE请求"""
        return await self.request("DELETE", url, **kwargs)

    def get_stats(self) -> Dict[str, Any]:
        """
        获取连接池统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "total_clients": len(self._clients),
            "max_connections": self.max_connections,
            "max_keepalive_connections": self.max_keepalive_connections,
            "keepalive_expiry": self.keepalive_expiry,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "client_usage": dict(self._client_usage),
            "cleanup_running": self._cleanup_task is not None
            and not self._cleanup_task.done(),
        }

    async def close(self):
        """关闭连接池"""
        # 停止清理任务
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass

        # 关闭所有客户端
        for client in self._clients.values():
            await client.aclose()

        self._clients.clear()
        self._client_usage.clear()
        self._client_last_used.clear()

        self._logger.info("连接池已关闭")

    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()


# 全局连接池实例
_global_connection_pool: Optional[ConnectionPool] = None


def get_global_connection_pool() -> ConnectionPool:
    """
    获取全局连接池实例

    Returns:
        ConnectionPool: 连接池实例
    """
    global _global_connection_pool

    if _global_connection_pool is None:
        _global_connection_pool = ConnectionPool()

    return _global_connection_pool


async def close_global_connection_pool():
    """关闭全局连接池"""
    global _global_connection_pool

    if _global_connection_pool is not None:
        await _global_connection_pool.close()
        _global_connection_pool = None
