"""
AI Agent Framework 日志系统

提供统一的日志管理功能，支持结构化日志、多种输出格式和日志级别控制。
集成了性能监控和错误跟踪功能。
"""

import json
import logging
import logging.handlers
import sys
import threading
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional, Union

import structlog

from ai_agent_framework.core.config import LoggingConfig
from ai_agent_framework.exceptions import ConfigurationError


class LoggingSystem:
    """
    日志系统

    提供统一的日志管理功能，支持：
    - 结构化日志记录
    - 多种输出格式（文本、JSON）
    - 文件轮转和大小限制
    - 性能监控集成
    - 上下文信息自动注入
    """

    _instance: Optional["LoggingSystem"] = None
    _lock = threading.Lock()

    def __new__(cls) -> "LoggingSystem":
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """初始化日志系统"""
        if hasattr(self, "_initialized"):
            return

        self._config: Optional[LoggingConfig] = None
        self._loggers: Dict[str, logging.Logger] = {}
        self._initialized = False
        self._context: Dict[str, Any] = {}

    def setup(self, config: LoggingConfig) -> None:
        """
        设置日志系统

        Args:
            config: 日志配置

        Raises:
            ConfigurationError: 配置无效时抛出
        """
        try:
            self._config = config

            # 配置structlog
            if config.structured:
                self._setup_structlog()

            # 配置标准logging
            self._setup_standard_logging()

            self._initialized = True

        except Exception as e:
            raise ConfigurationError(
                f"日志系统设置失败: {str(e)}", error_code="LOGGING_SETUP_ERROR", cause=e
            )

    def _setup_structlog(self) -> None:
        """设置structlog"""
        processors = [
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
        ]

        if self._config.json_format:
            processors.append(structlog.processors.JSONRenderer())
        else:
            processors.append(structlog.dev.ConsoleRenderer())

        structlog.configure(
            processors=processors,
            wrapper_class=structlog.stdlib.BoundLogger,
            logger_factory=structlog.stdlib.LoggerFactory(),
            cache_logger_on_first_use=True,
        )

    def _setup_standard_logging(self) -> None:
        """设置标准logging"""
        # 获取根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, self._config.level.upper()))

        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 创建格式化器
        if self._config.json_format:
            formatter = JsonFormatter()
        else:
            formatter = logging.Formatter(self._config.format)

        # 添加控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)

        # 添加文件处理器（如果配置了文件路径）
        if self._config.file_path:
            file_path = Path(self._config.file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)

            file_handler = logging.handlers.RotatingFileHandler(
                filename=file_path,
                maxBytes=self._config.max_file_size,
                backupCount=self._config.backup_count,
                encoding="utf-8",
            )
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)

    def get_logger(self, name: str) -> Union[logging.Logger, structlog.BoundLogger]:
        """
        获取日志记录器

        Args:
            name: 日志记录器名称

        Returns:
            Union[logging.Logger, structlog.BoundLogger]: 日志记录器
        """
        if not self._initialized:
            # 如果未初始化，返回标准logger
            return logging.getLogger(name)

        if name not in self._loggers:
            if self._config.structured:
                logger = structlog.get_logger(name)
                # 绑定上下文信息
                if self._context:
                    logger = logger.bind(**self._context)
            else:
                logger = logging.getLogger(name)

            self._loggers[name] = logger

        return self._loggers[name]

    def set_context(self, **kwargs) -> None:
        """
        设置全局上下文信息

        Args:
            **kwargs: 上下文键值对
        """
        self._context.update(kwargs)

        # 更新现有的structlog记录器
        if self._config and self._config.structured:
            for name, logger in self._loggers.items():
                if hasattr(logger, "bind"):
                    self._loggers[name] = logger.bind(**kwargs)

    def clear_context(self) -> None:
        """清除全局上下文信息"""
        self._context.clear()

    def log_performance(
        self, operation: str, duration: float, success: bool = True, **kwargs
    ) -> None:
        """
        记录性能日志

        Args:
            operation: 操作名称
            duration: 执行时间（秒）
            success: 是否成功
            **kwargs: 额外信息
        """
        logger = self.get_logger("performance")

        log_data = {
            "operation": operation,
            "duration": duration,
            "success": success,
            "timestamp": datetime.now().isoformat(),
            **kwargs,
        }

        if hasattr(logger, "info"):
            if self._config and self._config.structured:
                logger.info("性能监控", **log_data)
            else:
                logger.info(
                    f"性能监控 - {operation}: {duration:.3f}s (成功: {success})"
                )

    def log_error(
        self, error: Exception, context: Optional[Dict[str, Any]] = None, **kwargs
    ) -> None:
        """
        记录错误日志

        Args:
            error: 异常对象
            context: 错误上下文
            **kwargs: 额外信息
        """
        logger = self.get_logger("error")

        error_data = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "timestamp": datetime.now().isoformat(),
            **(context or {}),
            **kwargs,
        }

        if hasattr(logger, "error"):
            if self._config and self._config.structured:
                logger.error("错误记录", exc_info=error, **error_data)
            else:
                logger.error(f"错误: {error}", exc_info=error)

    def log_audit(
        self,
        action: str,
        user: Optional[str] = None,
        resource: Optional[str] = None,
        **kwargs,
    ) -> None:
        """
        记录审计日志

        Args:
            action: 操作动作
            user: 用户标识
            resource: 资源标识
            **kwargs: 额外信息
        """
        logger = self.get_logger("audit")

        audit_data = {
            "action": action,
            "user": user,
            "resource": resource,
            "timestamp": datetime.now().isoformat(),
            **kwargs,
        }

        if hasattr(logger, "info"):
            if self._config and self._config.structured:
                logger.info("审计记录", **audit_data)
            else:
                logger.info(f"审计 - {action} by {user} on {resource}")

    def get_log_level(self) -> str:
        """
        获取当前日志级别

        Returns:
            str: 日志级别
        """
        if self._config:
            return self._config.level
        return "INFO"

    def set_log_level(self, level: str) -> None:
        """
        设置日志级别

        Args:
            level: 日志级别
        """
        if self._config:
            self._config.level = level.upper()

            # 更新根日志记录器级别
            root_logger = logging.getLogger()
            root_logger.setLevel(getattr(logging, level.upper()))


class JsonFormatter(logging.Formatter):
    """JSON格式化器"""

    def format(self, record: logging.LogRecord) -> str:
        """
        格式化日志记录为JSON

        Args:
            record: 日志记录

        Returns:
            str: JSON格式的日志
        """
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }

        # 添加异常信息
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)

        # 添加额外字段
        for key, value in record.__dict__.items():
            if key not in [
                "name",
                "msg",
                "args",
                "levelname",
                "levelno",
                "pathname",
                "filename",
                "module",
                "lineno",
                "funcName",
                "created",
                "msecs",
                "relativeCreated",
                "thread",
                "threadName",
                "processName",
                "process",
                "getMessage",
                "exc_info",
                "exc_text",
                "stack_info",
            ]:
                log_data[key] = value

        return json.dumps(log_data, ensure_ascii=False, default=str)


# 全局日志系统实例
logging_system = LoggingSystem()
