"""
AI Agent Framework 缓存管理器

提供多层次缓存机制，支持内存缓存、LRU策略、TTL过期等功能。
优化频繁访问数据的性能。
"""

import asyncio
import hashlib
import json
import time
from collections import OrderedDict
from dataclasses import dataclass
from enum import Enum
from typing import Any, Callable, Dict, Optional, Union

from ai_agent_framework.utils.logging_system import logging_system


class CacheStrategy(Enum):
    """缓存策略枚举"""

    LRU = "lru"  # 最近最少使用
    LFU = "lfu"  # 最少使用频率
    FIFO = "fifo"  # 先进先出
    TTL = "ttl"  # 基于时间过期


@dataclass
class CacheEntry:
    """缓存条目"""

    key: str
    value: Any
    created_at: float
    last_accessed: float
    access_count: int = 0
    ttl: Optional[float] = None

    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return time.time() - self.created_at > self.ttl

    def touch(self):
        """更新访问时间和计数"""
        self.last_accessed = time.time()
        self.access_count += 1


class CacheManager:
    """
    缓存管理器

    提供高性能的内存缓存功能，支持多种缓存策略和自动清理。
    """

    def __init__(
        self,
        max_size: int = 1000,
        default_ttl: Optional[float] = None,
        strategy: CacheStrategy = CacheStrategy.LRU,
        cleanup_interval: float = 60.0,
        enable_stats: bool = True,
    ):
        """
        初始化缓存管理器

        Args:
            max_size: 最大缓存条目数
            default_ttl: 默认TTL（秒）
            strategy: 缓存策略
            cleanup_interval: 清理间隔（秒）
            enable_stats: 是否启用统计
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.strategy = strategy
        self.cleanup_interval = cleanup_interval
        self.enable_stats = enable_stats

        # 缓存存储
        if strategy == CacheStrategy.LRU:
            self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        else:
            self._cache: Dict[str, CacheEntry] = {}

        # 统计信息
        self._stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "expirations": 0,
            "sets": 0,
            "deletes": 0,
        }

        # 清理任务
        self._cleanup_task: Optional[asyncio.Task] = None

        self._logger = logging_system.get_logger("cache_manager")

        # 启动清理任务
        if cleanup_interval > 0:
            self._start_cleanup_task()

        self._logger.info(f"缓存管理器已初始化，策略: {strategy.value}")

    def _start_cleanup_task(self):
        """启动清理任务"""

        async def cleanup_loop():
            while True:
                try:
                    await asyncio.sleep(self.cleanup_interval)
                    self._cleanup_expired()
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    self._logger.error(f"缓存清理任务出错: {str(e)}")

        self._cleanup_task = asyncio.create_task(cleanup_loop())

    def _cleanup_expired(self):
        """清理过期条目"""
        expired_keys = []

        for key, entry in self._cache.items():
            if entry.is_expired():
                expired_keys.append(key)

        for key in expired_keys:
            del self._cache[key]
            if self.enable_stats:
                self._stats["expirations"] += 1

        if expired_keys:
            self._logger.debug(f"清理了 {len(expired_keys)} 个过期缓存条目")

    def _evict_if_needed(self):
        """根据策略驱逐条目"""
        if len(self._cache) < self.max_size:
            return

        if self.strategy == CacheStrategy.LRU:
            # LRU: 移除最近最少使用的
            self._cache.popitem(last=False)
        elif self.strategy == CacheStrategy.LFU:
            # LFU: 移除使用频率最低的
            min_key = min(self._cache.keys(), key=lambda k: self._cache[k].access_count)
            del self._cache[min_key]
        elif self.strategy == CacheStrategy.FIFO:
            # FIFO: 移除最早创建的
            oldest_key = min(
                self._cache.keys(), key=lambda k: self._cache[k].created_at
            )
            del self._cache[oldest_key]
        elif self.strategy == CacheStrategy.TTL:
            # TTL: 移除最早过期的
            earliest_expiry_key = min(
                self._cache.keys(),
                key=lambda k: self._cache[k].created_at + (self._cache[k].ttl or 0),
            )
            del self._cache[earliest_expiry_key]

        if self.enable_stats:
            self._stats["evictions"] += 1

    def _generate_key(self, key: Union[str, tuple, dict]) -> str:
        """生成缓存键"""
        if isinstance(key, str):
            return key
        elif isinstance(key, (tuple, list)):
            return hashlib.md5(str(key).encode()).hexdigest()
        elif isinstance(key, dict):
            # 对字典进行排序以确保一致性
            sorted_items = sorted(key.items())
            return hashlib.md5(str(sorted_items).encode()).hexdigest()
        else:
            return hashlib.md5(str(key).encode()).hexdigest()

    def get(self, key: Union[str, tuple, dict], default: Any = None) -> Any:
        """
        获取缓存值

        Args:
            key: 缓存键
            default: 默认值

        Returns:
            Any: 缓存值或默认值
        """
        cache_key = self._generate_key(key)

        if cache_key not in self._cache:
            if self.enable_stats:
                self._stats["misses"] += 1
            return default

        entry = self._cache[cache_key]

        # 检查是否过期
        if entry.is_expired():
            del self._cache[cache_key]
            if self.enable_stats:
                self._stats["misses"] += 1
                self._stats["expirations"] += 1
            return default

        # 更新访问信息
        entry.touch()

        # LRU策略需要移动到末尾
        if self.strategy == CacheStrategy.LRU:
            self._cache.move_to_end(cache_key)

        if self.enable_stats:
            self._stats["hits"] += 1

        return entry.value

    def set(
        self, key: Union[str, tuple, dict], value: Any, ttl: Optional[float] = None
    ) -> None:
        """
        设置缓存值

        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒）
        """
        cache_key = self._generate_key(key)

        # 使用默认TTL
        if ttl is None:
            ttl = self.default_ttl

        # 创建缓存条目
        entry = CacheEntry(
            key=cache_key,
            value=value,
            created_at=time.time(),
            last_accessed=time.time(),
            ttl=ttl,
        )

        # 如果键已存在，更新它
        if cache_key in self._cache:
            self._cache[cache_key] = entry
            if self.strategy == CacheStrategy.LRU:
                self._cache.move_to_end(cache_key)
        else:
            # 检查是否需要驱逐
            self._evict_if_needed()
            self._cache[cache_key] = entry

        if self.enable_stats:
            self._stats["sets"] += 1

    def delete(self, key: Union[str, tuple, dict]) -> bool:
        """
        删除缓存条目

        Args:
            key: 缓存键

        Returns:
            bool: 是否成功删除
        """
        cache_key = self._generate_key(key)

        if cache_key in self._cache:
            del self._cache[cache_key]
            if self.enable_stats:
                self._stats["deletes"] += 1
            return True

        return False

    def exists(self, key: Union[str, tuple, dict]) -> bool:
        """
        检查缓存键是否存在

        Args:
            key: 缓存键

        Returns:
            bool: 是否存在
        """
        cache_key = self._generate_key(key)

        if cache_key not in self._cache:
            return False

        entry = self._cache[cache_key]
        if entry.is_expired():
            del self._cache[cache_key]
            if self.enable_stats:
                self._stats["expirations"] += 1
            return False

        return True

    def clear(self) -> None:
        """清空所有缓存"""
        self._cache.clear()
        if self.enable_stats:
            self._stats = {key: 0 for key in self._stats}

        self._logger.info("缓存已清空")

    def get_or_set(
        self,
        key: Union[str, tuple, dict],
        factory: Callable[[], Any],
        ttl: Optional[float] = None,
    ) -> Any:
        """
        获取缓存值，如果不存在则通过工厂函数创建

        Args:
            key: 缓存键
            factory: 工厂函数
            ttl: 生存时间

        Returns:
            Any: 缓存值
        """
        value = self.get(key)

        if value is None:
            value = factory()
            self.set(key, value, ttl)

        return value

    async def get_or_set_async(
        self,
        key: Union[str, tuple, dict],
        factory: Callable[[], Any],
        ttl: Optional[float] = None,
    ) -> Any:
        """
        异步版本的get_or_set

        Args:
            key: 缓存键
            factory: 异步工厂函数
            ttl: 生存时间

        Returns:
            Any: 缓存值
        """
        value = self.get(key)

        if value is None:
            if asyncio.iscoroutinefunction(factory):
                value = await factory()
            else:
                value = factory()
            self.set(key, value, ttl)

        return value

    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        total_requests = self._stats["hits"] + self._stats["misses"]
        hit_rate = self._stats["hits"] / total_requests if total_requests > 0 else 0

        return {
            "size": len(self._cache),
            "max_size": self.max_size,
            "strategy": self.strategy.value,
            "default_ttl": self.default_ttl,
            "hit_rate": hit_rate,
            "stats": dict(self._stats),
            "cleanup_running": self._cleanup_task is not None
            and not self._cleanup_task.done(),
        }

    def keys(self) -> list:
        """获取所有缓存键"""
        return list(self._cache.keys())

    def values(self) -> list:
        """获取所有缓存值"""
        return [entry.value for entry in self._cache.values()]

    def items(self) -> list:
        """获取所有缓存键值对"""
        return [(key, entry.value) for key, entry in self._cache.items()]

    async def close(self):
        """关闭缓存管理器"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass

        self.clear()
        self._logger.info("缓存管理器已关闭")

    def __len__(self) -> int:
        """返回缓存大小"""
        return len(self._cache)

    def __contains__(self, key: Union[str, tuple, dict]) -> bool:
        """检查键是否存在"""
        return self.exists(key)

    def __getitem__(self, key: Union[str, tuple, dict]) -> Any:
        """获取缓存值"""
        value = self.get(key)
        if value is None:
            raise KeyError(key)
        return value

    def __setitem__(self, key: Union[str, tuple, dict], value: Any) -> None:
        """设置缓存值"""
        self.set(key, value)

    def __delitem__(self, key: Union[str, tuple, dict]) -> None:
        """删除缓存条目"""
        if not self.delete(key):
            raise KeyError(key)


# 全局缓存管理器实例
_global_cache_manager: Optional[CacheManager] = None


def get_global_cache_manager() -> CacheManager:
    """
    获取全局缓存管理器实例

    Returns:
        CacheManager: 缓存管理器实例
    """
    global _global_cache_manager

    if _global_cache_manager is None:
        _global_cache_manager = CacheManager()

    return _global_cache_manager


async def close_global_cache_manager():
    """关闭全局缓存管理器"""
    global _global_cache_manager

    if _global_cache_manager is not None:
        await _global_cache_manager.close()
        _global_cache_manager = None
