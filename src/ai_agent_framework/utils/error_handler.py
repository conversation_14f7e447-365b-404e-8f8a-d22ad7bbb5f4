"""
AI Agent Framework 错误处理器

提供统一的错误处理和恢复机制，包括重试策略、错误分类和上报功能。
支持异步错误处理和自定义错误处理策略。
"""

import asyncio
import functools
import traceback
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Type, Union

from ai_agent_framework.exceptions import AgentFrameworkError
from ai_agent_framework.utils.logging_system import logging_system


class RetryConfig:
    """重试配置"""

    def __init__(
        self,
        max_attempts: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True,
    ):
        """
        初始化重试配置

        Args:
            max_attempts: 最大重试次数
            base_delay: 基础延迟时间（秒）
            max_delay: 最大延迟时间（秒）
            exponential_base: 指数退避基数
            jitter: 是否添加随机抖动
        """
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter


class ErrorHandler:
    """
    错误处理器

    提供统一的错误处理机制，包括：
    - 错误分类和优先级
    - 重试策略
    - 错误恢复
    - 错误统计和报告
    """

    def __init__(self):
        """初始化错误处理器"""
        self._error_stats: Dict[str, int] = {}
        self._error_handlers: Dict[Type[Exception], Callable] = {}
        self._retry_configs: Dict[str, RetryConfig] = {}
        self._logger = logging_system.get_logger("error_handler")

    def register_handler(
        self,
        exception_type: Type[Exception],
        handler: Callable[[Exception, Dict[str, Any]], Any],
    ) -> None:
        """
        注册错误处理器

        Args:
            exception_type: 异常类型
            handler: 处理函数
        """
        self._error_handlers[exception_type] = handler

    def register_retry_config(self, operation: str, config: RetryConfig) -> None:
        """
        注册重试配置

        Args:
            operation: 操作名称
            config: 重试配置
        """
        self._retry_configs[operation] = config

    async def handle_error(
        self,
        error: Exception,
        context: Optional[Dict[str, Any]] = None,
        operation: Optional[str] = None,
    ) -> Optional[Any]:
        """
        处理错误

        Args:
            error: 异常对象
            context: 错误上下文
            operation: 操作名称

        Returns:
            Optional[Any]: 处理结果
        """
        error_type = type(error).__name__
        self._error_stats[error_type] = self._error_stats.get(error_type, 0) + 1

        # 记录错误日志
        logging_system.log_error(error, context)

        # 查找并执行自定义处理器
        for exc_type, handler in self._error_handlers.items():
            if isinstance(error, exc_type):
                try:
                    return await self._call_handler(handler, error, context or {})
                except Exception as handler_error:
                    self._logger.error(f"错误处理器执行失败: {handler_error}")

        # 默认处理逻辑
        return await self._default_error_handling(error, context, operation)

    async def _call_handler(
        self,
        handler: Callable,
        error: Exception,
        context: Dict[str, Any],
    ) -> Any:
        """调用错误处理器"""
        if asyncio.iscoroutinefunction(handler):
            return await handler(error, context)
        else:
            return handler(error, context)

    async def _default_error_handling(
        self,
        error: Exception,
        context: Optional[Dict[str, Any]],
        operation: Optional[str],
    ) -> None:
        """默认错误处理逻辑"""
        # 对于框架异常，提取更多信息
        if isinstance(error, AgentFrameworkError):
            self._logger.error(
                f"框架异常: {error.message}",
                extra={
                    "error_code": error.error_code,
                    "context": error.context,
                    "operation": operation,
                },
            )
        else:
            self._logger.error(
                f"未处理异常: {str(error)}",
                extra={
                    "error_type": type(error).__name__,
                    "traceback": traceback.format_exc(),
                    "operation": operation,
                },
            )

    def retry_on_error(
        self,
        operation: str,
        retry_config: Optional[RetryConfig] = None,
        exceptions: Optional[List[Type[Exception]]] = None,
    ):
        """
        重试装饰器

        Args:
            operation: 操作名称
            retry_config: 重试配置
            exceptions: 需要重试的异常类型列表
        """

        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                config = retry_config or self._retry_configs.get(
                    operation, RetryConfig()
                )
                retry_exceptions = exceptions or [Exception]

                last_exception = None

                for attempt in range(config.max_attempts):
                    try:
                        if asyncio.iscoroutinefunction(func):
                            return await func(*args, **kwargs)
                        else:
                            return func(*args, **kwargs)

                    except Exception as e:
                        last_exception = e

                        # 检查是否需要重试
                        if not any(
                            isinstance(e, exc_type) for exc_type in retry_exceptions
                        ):
                            raise e

                        # 如果是最后一次尝试，直接抛出异常
                        if attempt == config.max_attempts - 1:
                            break

                        # 计算延迟时间
                        delay = min(
                            config.base_delay * (config.exponential_base**attempt),
                            config.max_delay,
                        )

                        if config.jitter:
                            import random

                            delay *= 0.5 + random.random() * 0.5

                        self._logger.warning(
                            f"操作 {operation} 第 {attempt + 1} 次尝试失败，{delay:.2f}秒后重试: {str(e)}"
                        )

                        await asyncio.sleep(delay)

                # 处理最终失败
                await self.handle_error(last_exception, {"operation": operation})
                raise last_exception

            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                # 对于同步函数，创建事件循环来运行异步包装器
                try:
                    loop = asyncio.get_event_loop()
                except RuntimeError:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                return loop.run_until_complete(async_wrapper(*args, **kwargs))

            return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

        return decorator

    def get_error_stats(self) -> Dict[str, int]:
        """
        获取错误统计

        Returns:
            Dict[str, int]: 错误统计信息
        """
        return self._error_stats.copy()

    def reset_error_stats(self) -> None:
        """重置错误统计"""
        self._error_stats.clear()

    def create_error_report(self) -> Dict[str, Any]:
        """
        创建错误报告

        Returns:
            Dict[str, Any]: 错误报告
        """
        total_errors = sum(self._error_stats.values())

        return {
            "timestamp": datetime.now().isoformat(),
            "total_errors": total_errors,
            "error_breakdown": self._error_stats.copy(),
            "registered_handlers": len(self._error_handlers),
            "retry_configs": len(self._retry_configs),
        }

    async def handle_critical_error(
        self,
        error: Exception,
        context: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        处理关键错误

        Args:
            error: 异常对象
            context: 错误上下文
        """
        self._logger.critical(
            f"关键错误: {str(error)}",
            extra={
                "error_type": type(error).__name__,
                "traceback": traceback.format_exc(),
                "context": context or {},
                "timestamp": datetime.now().isoformat(),
            },
        )

        # 可以在这里添加告警通知逻辑
        # 例如发送邮件、Slack通知等

    def is_retryable_error(self, error: Exception) -> bool:
        """
        判断错误是否可重试

        Args:
            error: 异常对象

        Returns:
            bool: 是否可重试
        """
        # 定义不可重试的错误类型
        non_retryable_errors = [
            ValueError,
            TypeError,
            AttributeError,
            KeyError,
        ]

        return not any(isinstance(error, exc_type) for exc_type in non_retryable_errors)

    def categorize_error(self, error: Exception) -> str:
        """
        错误分类

        Args:
            error: 异常对象

        Returns:
            str: 错误类别
        """
        if isinstance(error, AgentFrameworkError):
            return "framework_error"
        elif isinstance(error, (ConnectionError, TimeoutError)):
            return "network_error"
        elif isinstance(error, (ValueError, TypeError)):
            return "validation_error"
        elif isinstance(error, PermissionError):
            return "permission_error"
        else:
            return "unknown_error"


# 全局错误处理器实例
error_handler = ErrorHandler()
