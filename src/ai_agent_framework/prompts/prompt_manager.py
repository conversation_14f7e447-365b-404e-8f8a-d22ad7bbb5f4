"""
AI Agent Framework Prompt管理器

管理Prompt模板的加载、缓存、版本控制和多语言支持。
提供统一的Prompt管理接口。
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from jinja2 import Environment, FileSystemLoader

from ai_agent_framework.exceptions import PromptError, PromptTemplateError
from ai_agent_framework.prompts.prompt_template import PromptTemplate
from ai_agent_framework.utils.logging_system import logging_system


class PromptManager:
    """
    Prompt管理器

    负责管理所有Prompt模板，包括加载、缓存、版本控制等功能。
    支持从文件系统、数据库等多种来源加载模板。
    """

    def __init__(
        self,
        template_dirs: Optional[List[Union[str, Path]]] = None,
        default_language: str = "zh",
        enable_cache: bool = True,
        auto_reload: bool = False,
    ):
        """
        初始化Prompt管理器

        Args:
            template_dirs: 模板目录列表
            default_language: 默认语言
            enable_cache: 是否启用缓存
            auto_reload: 是否自动重载模板
        """
        self.template_dirs = [Path(d) for d in (template_dirs or [])]
        self.default_language = default_language
        self.enable_cache = enable_cache
        self.auto_reload = auto_reload

        # 模板缓存
        self._template_cache: Dict[str, PromptTemplate] = {}
        self._template_registry: Dict[str, Dict[str, Any]] = {}

        # Jinja2环境
        if self.template_dirs:
            loader = FileSystemLoader([str(d) for d in self.template_dirs])
            self._jinja_env = Environment(loader=loader, auto_reload=auto_reload)
        else:
            self._jinja_env = Environment()

        self._logger = logging_system.get_logger("prompt_manager")

        # 加载模板
        self._load_templates()

        self._logger.info(
            f"Prompt管理器已初始化，加载了 {len(self._template_cache)} 个模板"
        )

    def _load_templates(self) -> None:
        """从模板目录加载所有模板"""
        for template_dir in self.template_dirs:
            if not template_dir.exists():
                self._logger.warning(f"模板目录不存在: {template_dir}")
                continue

            self._load_templates_from_dir(template_dir)

    def _load_templates_from_dir(self, template_dir: Path) -> None:
        """从指定目录加载模板"""
        try:
            # 查找所有模板文件
            for template_file in template_dir.rglob("*.j2"):
                try:
                    template = PromptTemplate.from_file(template_file)
                    self.register_template(template)
                except Exception as e:
                    self._logger.error(f"加载模板文件失败 {template_file}: {str(e)}")

            # 查找所有.txt模板文件
            for template_file in template_dir.rglob("*.txt"):
                if not template_file.with_suffix(".j2").exists():  # 避免重复加载
                    try:
                        template = PromptTemplate.from_file(template_file)
                        self.register_template(template)
                    except Exception as e:
                        self._logger.error(
                            f"加载模板文件失败 {template_file}: {str(e)}"
                        )

        except Exception as e:
            self._logger.error(f"扫描模板目录失败 {template_dir}: {str(e)}")

    def register_template(
        self, template: PromptTemplate, overwrite: bool = False
    ) -> None:
        """
        注册模板

        Args:
            template: 模板实例
            overwrite: 是否覆盖已存在的模板
        """
        template_key = f"{template.name}_{template.language}"

        if template_key in self._template_cache and not overwrite:
            raise PromptError(
                f"模板已存在: {template_key}", error_code="TEMPLATE_ALREADY_EXISTS"
            )

        self._template_cache[template_key] = template
        self._template_registry[template.name] = {
            "name": template.name,
            "description": template.description,
            "languages": self.get_template_languages(template.name),
            "variables": template.get_variables(),
            "metadata": template.metadata,
        }

        self._logger.debug(f"注册模板: {template_key}")

    def get_template(
        self, name: str, language: Optional[str] = None
    ) -> Optional[PromptTemplate]:
        """
        获取模板

        Args:
            name: 模板名称
            language: 语言代码，None使用默认语言

        Returns:
            Optional[PromptTemplate]: 模板实例，不存在时返回None
        """
        language = language or self.default_language
        template_key = f"{name}_{language}"

        # 从缓存获取
        if template_key in self._template_cache:
            return self._template_cache[template_key]

        # 尝试默认语言
        if language != self.default_language:
            default_key = f"{name}_{self.default_language}"
            if default_key in self._template_cache:
                self._logger.warning(f"模板 {template_key} 不存在，使用默认语言版本")
                return self._template_cache[default_key]

        # 尝试任意语言版本
        for key in self._template_cache:
            if key.startswith(f"{name}_"):
                self._logger.warning(f"模板 {template_key} 不存在，使用其他语言版本")
                return self._template_cache[key]

        return None

    def render_template(
        self,
        name: str,
        context: Optional[Dict[str, Any]] = None,
        language: Optional[str] = None,
        **kwargs,
    ) -> str:
        """
        渲染模板

        Args:
            name: 模板名称
            context: 上下文变量
            language: 语言代码
            **kwargs: 额外变量

        Returns:
            str: 渲染后的文本

        Raises:
            PromptTemplateError: 模板不存在或渲染失败时抛出
        """
        template = self.get_template(name, language)
        if template is None:
            raise PromptTemplateError(
                f"模板不存在: {name}",
                template_name=name,
                error_code="TEMPLATE_NOT_FOUND",
            )

        return template.render(context, **kwargs)

    def list_templates(self, language: Optional[str] = None) -> List[str]:
        """
        列出所有模板名称

        Args:
            language: 语言过滤，None返回所有语言的模板

        Returns:
            List[str]: 模板名称列表
        """
        if language is None:
            return list(self._template_registry.keys())

        templates = []
        for key in self._template_cache:
            if key.endswith(f"_{language}"):
                template_name = key[: -len(f"_{language}")]
                templates.append(template_name)

        return templates

    def get_template_info(self, name: str) -> Optional[Dict[str, Any]]:
        """
        获取模板信息

        Args:
            name: 模板名称

        Returns:
            Optional[Dict[str, Any]]: 模板信息，不存在时返回None
        """
        return self._template_registry.get(name)

    def get_template_languages(self, name: str) -> List[str]:
        """
        获取模板支持的语言列表

        Args:
            name: 模板名称

        Returns:
            List[str]: 语言代码列表
        """
        languages = []
        for key in self._template_cache:
            if key.startswith(f"{name}_"):
                language = key[len(f"{name}_") :]
                languages.append(language)

        return languages

    def remove_template(self, name: str, language: Optional[str] = None) -> bool:
        """
        移除模板

        Args:
            name: 模板名称
            language: 语言代码，None移除所有语言版本

        Returns:
            bool: 是否成功移除
        """
        if language is not None:
            # 移除特定语言版本
            template_key = f"{name}_{language}"
            if template_key in self._template_cache:
                del self._template_cache[template_key]

                # 如果没有其他语言版本，移除注册信息
                if not self.get_template_languages(name):
                    self._template_registry.pop(name, None)

                self._logger.info(f"移除模板: {template_key}")
                return True
        else:
            # 移除所有语言版本
            removed = False
            keys_to_remove = [
                key for key in self._template_cache if key.startswith(f"{name}_")
            ]

            for key in keys_to_remove:
                del self._template_cache[key]
                removed = True

            if removed:
                self._template_registry.pop(name, None)
                self._logger.info(f"移除模板所有版本: {name}")

            return removed

        return False

    def clear_cache(self) -> None:
        """清空模板缓存"""
        self._template_cache.clear()
        self._template_registry.clear()
        self._logger.info("模板缓存已清空")

    def reload_templates(self) -> None:
        """重新加载所有模板"""
        self.clear_cache()
        self._load_templates()
        self._logger.info("模板已重新加载")

    def export_templates(self, export_dir: Union[str, Path]) -> None:
        """
        导出所有模板到指定目录

        Args:
            export_dir: 导出目录
        """
        export_dir = Path(export_dir)
        export_dir.mkdir(parents=True, exist_ok=True)

        for template in self._template_cache.values():
            filename = f"{template.name}_{template.language}.j2"
            template.save_to_file(export_dir / filename)

        self._logger.info(f"模板已导出到: {export_dir}")

    def get_stats(self) -> Dict[str, Any]:
        """
        获取管理器统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        language_stats = {}
        for key in self._template_cache:
            language = key.split("_")[-1]
            language_stats[language] = language_stats.get(language, 0) + 1

        return {
            "total_templates": len(self._template_cache),
            "unique_names": len(self._template_registry),
            "template_dirs": [str(d) for d in self.template_dirs],
            "default_language": self.default_language,
            "language_distribution": language_stats,
            "cache_enabled": self.enable_cache,
            "auto_reload": self.auto_reload,
        }

    def __len__(self) -> int:
        """返回模板数量"""
        return len(self._template_cache)

    def __contains__(self, name: str) -> bool:
        """检查模板是否存在"""
        return name in self._template_registry

    def __iter__(self):
        """迭代所有模板"""
        return iter(self._template_cache.values())
