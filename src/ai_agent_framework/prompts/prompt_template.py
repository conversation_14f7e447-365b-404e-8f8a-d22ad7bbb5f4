"""
AI Agent Framework Prompt模板系统

基于Jinja2的Prompt模板引擎，支持动态上下文注入、多语言支持和模板继承。
提供灵活的Prompt生成和管理功能。
"""

import json
import os
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from jinja2 import Environment, FileSystemLoader, Template, TemplateNotFound
from jinja2.exceptions import TemplateError

from ai_agent_framework.exceptions import PromptError, PromptTemplateError
from ai_agent_framework.utils.logging_system import logging_system


class PromptTemplate:
    """
    Prompt模板类

    基于Jinja2模板引擎，支持动态变量替换、条件逻辑、循环等高级功能。
    提供多语言支持和模板继承机制。
    """

    def __init__(
        self,
        template_content: str,
        name: Optional[str] = None,
        description: Optional[str] = None,
        language: str = "zh",
        variables: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """
        初始化Prompt模板

        Args:
            template_content: 模板内容（Jinja2格式）
            name: 模板名称
            description: 模板描述
            language: 语言代码
            variables: 默认变量
            metadata: 元数据
        """
        self.template_content = template_content
        self.name = name or "unnamed_template"
        self.description = description or ""
        self.language = language
        self.variables = variables or {}
        self.metadata = metadata or {}

        self._logger = logging_system.get_logger("prompt_template")

        # 创建Jinja2模板
        try:
            self._template = Template(template_content)
        except TemplateError as e:
            raise PromptTemplateError(
                f"模板语法错误: {str(e)}",
                template_name=self.name,
                error_code="TEMPLATE_SYNTAX_ERROR",
                cause=e,
            )

        self._logger.debug(f"创建Prompt模板: {self.name}")

    def render(self, context: Optional[Dict[str, Any]] = None, **kwargs) -> str:
        """
        渲染模板

        Args:
            context: 上下文变量字典
            **kwargs: 额外的变量

        Returns:
            str: 渲染后的Prompt文本

        Raises:
            PromptTemplateError: 模板渲染失败时抛出
        """
        try:
            # 合并变量
            render_context = {}
            render_context.update(self.variables)  # 默认变量
            if context:
                render_context.update(context)  # 传入的上下文
            render_context.update(kwargs)  # 关键字参数

            # 添加内置变量
            render_context.update(
                {
                    "current_time": datetime.now().isoformat(),
                    "template_name": self.name,
                    "language": self.language,
                }
            )

            # 渲染模板
            rendered = self._template.render(**render_context)

            self._logger.debug(f"模板 {self.name} 渲染成功")
            return rendered.strip()

        except TemplateError as e:
            self._logger.error(f"模板 {self.name} 渲染失败: {str(e)}")
            raise PromptTemplateError(
                f"模板渲染失败: {str(e)}",
                template_name=self.name,
                error_code="TEMPLATE_RENDER_ERROR",
                cause=e,
            )
        except Exception as e:
            self._logger.error(f"模板 {self.name} 渲染出现未知错误: {str(e)}")
            raise PromptTemplateError(
                f"模板渲染出现未知错误: {str(e)}",
                template_name=self.name,
                error_code="TEMPLATE_UNKNOWN_ERROR",
                cause=e,
            )

    def validate_variables(self, context: Dict[str, Any]) -> List[str]:
        """
        验证模板变量

        Args:
            context: 上下文变量

        Returns:
            List[str]: 缺失的必需变量列表
        """
        try:
            # 尝试渲染模板以检查缺失变量
            self._template.render(**context)
            return []
        except Exception as e:
            # 解析错误信息以找出缺失的变量
            error_msg = str(e)
            missing_vars = []

            # 简单的缺失变量检测
            if "is undefined" in error_msg:
                # 提取变量名
                import re

                matches = re.findall(r"'(\w+)' is undefined", error_msg)
                missing_vars.extend(matches)

            return missing_vars

    def get_variables(self) -> List[str]:
        """
        获取模板中使用的所有变量

        Returns:
            List[str]: 变量名列表
        """
        try:
            # 使用Jinja2的AST分析获取变量
            from jinja2 import meta

            env = Environment()
            ast = env.parse(self.template_content)
            variables = meta.find_undeclared_variables(ast)
            return list(variables)
        except Exception as e:
            self._logger.warning(f"无法分析模板变量: {str(e)}")
            return []

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式

        Returns:
            Dict[str, Any]: 模板信息字典
        """
        return {
            "name": self.name,
            "description": self.description,
            "language": self.language,
            "template_content": self.template_content,
            "variables": self.variables,
            "metadata": self.metadata,
            "template_variables": self.get_variables(),
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "PromptTemplate":
        """
        从字典创建模板

        Args:
            data: 模板数据字典

        Returns:
            PromptTemplate: 模板实例
        """
        return cls(
            template_content=data["template_content"],
            name=data.get("name"),
            description=data.get("description"),
            language=data.get("language", "zh"),
            variables=data.get("variables"),
            metadata=data.get("metadata"),
        )

    @classmethod
    def from_file(cls, file_path: Union[str, Path]) -> "PromptTemplate":
        """
        从文件加载模板

        Args:
            file_path: 模板文件路径

        Returns:
            PromptTemplate: 模板实例
        """
        file_path = Path(file_path)

        if not file_path.exists():
            raise PromptTemplateError(
                f"模板文件不存在: {file_path}",
                template_name=str(file_path),
                error_code="TEMPLATE_FILE_NOT_FOUND",
            )

        try:
            # 读取模板内容
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # 尝试读取同名的JSON配置文件
            config_path = file_path.with_suffix(".json")
            config = {}
            if config_path.exists():
                with open(config_path, "r", encoding="utf-8") as f:
                    config = json.load(f)

            return cls(
                template_content=content,
                name=config.get("name", file_path.stem),
                description=config.get("description", ""),
                language=config.get("language", "zh"),
                variables=config.get("variables", {}),
                metadata=config.get("metadata", {}),
            )

        except Exception as e:
            raise PromptTemplateError(
                f"加载模板文件失败: {str(e)}",
                template_name=str(file_path),
                error_code="TEMPLATE_FILE_LOAD_ERROR",
                cause=e,
            )

    def save_to_file(self, file_path: Union[str, Path]) -> None:
        """
        保存模板到文件

        Args:
            file_path: 保存路径
        """
        file_path = Path(file_path)

        try:
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # 保存模板内容
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(self.template_content)

            # 保存配置信息
            config_path = file_path.with_suffix(".json")
            config = {
                "name": self.name,
                "description": self.description,
                "language": self.language,
                "variables": self.variables,
                "metadata": self.metadata,
            }

            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            self._logger.info(f"模板已保存到: {file_path}")

        except Exception as e:
            raise PromptTemplateError(
                f"保存模板文件失败: {str(e)}",
                template_name=self.name,
                error_code="TEMPLATE_FILE_SAVE_ERROR",
                cause=e,
            )

    def __str__(self) -> str:
        """字符串表示"""
        return f"PromptTemplate(name='{self.name}', language='{self.language}')"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (
            f"PromptTemplate(name='{self.name}', description='{self.description}', "
            f"language='{self.language}', variables={len(self.variables)})"
        )


class PromptTemplateBuilder:
    """
    Prompt模板构建器

    提供链式API来构建复杂的Prompt模板。
    """

    def __init__(self):
        """初始化构建器"""
        self._parts = []
        self._variables = {}
        self._metadata = {}
        self._name = None
        self._description = None
        self._language = "zh"

    def add_system_message(self, content: str) -> "PromptTemplateBuilder":
        """添加系统消息"""
        self._parts.append(f"# 系统消息\n{content}\n")
        return self

    def add_user_message(self, content: str) -> "PromptTemplateBuilder":
        """添加用户消息"""
        self._parts.append(f"# 用户消息\n{content}\n")
        return self

    def add_context(self, context_template: str) -> "PromptTemplateBuilder":
        """添加上下文信息"""
        self._parts.append(f"# 上下文信息\n{context_template}\n")
        return self

    def add_instructions(self, instructions: str) -> "PromptTemplateBuilder":
        """添加指令"""
        self._parts.append(f"# 指令\n{instructions}\n")
        return self

    def add_examples(self, examples: str) -> "PromptTemplateBuilder":
        """添加示例"""
        self._parts.append(f"# 示例\n{examples}\n")
        return self

    def add_constraints(self, constraints: str) -> "PromptTemplateBuilder":
        """添加约束条件"""
        self._parts.append(f"# 约束条件\n{constraints}\n")
        return self

    def add_custom_section(self, title: str, content: str) -> "PromptTemplateBuilder":
        """添加自定义部分"""
        self._parts.append(f"# {title}\n{content}\n")
        return self

    def set_name(self, name: str) -> "PromptTemplateBuilder":
        """设置模板名称"""
        self._name = name
        return self

    def set_description(self, description: str) -> "PromptTemplateBuilder":
        """设置模板描述"""
        self._description = description
        return self

    def set_language(self, language: str) -> "PromptTemplateBuilder":
        """设置语言"""
        self._language = language
        return self

    def add_variable(
        self, name: str, default_value: Any = None
    ) -> "PromptTemplateBuilder":
        """添加变量"""
        self._variables[name] = default_value
        return self

    def add_metadata(self, key: str, value: Any) -> "PromptTemplateBuilder":
        """添加元数据"""
        self._metadata[key] = value
        return self

    def build(self) -> PromptTemplate:
        """构建模板"""
        template_content = "\n".join(self._parts)

        return PromptTemplate(
            template_content=template_content,
            name=self._name,
            description=self._description,
            language=self._language,
            variables=self._variables,
            metadata=self._metadata,
        )


# 预定义的常用模板
REACT_TEMPLATE = PromptTemplate(
    template_content="""你是一个智能助手，使用ReAct（Reasoning and Acting）方法来解决问题。

请按照以下格式进行推理：

思考: [分析问题，思考下一步行动]
行动: [如果需要使用工具，调用相应的工具]
观察: [观察工具执行结果]
思考: [基于观察结果继续思考]
...
最终答案: [给出最终答案]

重要规则：
1. 每次只能执行一个行动
2. 必须基于观察结果进行下一步思考
3. 如果工具执行失败，需要分析原因并尝试其他方法
4. 当有足够信息回答问题时，给出最终答案

{% if context %}
上下文信息：
{{ context }}
{% endif %}

{% if examples %}
示例：
{{ examples }}
{% endif %}

现在开始解决用户的问题：{{ user_query }}""",
    name="react_template",
    description="ReAct推理模式模板",
    language="zh",
)

COT_TEMPLATE = PromptTemplate(
    template_content="""你是一个智能助手，请使用逐步推理的方法来解决问题。

请按照以下步骤进行思考：
1. 理解问题：明确用户想要什么
2. 分析问题：分解问题的各个部分
3. 制定计划：确定解决问题的步骤
4. 执行计划：逐步执行，如需要可以使用工具
5. 验证结果：检查答案是否合理
6. 总结答案：给出清晰的最终答案

{% if context %}
上下文信息：
{{ context }}
{% endif %}

{% if constraints %}
约束条件：
{{ constraints }}
{% endif %}

请在回答中展示你的思考过程，让用户了解你是如何得出答案的。

用户问题：{{ user_query }}""",
    name="cot_template",
    description="Chain-of-Thought推理模式模板",
    language="zh",
)

CHAT_TEMPLATE = PromptTemplate(
    template_content="""你是一个友好、有帮助的AI助手。

{% if personality %}
个性特征：{{ personality }}
{% endif %}

{% if knowledge_cutoff %}
知识截止时间：{{ knowledge_cutoff }}
{% endif %}

{% if conversation_history %}
对话历史：
{% for message in conversation_history %}
{{ message.role }}: {{ message.content }}
{% endfor %}
{% endif %}

请根据用户的问题提供有帮助的回答。如果需要使用工具来获取信息或执行任务，请合理使用。

用户：{{ user_query }}
助手：""",
    name="chat_template",
    description="通用聊天模板",
    language="zh",
)
