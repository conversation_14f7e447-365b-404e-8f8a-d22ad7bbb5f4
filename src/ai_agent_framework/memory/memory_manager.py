"""
AI Agent Framework 记忆管理器

实现多层次记忆管理系统，支持短期、工作、长期记忆的存储、检索和管理。
提供基于相似度的记忆搜索功能。
"""

import asyncio
import json
import sqlite3
import threading
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from uuid import uuid4

from ai_agent_framework.core.interfaces import MemoryInterface
from ai_agent_framework.exceptions import (
    MemoryError,
    MemoryRetrievalError,
    MemoryStorageError,
)
from ai_agent_framework.utils.logging_system import logging_system


class MemoryType:
    """记忆类型枚举"""

    SHORT_TERM = "short_term"  # 短期记忆
    WORKING = "working"  # 工作记忆
    LONG_TERM = "long_term"  # 长期记忆


class MemoryManager(MemoryInterface):
    """
    记忆管理器

    实现多层次记忆管理系统：
    - 短期记忆：临时信息，TTL较短
    - 工作记忆：当前任务相关信息，中等TTL
    - 长期记忆：重要信息，长期保存
    """

    def __init__(
        self,
        storage_path: str = "memory.db",
        short_term_ttl: int = 3600,  # 1小时
        working_ttl: int = 86400,  # 1天
        long_term_ttl: Optional[int] = None,  # 永久
        auto_cleanup: bool = True,
        cleanup_interval: int = 3600,  # 1小时
    ):
        """
        初始化记忆管理器

        Args:
            storage_path: 数据库文件路径
            short_term_ttl: 短期记忆TTL（秒）
            working_ttl: 工作记忆TTL（秒）
            long_term_ttl: 长期记忆TTL（秒），None表示永久
            auto_cleanup: 是否自动清理过期记忆
            cleanup_interval: 清理间隔（秒）
        """
        self.storage_path = storage_path
        self.short_term_ttl = short_term_ttl
        self.working_ttl = working_ttl
        self.long_term_ttl = long_term_ttl
        self.auto_cleanup = auto_cleanup
        self.cleanup_interval = cleanup_interval

        self._logger = logging_system.get_logger("memory_manager")
        self._lock = threading.Lock()
        self._cleanup_task: Optional[asyncio.Task] = None
        self._connection: Optional[sqlite3.Connection] = None

        # 初始化数据库
        self._init_database()

        # 启动自动清理任务
        if auto_cleanup:
            self._start_cleanup_task()

        self._logger.info("记忆管理器已初始化")

    def _init_database(self) -> None:
        """初始化SQLite数据库"""
        try:
            # 对于内存数据库，需要保持连接
            if self.storage_path == ":memory:":
                self._connection = sqlite3.connect(
                    self.storage_path, check_same_thread=False
                )
                conn = self._connection
            else:
                conn = sqlite3.connect(self.storage_path)

            cursor = conn.cursor()

            # 创建记忆表
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS memories (
                    id TEXT PRIMARY KEY,
                    key TEXT NOT NULL,
                    value TEXT NOT NULL,
                    memory_type TEXT NOT NULL,
                    created_at REAL NOT NULL,
                    expires_at REAL,
                    metadata TEXT,
                    UNIQUE(key, memory_type)
                )
            """
            )

            # 创建索引
            cursor.execute(
                "CREATE INDEX IF NOT EXISTS idx_memory_type ON memories(memory_type)"
            )
            cursor.execute(
                "CREATE INDEX IF NOT EXISTS idx_expires_at ON memories(expires_at)"
            )
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_key ON memories(key)")

            conn.commit()

            if self.storage_path != ":memory:":
                conn.close()

            self._logger.info("数据库初始化完成")

        except Exception as e:
            self._logger.error(f"数据库初始化失败: {str(e)}")
            raise MemoryStorageError(
                f"数据库初始化失败: {str(e)}", error_code="DATABASE_INIT_ERROR", cause=e
            )

    def _start_cleanup_task(self) -> None:
        """启动自动清理任务"""

        async def cleanup_loop():
            while True:
                try:
                    await asyncio.sleep(self.cleanup_interval)
                    await self._cleanup_expired_memories()
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    self._logger.error(f"自动清理任务出错: {str(e)}")

        self._cleanup_task = asyncio.create_task(cleanup_loop())

    async def store(
        self,
        key: str,
        value: Any,
        memory_type: str = MemoryType.WORKING,
        ttl: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        存储记忆

        Args:
            key: 记忆键
            value: 记忆值
            memory_type: 记忆类型
            ttl: 生存时间（秒）
            metadata: 元数据

        Returns:
            bool: 是否存储成功
        """
        try:
            # 确定TTL
            if ttl is None:
                if memory_type == MemoryType.SHORT_TERM:
                    ttl = self.short_term_ttl
                elif memory_type == MemoryType.WORKING:
                    ttl = self.working_ttl
                else:  # LONG_TERM
                    ttl = self.long_term_ttl

            # 计算过期时间
            expires_at = None
            if ttl is not None:
                expires_at = time.time() + ttl

            # 序列化数据
            value_json = json.dumps(value, ensure_ascii=False, default=str)
            metadata_json = json.dumps(metadata or {}, ensure_ascii=False, default=str)

            # 存储到数据库
            conn = (
                self._connection
                if self._connection
                else sqlite3.connect(self.storage_path)
            )
            try:
                cursor = conn.cursor()

                memory_id = str(uuid4())
                cursor.execute(
                    """
                    INSERT OR REPLACE INTO memories
                    (id, key, value, memory_type, created_at, expires_at, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """,
                    (
                        memory_id,
                        key,
                        value_json,
                        memory_type,
                        time.time(),
                        expires_at,
                        metadata_json,
                    ),
                )

                conn.commit()
            finally:
                if not self._connection:
                    conn.close()

            self._logger.debug(f"存储记忆: {key} ({memory_type})")
            return True

        except Exception as e:
            self._logger.error(f"存储记忆失败: {str(e)}")
            raise MemoryStorageError(
                f"存储记忆失败: {str(e)}",
                memory_type=memory_type,
                error_code="MEMORY_STORE_ERROR",
                cause=e,
            )

    async def retrieve(
        self,
        key: str,
        memory_type: str = MemoryType.WORKING,
    ) -> Optional[Any]:
        """
        检索记忆

        Args:
            key: 记忆键
            memory_type: 记忆类型

        Returns:
            Optional[Any]: 记忆值，不存在时返回None
        """
        try:
            conn = (
                self._connection
                if self._connection
                else sqlite3.connect(self.storage_path)
            )
            try:
                cursor = conn.cursor()

                # 查询记忆
                cursor.execute(
                    """
                    SELECT value, expires_at FROM memories
                    WHERE key = ? AND memory_type = ?
                """,
                    (key, memory_type),
                )

                result = cursor.fetchone()

                if result is None:
                    return None

                value_json, expires_at = result

                # 检查是否过期
                if expires_at is not None and time.time() > expires_at:
                    # 删除过期记忆
                    cursor.execute(
                        """
                        DELETE FROM memories
                        WHERE key = ? AND memory_type = ?
                    """,
                        (key, memory_type),
                    )
                    conn.commit()
                    return None

                # 反序列化数据
                value = json.loads(value_json)

                self._logger.debug(f"检索记忆: {key} ({memory_type})")
                return value
            finally:
                if not self._connection:
                    conn.close()

        except Exception as e:
            self._logger.error(f"检索记忆失败: {str(e)}")
            raise MemoryRetrievalError(
                f"检索记忆失败: {str(e)}",
                memory_type=memory_type,
                error_code="MEMORY_RETRIEVE_ERROR",
                cause=e,
            )

    async def search(
        self,
        query: str,
        memory_type: str = MemoryType.WORKING,
        limit: int = 10,
        similarity_threshold: float = 0.7,
    ) -> List[Dict[str, Any]]:
        """
        搜索相似记忆

        Args:
            query: 搜索查询
            memory_type: 记忆类型
            limit: 返回数量限制
            similarity_threshold: 相似度阈值

        Returns:
            List[Dict[str, Any]]: 搜索结果列表
        """
        try:
            with sqlite3.connect(self.storage_path) as conn:
                cursor = conn.cursor()

                # 获取指定类型的所有记忆
                cursor.execute(
                    """
                    SELECT key, value, metadata, created_at, expires_at 
                    FROM memories 
                    WHERE memory_type = ? AND (expires_at IS NULL OR expires_at > ?)
                    ORDER BY created_at DESC
                """,
                    (memory_type, time.time()),
                )

                results = []
                for row in cursor.fetchall():
                    key, value_json, metadata_json, created_at, expires_at = row

                    try:
                        value = json.loads(value_json)
                        metadata = json.loads(metadata_json)

                        # 简单的文本相似度计算（基于关键词匹配）
                        similarity = self._calculate_similarity(query, str(value))

                        if similarity >= similarity_threshold:
                            results.append(
                                {
                                    "key": key,
                                    "value": value,
                                    "metadata": metadata,
                                    "similarity": similarity,
                                    "created_at": datetime.fromtimestamp(
                                        created_at
                                    ).isoformat(),
                                }
                            )

                    except json.JSONDecodeError:
                        continue

                # 按相似度排序并限制数量
                results.sort(key=lambda x: x["similarity"], reverse=True)
                return results[:limit]

        except Exception as e:
            self._logger.error(f"搜索记忆失败: {str(e)}")
            raise MemoryRetrievalError(
                f"搜索记忆失败: {str(e)}",
                memory_type=memory_type,
                error_code="MEMORY_SEARCH_ERROR",
                cause=e,
            )

    async def delete(
        self,
        key: str,
        memory_type: str = MemoryType.WORKING,
    ) -> bool:
        """
        删除记忆

        Args:
            key: 记忆键
            memory_type: 记忆类型

        Returns:
            bool: 是否删除成功
        """
        try:
            with sqlite3.connect(self.storage_path) as conn:
                cursor = conn.cursor()

                cursor.execute(
                    """
                    DELETE FROM memories 
                    WHERE key = ? AND memory_type = ?
                """,
                    (key, memory_type),
                )

                deleted_count = cursor.rowcount
                conn.commit()

                self._logger.debug(f"删除记忆: {key} ({memory_type})")
                return deleted_count > 0

        except Exception as e:
            self._logger.error(f"删除记忆失败: {str(e)}")
            return False

    async def clear(
        self,
        memory_type: Optional[str] = None,
    ) -> bool:
        """
        清空记忆

        Args:
            memory_type: 记忆类型，None表示清空所有类型

        Returns:
            bool: 是否清空成功
        """
        try:
            with sqlite3.connect(self.storage_path) as conn:
                cursor = conn.cursor()

                if memory_type is None:
                    cursor.execute("DELETE FROM memories")
                else:
                    cursor.execute(
                        "DELETE FROM memories WHERE memory_type = ?", (memory_type,)
                    )

                deleted_count = cursor.rowcount
                conn.commit()

                self._logger.info(
                    f"清空记忆: {memory_type or '所有类型'}, 删除 {deleted_count} 条记录"
                )
                return True

        except Exception as e:
            self._logger.error(f"清空记忆失败: {str(e)}")
            return False

    async def get_stats(self) -> Dict[str, Any]:
        """
        获取记忆统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            with sqlite3.connect(self.storage_path) as conn:
                cursor = conn.cursor()

                # 总记忆数
                cursor.execute("SELECT COUNT(*) FROM memories")
                total_count = cursor.fetchone()[0]

                # 按类型统计
                cursor.execute(
                    """
                    SELECT memory_type, COUNT(*) 
                    FROM memories 
                    GROUP BY memory_type
                """
                )
                type_counts = dict(cursor.fetchall())

                # 过期记忆数
                cursor.execute(
                    """
                    SELECT COUNT(*) FROM memories 
                    WHERE expires_at IS NOT NULL AND expires_at <= ?
                """,
                    (time.time(),),
                )
                expired_count = cursor.fetchone()[0]

                return {
                    "total_memories": total_count,
                    "by_type": type_counts,
                    "expired_memories": expired_count,
                    "storage_path": self.storage_path,
                }

        except Exception as e:
            self._logger.error(f"获取统计信息失败: {str(e)}")
            return {}

    async def _cleanup_expired_memories(self) -> int:
        """
        清理过期记忆

        Returns:
            int: 清理的记忆数量
        """
        try:
            with sqlite3.connect(self.storage_path) as conn:
                cursor = conn.cursor()

                cursor.execute(
                    """
                    DELETE FROM memories 
                    WHERE expires_at IS NOT NULL AND expires_at <= ?
                """,
                    (time.time(),),
                )

                deleted_count = cursor.rowcount
                conn.commit()

                if deleted_count > 0:
                    self._logger.info(f"清理了 {deleted_count} 条过期记忆")

                return deleted_count

        except Exception as e:
            self._logger.error(f"清理过期记忆失败: {str(e)}")
            return 0

    def _calculate_similarity(self, query: str, text: str) -> float:
        """
        计算文本相似度（简单实现）

        Args:
            query: 查询文本
            text: 目标文本

        Returns:
            float: 相似度分数 (0-1)
        """
        query_words = set(query.lower().split())
        text_words = set(text.lower().split())

        if not query_words or not text_words:
            return 0.0

        # 计算Jaccard相似度
        intersection = query_words.intersection(text_words)
        union = query_words.union(text_words)

        return len(intersection) / len(union) if union else 0.0

    async def close(self) -> None:
        """关闭记忆管理器"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass

        if self._connection:
            self._connection.close()
            self._connection = None

        self._logger.info("记忆管理器已关闭")
