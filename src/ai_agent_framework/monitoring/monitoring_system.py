"""
AI Agent Framework 监控系统

集成指标收集、健康检查、告警等功能的统一监控系统。
提供HTTP接口用于外部监控系统集成。
"""

import asyncio
import json
import threading
from datetime import datetime
from http.server import BaseHTTPRequestHandler, HTTPServer
from typing import Any, Dict, List, Optional
from urllib.parse import parse_qs, urlparse

from ai_agent_framework.monitoring.health_checker import HealthChecker, HealthStatus
from ai_agent_framework.monitoring.metrics_collector import MetricsCollector
from ai_agent_framework.utils.logging_system import logging_system


class MonitoringHTTPHandler(BaseHTTPRequestHandler):
    """监控HTTP处理器"""

    def __init__(self, monitoring_system, *args, **kwargs):
        self.monitoring_system = monitoring_system
        super().__init__(*args, **kwargs)

    def do_GET(self):
        """处理GET请求"""
        try:
            parsed_path = urlparse(self.path)
            path = parsed_path.path

            if path == "/health":
                self._handle_health_check()
            elif path == "/metrics":
                self._handle_metrics()
            elif path == "/status":
                self._handle_status()
            elif path == "/":
                self._handle_index()
            else:
                self._send_error(404, "Not Found")

        except Exception as e:
            self._send_error(500, f"Internal Server Error: {str(e)}")

    def _handle_health_check(self):
        """处理健康检查请求"""
        try:
            # 获取健康状态（同步方式）
            health_data = asyncio.run(
                self.monitoring_system.health_checker.get_overall_health()
            )

            status_code = 200
            if health_data["status"] == HealthStatus.UNHEALTHY.value:
                status_code = 503
            elif health_data["status"] == HealthStatus.DEGRADED.value:
                status_code = 200  # 降级状态仍返回200

            self._send_json_response(health_data, status_code)

        except Exception as e:
            self._send_error(500, f"Health check failed: {str(e)}")

    def _handle_metrics(self):
        """处理指标请求"""
        try:
            accept_header = self.headers.get("Accept", "")

            if "application/json" in accept_header:
                # 返回JSON格式
                metrics_data = (
                    self.monitoring_system.metrics_collector.get_all_metrics()
                )
                self._send_json_response(metrics_data)
            else:
                # 返回Prometheus格式
                prometheus_data = (
                    self.monitoring_system.metrics_collector.export_prometheus_format()
                )
                self._send_text_response(prometheus_data, content_type="text/plain")

        except Exception as e:
            self._send_error(500, f"Metrics export failed: {str(e)}")

    def _handle_status(self):
        """处理状态请求"""
        try:
            status_data = self.monitoring_system.get_system_status()
            self._send_json_response(status_data)

        except Exception as e:
            self._send_error(500, f"Status check failed: {str(e)}")

    def _handle_index(self):
        """处理首页请求"""
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>AI Agent Framework Monitoring</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .endpoint { margin: 20px 0; padding: 10px; background: #f5f5f5; }
                .status-healthy { color: green; }
                .status-degraded { color: orange; }
                .status-unhealthy { color: red; }
            </style>
        </head>
        <body>
            <h1>AI Agent Framework Monitoring</h1>
            <div class="endpoint">
                <h3>Available Endpoints:</h3>
                <ul>
                    <li><a href="/health">/health</a> - Health check</li>
                    <li><a href="/metrics">/metrics</a> - Metrics (Prometheus format)</li>
                    <li><a href="/status">/status</a> - System status</li>
                </ul>
            </div>
        </body>
        </html>
        """
        self._send_text_response(html, content_type="text/html")

    def _send_json_response(self, data: Dict[str, Any], status_code: int = 200):
        """发送JSON响应"""
        self.send_response(status_code)
        self.send_header("Content-Type", "application/json")
        self.send_header("Access-Control-Allow-Origin", "*")
        self.end_headers()

        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(json_data.encode("utf-8"))

    def _send_text_response(
        self, text: str, status_code: int = 200, content_type: str = "text/plain"
    ):
        """发送文本响应"""
        self.send_response(status_code)
        self.send_header("Content-Type", content_type)
        self.send_header("Access-Control-Allow-Origin", "*")
        self.end_headers()

        self.wfile.write(text.encode("utf-8"))

    def _send_error(self, status_code: int, message: str):
        """发送错误响应"""
        self.send_response(status_code)
        self.send_header("Content-Type", "application/json")
        self.end_headers()

        error_data = {
            "error": message,
            "status_code": status_code,
            "timestamp": datetime.now().isoformat(),
        }

        json_data = json.dumps(error_data, ensure_ascii=False)
        self.wfile.write(json_data.encode("utf-8"))

    def log_message(self, format, *args):
        """重写日志方法以使用框架日志系统"""
        pass  # 禁用默认日志


class MonitoringSystem:
    """
    监控系统

    集成指标收集、健康检查等功能，提供统一的监控接口。
    """

    def __init__(
        self,
        metrics_collector: Optional[MetricsCollector] = None,
        health_checker: Optional[HealthChecker] = None,
        http_port: int = 8000,
        enable_http_server: bool = True,
    ):
        """
        初始化监控系统

        Args:
            metrics_collector: 指标收集器
            health_checker: 健康检查器
            http_port: HTTP服务端口
            enable_http_server: 是否启用HTTP服务
        """
        self.metrics_collector = metrics_collector or MetricsCollector()
        self.health_checker = health_checker or HealthChecker()
        self.http_port = http_port
        self.enable_http_server = enable_http_server

        # HTTP服务器
        self._http_server: Optional[HTTPServer] = None
        self._http_thread: Optional[threading.Thread] = None

        self._logger = logging_system.get_logger("monitoring_system")

        self._logger.info("监控系统已初始化")

    def start(self) -> None:
        """启动监控系统"""
        try:
            # 启动指标收集
            self.metrics_collector.start_collection()

            # 启动健康检查
            self.health_checker.start_monitoring()

            # 启动HTTP服务器
            if self.enable_http_server:
                self._start_http_server()

            self._logger.info("监控系统已启动")

        except Exception as e:
            self._logger.error(f"监控系统启动失败: {str(e)}")
            raise

    def stop(self) -> None:
        """停止监控系统"""
        try:
            # 停止HTTP服务器
            if self._http_server:
                self._http_server.shutdown()
                self._http_server = None

            if self._http_thread and self._http_thread.is_alive():
                self._http_thread.join(timeout=5)
                self._http_thread = None

            # 停止健康检查
            self.health_checker.stop_monitoring()

            # 停止指标收集
            self.metrics_collector.stop_collection()

            self._logger.info("监控系统已停止")

        except Exception as e:
            self._logger.error(f"监控系统停止失败: {str(e)}")

    def _start_http_server(self) -> None:
        """启动HTTP服务器"""
        try:

            def handler_factory(*args, **kwargs):
                return MonitoringHTTPHandler(self, *args, **kwargs)

            self._http_server = HTTPServer(("0.0.0.0", self.http_port), handler_factory)

            def serve_forever():
                self._http_server.serve_forever()

            self._http_thread = threading.Thread(target=serve_forever, daemon=True)
            self._http_thread.start()

            self._logger.info(f"监控HTTP服务器已启动，端口: {self.http_port}")

        except Exception as e:
            self._logger.error(f"HTTP服务器启动失败: {str(e)}")
            raise

    def record_agent_request(self, duration: float, success: bool = True) -> None:
        """记录Agent请求"""
        self.metrics_collector.increment_counter("agent_requests_total")
        self.metrics_collector.record_timer("agent_request_duration", duration)

        if not success:
            self.metrics_collector.increment_counter(
                "error_count", labels={"type": "agent_request"}
            )

    def record_model_call(
        self, model_name: str, duration: float, success: bool = True
    ) -> None:
        """记录模型调用"""
        labels = {"model": model_name}
        self.metrics_collector.increment_counter("model_calls_total", labels=labels)
        self.metrics_collector.record_timer(
            "model_call_duration", duration, labels=labels
        )

        if not success:
            self.metrics_collector.increment_counter(
                "error_count", labels={"type": "model_call", "model": model_name}
            )

    def record_tool_execution(
        self, tool_name: str, duration: float, success: bool = True
    ) -> None:
        """记录工具执行"""
        labels = {"tool": tool_name}
        self.metrics_collector.increment_counter("tool_executions_total", labels=labels)
        self.metrics_collector.record_timer(
            "tool_execution_duration", duration, labels=labels
        )

        if not success:
            self.metrics_collector.increment_counter(
                "error_count", labels={"type": "tool_execution", "tool": tool_name}
            )

    def record_memory_operation(self, operation: str, success: bool = True) -> None:
        """记录记忆操作"""
        labels = {"operation": operation}
        self.metrics_collector.increment_counter(
            "memory_operations_total", labels=labels
        )

        if not success:
            self.metrics_collector.increment_counter(
                "error_count",
                labels={"type": "memory_operation", "operation": operation},
            )

    def set_active_agents(self, count: int) -> None:
        """设置活跃Agent数量"""
        self.metrics_collector.set_gauge("active_agents", count)

    def get_system_status(self) -> Dict[str, Any]:
        """
        获取系统状态

        Returns:
            Dict[str, Any]: 系统状态信息
        """
        # 获取健康状态
        health_status = asyncio.run(self.health_checker.get_overall_health())

        # 获取指标统计
        metrics_stats = self.metrics_collector.get_collection_stats()

        # 获取监控统计
        monitoring_stats = self.health_checker.get_monitoring_stats()

        return {
            "timestamp": datetime.now().isoformat(),
            "health": health_status,
            "metrics": metrics_stats,
            "monitoring": monitoring_stats,
            "http_server": {
                "enabled": self.enable_http_server,
                "port": self.http_port,
                "running": self._http_server is not None,
            },
        }

    def register_custom_metric(
        self,
        name: str,
        metric_type: str,
        description: str = "",
        unit: str = "",
    ) -> None:
        """
        注册自定义指标

        Args:
            name: 指标名称
            metric_type: 指标类型
            description: 描述
            unit: 单位
        """
        from ai_agent_framework.monitoring.metrics_collector import MetricType

        try:
            metric_type_enum = MetricType(metric_type)
            self.metrics_collector.register_metric(
                name, metric_type_enum, description, unit
            )
            self._logger.debug(f"注册自定义指标: {name}")
        except ValueError:
            self._logger.error(f"无效的指标类型: {metric_type}")

    def register_custom_health_check(
        self,
        name: str,
        check_func,
        description: str = "",
        critical: bool = False,
    ) -> None:
        """
        注册自定义健康检查

        Args:
            name: 检查名称
            check_func: 检查函数
            description: 描述
            critical: 是否为关键检查
        """
        self.health_checker.register_check(
            name, check_func, description, critical=critical
        )
        self._logger.debug(f"注册自定义健康检查: {name}")

    def __enter__(self):
        """上下文管理器入口"""
        self.start()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop()
