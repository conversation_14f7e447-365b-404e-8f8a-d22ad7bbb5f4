"""
AI Agent Framework 指标收集器

收集和管理系统性能指标，支持多种指标类型和导出格式。
提供实时监控和历史数据分析功能。
"""

import asyncio
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from ai_agent_framework.utils.logging_system import logging_system


class MetricType(Enum):
    """指标类型枚举"""

    COUNTER = "counter"  # 计数器
    GAUGE = "gauge"  # 仪表盘
    HISTOGRAM = "histogram"  # 直方图
    TIMER = "timer"  # 计时器


@dataclass
class MetricPoint:
    """指标数据点"""

    timestamp: float
    value: Union[int, float]
    labels: Dict[str, str] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "timestamp": self.timestamp,
            "value": self.value,
            "labels": self.labels,
            "datetime": datetime.fromtimestamp(self.timestamp).isoformat(),
        }


@dataclass
class Metric:
    """指标定义"""

    name: str
    metric_type: MetricType
    description: str = ""
    unit: str = ""
    labels: Dict[str, str] = field(default_factory=dict)
    data_points: deque = field(default_factory=lambda: deque(maxlen=1000))

    def add_point(
        self, value: Union[int, float], labels: Optional[Dict[str, str]] = None
    ):
        """添加数据点"""
        point = MetricPoint(
            timestamp=time.time(), value=value, labels={**self.labels, **(labels or {})}
        )
        self.data_points.append(point)

    def get_latest_value(self) -> Optional[Union[int, float]]:
        """获取最新值"""
        if self.data_points:
            return self.data_points[-1].value
        return None

    def get_average(self, duration_seconds: Optional[int] = None) -> Optional[float]:
        """获取平均值"""
        if not self.data_points:
            return None

        if duration_seconds is None:
            values = [point.value for point in self.data_points]
        else:
            cutoff_time = time.time() - duration_seconds
            values = [
                point.value
                for point in self.data_points
                if point.timestamp >= cutoff_time
            ]

        return sum(values) / len(values) if values else None


class MetricsCollector:
    """
    指标收集器

    负责收集、存储和管理各种系统指标。
    支持实时监控和历史数据分析。
    """

    def __init__(
        self,
        max_data_points: int = 1000,
        retention_hours: int = 24,
        collection_interval: float = 1.0,
    ):
        """
        初始化指标收集器

        Args:
            max_data_points: 每个指标的最大数据点数
            retention_hours: 数据保留时间（小时）
            collection_interval: 收集间隔（秒）
        """
        self.max_data_points = max_data_points
        self.retention_hours = retention_hours
        self.collection_interval = collection_interval

        # 指标存储
        self._metrics: Dict[str, Metric] = {}
        self._counters: Dict[str, float] = defaultdict(float)
        self._gauges: Dict[str, float] = {}
        self._timers: Dict[str, List[float]] = defaultdict(list)

        # 系统指标
        self._system_metrics_enabled = True
        self._collection_task: Optional[asyncio.Task] = None

        self._logger = logging_system.get_logger("metrics_collector")

        # 注册默认指标
        self._register_default_metrics()

        self._logger.info("指标收集器已初始化")

    def _register_default_metrics(self):
        """注册默认系统指标"""
        self.register_metric(
            "agent_requests_total", MetricType.COUNTER, "Agent请求总数"
        )
        self.register_metric(
            "agent_request_duration", MetricType.HISTOGRAM, "Agent请求耗时", "seconds"
        )
        self.register_metric("model_calls_total", MetricType.COUNTER, "模型调用总数")
        self.register_metric(
            "model_call_duration", MetricType.HISTOGRAM, "模型调用耗时", "seconds"
        )
        self.register_metric(
            "tool_executions_total", MetricType.COUNTER, "工具执行总数"
        )
        self.register_metric(
            "tool_execution_duration", MetricType.HISTOGRAM, "工具执行耗时", "seconds"
        )
        self.register_metric(
            "memory_operations_total", MetricType.COUNTER, "记忆操作总数"
        )
        self.register_metric("active_agents", MetricType.GAUGE, "活跃Agent数量")
        self.register_metric("error_count", MetricType.COUNTER, "错误计数")

    def register_metric(
        self,
        name: str,
        metric_type: MetricType,
        description: str = "",
        unit: str = "",
        labels: Optional[Dict[str, str]] = None,
    ) -> None:
        """
        注册指标

        Args:
            name: 指标名称
            metric_type: 指标类型
            description: 指标描述
            unit: 单位
            labels: 标签
        """
        if name in self._metrics:
            self._logger.warning(f"指标 {name} 已存在，将被覆盖")

        metric = Metric(
            name=name,
            metric_type=metric_type,
            description=description,
            unit=unit,
            labels=labels or {},
            data_points=deque(maxlen=self.max_data_points),
        )

        self._metrics[name] = metric
        self._logger.debug(f"注册指标: {name} ({metric_type.value})")

    def increment_counter(
        self,
        name: str,
        value: float = 1.0,
        labels: Optional[Dict[str, str]] = None,
    ) -> None:
        """
        增加计数器

        Args:
            name: 指标名称
            value: 增加值
            labels: 标签
        """
        self._counters[name] += value

        if name in self._metrics:
            self._metrics[name].add_point(self._counters[name], labels)

        self._logger.debug(f"计数器 {name} 增加 {value}")

    def set_gauge(
        self,
        name: str,
        value: float,
        labels: Optional[Dict[str, str]] = None,
    ) -> None:
        """
        设置仪表盘值

        Args:
            name: 指标名称
            value: 值
            labels: 标签
        """
        self._gauges[name] = value

        if name in self._metrics:
            self._metrics[name].add_point(value, labels)

        self._logger.debug(f"仪表盘 {name} 设置为 {value}")

    def record_timer(
        self,
        name: str,
        duration: float,
        labels: Optional[Dict[str, str]] = None,
    ) -> None:
        """
        记录计时器

        Args:
            name: 指标名称
            duration: 持续时间（秒）
            labels: 标签
        """
        self._timers[name].append(duration)

        # 保持最近的1000个记录
        if len(self._timers[name]) > 1000:
            self._timers[name] = self._timers[name][-1000:]

        if name in self._metrics:
            self._metrics[name].add_point(duration, labels)

        self._logger.debug(f"计时器 {name} 记录 {duration:.3f}s")

    def get_metric_value(self, name: str) -> Optional[Union[int, float]]:
        """
        获取指标当前值

        Args:
            name: 指标名称

        Returns:
            Optional[Union[int, float]]: 指标值
        """
        if name in self._metrics:
            return self._metrics[name].get_latest_value()

        # 从缓存获取
        if name in self._counters:
            return self._counters[name]
        elif name in self._gauges:
            return self._gauges[name]
        elif name in self._timers and self._timers[name]:
            return self._timers[name][-1]

        return None

    def get_metric_stats(self, name: str) -> Optional[Dict[str, Any]]:
        """
        获取指标统计信息

        Args:
            name: 指标名称

        Returns:
            Optional[Dict[str, Any]]: 统计信息
        """
        if name not in self._metrics:
            return None

        metric = self._metrics[name]

        if not metric.data_points:
            return {
                "name": name,
                "type": metric.metric_type.value,
                "description": metric.description,
                "unit": metric.unit,
                "count": 0,
            }

        values = [point.value for point in metric.data_points]

        stats = {
            "name": name,
            "type": metric.metric_type.value,
            "description": metric.description,
            "unit": metric.unit,
            "count": len(values),
            "latest": values[-1],
            "min": min(values),
            "max": max(values),
            "avg": sum(values) / len(values),
        }

        # 计算百分位数
        if len(values) >= 2:
            sorted_values = sorted(values)
            stats["p50"] = sorted_values[len(sorted_values) // 2]
            stats["p95"] = sorted_values[int(len(sorted_values) * 0.95)]
            stats["p99"] = sorted_values[int(len(sorted_values) * 0.99)]

        return stats

    def get_all_metrics(self) -> Dict[str, Any]:
        """
        获取所有指标

        Returns:
            Dict[str, Any]: 所有指标数据
        """
        result = {}

        for name in self._metrics:
            stats = self.get_metric_stats(name)
            if stats:
                result[name] = stats

        return result

    def export_prometheus_format(self) -> str:
        """
        导出Prometheus格式的指标

        Returns:
            str: Prometheus格式的指标文本
        """
        lines = []

        for name, metric in self._metrics.items():
            # 添加HELP和TYPE注释
            if metric.description:
                lines.append(f"# HELP {name} {metric.description}")
            lines.append(f"# TYPE {name} {metric.metric_type.value}")

            # 添加数据点
            latest_value = metric.get_latest_value()
            if latest_value is not None:
                labels_str = ""
                if metric.labels:
                    label_pairs = [f'{k}="{v}"' for k, v in metric.labels.items()]
                    labels_str = "{" + ",".join(label_pairs) + "}"

                lines.append(f"{name}{labels_str} {latest_value}")

        return "\n".join(lines)

    def start_collection(self) -> None:
        """启动指标收集"""
        if self._collection_task is None or self._collection_task.done():
            self._collection_task = asyncio.create_task(self._collection_loop())
            self._logger.info("指标收集已启动")

    def stop_collection(self) -> None:
        """停止指标收集"""
        if self._collection_task and not self._collection_task.done():
            self._collection_task.cancel()
            self._logger.info("指标收集已停止")

    async def _collection_loop(self) -> None:
        """指标收集循环"""
        while True:
            try:
                await asyncio.sleep(self.collection_interval)

                # 收集系统指标
                if self._system_metrics_enabled:
                    await self._collect_system_metrics()

                # 清理过期数据
                await self._cleanup_expired_data()

            except asyncio.CancelledError:
                break
            except Exception as e:
                self._logger.error(f"指标收集出错: {str(e)}")

    async def _collect_system_metrics(self) -> None:
        """收集系统指标"""
        try:
            import psutil

            # CPU使用率
            cpu_percent = psutil.cpu_percent()
            self.set_gauge("system_cpu_percent", cpu_percent)

            # 内存使用率
            memory = psutil.virtual_memory()
            self.set_gauge("system_memory_percent", memory.percent)
            self.set_gauge("system_memory_used_bytes", memory.used)

            # 磁盘使用率
            disk = psutil.disk_usage("/")
            self.set_gauge("system_disk_percent", disk.percent)
            self.set_gauge("system_disk_used_bytes", disk.used)

        except ImportError:
            # psutil未安装，跳过系统指标收集
            pass
        except Exception as e:
            self._logger.warning(f"收集系统指标失败: {str(e)}")

    async def _cleanup_expired_data(self) -> None:
        """清理过期数据"""
        cutoff_time = time.time() - (self.retention_hours * 3600)

        for metric in self._metrics.values():
            # 移除过期的数据点
            while metric.data_points and metric.data_points[0].timestamp < cutoff_time:
                metric.data_points.popleft()

    def get_collection_stats(self) -> Dict[str, Any]:
        """
        获取收集器统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        total_data_points = sum(
            len(metric.data_points) for metric in self._metrics.values()
        )

        return {
            "total_metrics": len(self._metrics),
            "total_data_points": total_data_points,
            "max_data_points": self.max_data_points,
            "retention_hours": self.retention_hours,
            "collection_interval": self.collection_interval,
            "collection_running": self._collection_task is not None
            and not self._collection_task.done(),
            "system_metrics_enabled": self._system_metrics_enabled,
        }
