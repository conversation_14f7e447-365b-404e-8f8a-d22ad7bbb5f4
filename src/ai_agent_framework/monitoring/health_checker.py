"""
AI Agent Framework 健康检查器

提供系统健康状态检查功能，包括组件状态监控、依赖检查等。
支持自定义健康检查规则和告警机制。
"""

import asyncio
import time
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Union

from ai_agent_framework.utils.logging_system import logging_system


class HealthStatus(Enum):
    """健康状态枚举"""

    HEALTHY = "healthy"  # 健康
    DEGRADED = "degraded"  # 降级
    UNHEALTHY = "unhealthy"  # 不健康
    UNKNOWN = "unknown"  # 未知


@dataclass
class HealthCheckResult:
    """健康检查结果"""

    name: str
    status: HealthStatus
    message: str = ""
    details: Dict[str, Any] = None
    timestamp: float = None
    duration: float = 0.0

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()
        if self.details is None:
            self.details = {}

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "name": self.name,
            "status": self.status.value,
            "message": self.message,
            "details": self.details,
            "timestamp": self.timestamp,
            "datetime": datetime.fromtimestamp(self.timestamp).isoformat(),
            "duration": self.duration,
        }


class HealthCheck:
    """健康检查定义"""

    def __init__(
        self,
        name: str,
        check_func: Callable[[], Union[bool, HealthCheckResult]],
        description: str = "",
        timeout: float = 5.0,
        interval: float = 30.0,
        critical: bool = False,
    ):
        """
        初始化健康检查

        Args:
            name: 检查名称
            check_func: 检查函数
            description: 描述
            timeout: 超时时间（秒）
            interval: 检查间隔（秒）
            critical: 是否为关键检查
        """
        self.name = name
        self.check_func = check_func
        self.description = description
        self.timeout = timeout
        self.interval = interval
        self.critical = critical

        self.last_result: Optional[HealthCheckResult] = None
        self.last_check_time: float = 0
        self.consecutive_failures: int = 0

    async def execute(self) -> HealthCheckResult:
        """执行健康检查"""
        start_time = time.time()

        try:
            # 执行检查函数（带超时）
            if asyncio.iscoroutinefunction(self.check_func):
                result = await asyncio.wait_for(self.check_func(), timeout=self.timeout)
            else:
                result = await asyncio.wait_for(
                    asyncio.get_event_loop().run_in_executor(None, self.check_func),
                    timeout=self.timeout,
                )

            duration = time.time() - start_time

            # 处理检查结果
            if isinstance(result, HealthCheckResult):
                result.duration = duration
                check_result = result
            elif isinstance(result, bool):
                status = HealthStatus.HEALTHY if result else HealthStatus.UNHEALTHY
                message = "检查通过" if result else "检查失败"
                check_result = HealthCheckResult(
                    name=self.name, status=status, message=message, duration=duration
                )
            else:
                check_result = HealthCheckResult(
                    name=self.name,
                    status=HealthStatus.UNKNOWN,
                    message=f"未知检查结果类型: {type(result)}",
                    duration=duration,
                )

            # 更新连续失败计数
            if check_result.status == HealthStatus.HEALTHY:
                self.consecutive_failures = 0
            else:
                self.consecutive_failures += 1

            self.last_result = check_result
            self.last_check_time = time.time()

            return check_result

        except asyncio.TimeoutError:
            duration = time.time() - start_time
            self.consecutive_failures += 1

            check_result = HealthCheckResult(
                name=self.name,
                status=HealthStatus.UNHEALTHY,
                message=f"检查超时 ({self.timeout}s)",
                duration=duration,
            )

            self.last_result = check_result
            self.last_check_time = time.time()

            return check_result

        except Exception as e:
            duration = time.time() - start_time
            self.consecutive_failures += 1

            check_result = HealthCheckResult(
                name=self.name,
                status=HealthStatus.UNHEALTHY,
                message=f"检查异常: {str(e)}",
                details={"error": str(e), "error_type": type(e).__name__},
                duration=duration,
            )

            self.last_result = check_result
            self.last_check_time = time.time()

            return check_result


class HealthChecker:
    """
    健康检查器

    管理和执行各种健康检查，提供系统整体健康状态评估。
    """

    def __init__(
        self,
        check_interval: float = 30.0,
        failure_threshold: int = 3,
        recovery_threshold: int = 2,
    ):
        """
        初始化健康检查器

        Args:
            check_interval: 默认检查间隔（秒）
            failure_threshold: 失败阈值
            recovery_threshold: 恢复阈值
        """
        self.check_interval = check_interval
        self.failure_threshold = failure_threshold
        self.recovery_threshold = recovery_threshold

        # 健康检查注册表
        self._health_checks: Dict[str, HealthCheck] = {}

        # 检查任务
        self._check_tasks: Dict[str, asyncio.Task] = {}
        self._global_check_task: Optional[asyncio.Task] = None

        # 状态缓存
        self._overall_status: HealthStatus = HealthStatus.UNKNOWN
        self._last_overall_check: float = 0

        self._logger = logging_system.get_logger("health_checker")

        # 注册默认健康检查
        self._register_default_checks()

        self._logger.info("健康检查器已初始化")

    def _register_default_checks(self):
        """注册默认健康检查"""

        # 内存使用检查
        def check_memory():
            try:
                import psutil

                memory = psutil.virtual_memory()
                if memory.percent > 90:
                    return HealthCheckResult(
                        name="memory_usage",
                        status=HealthStatus.UNHEALTHY,
                        message=f"内存使用率过高: {memory.percent:.1f}%",
                        details={"memory_percent": memory.percent},
                    )
                elif memory.percent > 80:
                    return HealthCheckResult(
                        name="memory_usage",
                        status=HealthStatus.DEGRADED,
                        message=f"内存使用率较高: {memory.percent:.1f}%",
                        details={"memory_percent": memory.percent},
                    )
                else:
                    return HealthCheckResult(
                        name="memory_usage",
                        status=HealthStatus.HEALTHY,
                        message=f"内存使用正常: {memory.percent:.1f}%",
                        details={"memory_percent": memory.percent},
                    )
            except ImportError:
                return HealthCheckResult(
                    name="memory_usage",
                    status=HealthStatus.UNKNOWN,
                    message="psutil未安装，无法检查内存使用",
                )

        self.register_check("memory_usage", check_memory, "内存使用率检查")

        # 磁盘空间检查
        def check_disk_space():
            try:
                import psutil

                disk = psutil.disk_usage("/")
                if disk.percent > 95:
                    return HealthCheckResult(
                        name="disk_space",
                        status=HealthStatus.UNHEALTHY,
                        message=f"磁盘空间不足: {disk.percent:.1f}%",
                        details={"disk_percent": disk.percent},
                    )
                elif disk.percent > 85:
                    return HealthCheckResult(
                        name="disk_space",
                        status=HealthStatus.DEGRADED,
                        message=f"磁盘空间较少: {disk.percent:.1f}%",
                        details={"disk_percent": disk.percent},
                    )
                else:
                    return HealthCheckResult(
                        name="disk_space",
                        status=HealthStatus.HEALTHY,
                        message=f"磁盘空间正常: {disk.percent:.1f}%",
                        details={"disk_percent": disk.percent},
                    )
            except ImportError:
                return HealthCheckResult(
                    name="disk_space",
                    status=HealthStatus.UNKNOWN,
                    message="psutil未安装，无法检查磁盘空间",
                )

        self.register_check("disk_space", check_disk_space, "磁盘空间检查")

    def register_check(
        self,
        name: str,
        check_func: Callable[[], Union[bool, HealthCheckResult]],
        description: str = "",
        timeout: float = 5.0,
        interval: Optional[float] = None,
        critical: bool = False,
    ) -> None:
        """
        注册健康检查

        Args:
            name: 检查名称
            check_func: 检查函数
            description: 描述
            timeout: 超时时间
            interval: 检查间隔
            critical: 是否为关键检查
        """
        if name in self._health_checks:
            self._logger.warning(f"健康检查 {name} 已存在，将被覆盖")

        health_check = HealthCheck(
            name=name,
            check_func=check_func,
            description=description,
            timeout=timeout,
            interval=interval or self.check_interval,
            critical=critical,
        )

        self._health_checks[name] = health_check
        self._logger.debug(f"注册健康检查: {name}")

    def unregister_check(self, name: str) -> bool:
        """
        注销健康检查

        Args:
            name: 检查名称

        Returns:
            bool: 是否成功注销
        """
        if name in self._health_checks:
            del self._health_checks[name]

            # 停止检查任务
            if name in self._check_tasks:
                self._check_tasks[name].cancel()
                del self._check_tasks[name]

            self._logger.debug(f"注销健康检查: {name}")
            return True

        return False

    async def check_health(self, name: str) -> Optional[HealthCheckResult]:
        """
        执行单个健康检查

        Args:
            name: 检查名称

        Returns:
            Optional[HealthCheckResult]: 检查结果
        """
        if name not in self._health_checks:
            return None

        health_check = self._health_checks[name]
        result = await health_check.execute()

        self._logger.debug(f"健康检查 {name}: {result.status.value}")
        return result

    async def check_all_health(self) -> Dict[str, HealthCheckResult]:
        """
        执行所有健康检查

        Returns:
            Dict[str, HealthCheckResult]: 所有检查结果
        """
        results = {}

        # 并发执行所有检查
        tasks = []
        for name in self._health_checks:
            task = asyncio.create_task(self.check_health(name))
            tasks.append((name, task))

        # 等待所有检查完成
        for name, task in tasks:
            try:
                result = await task
                if result:
                    results[name] = result
            except Exception as e:
                self._logger.error(f"健康检查 {name} 执行失败: {str(e)}")
                results[name] = HealthCheckResult(
                    name=name,
                    status=HealthStatus.UNHEALTHY,
                    message=f"检查执行失败: {str(e)}",
                )

        return results

    async def get_overall_health(self) -> Dict[str, Any]:
        """
        获取整体健康状态

        Returns:
            Dict[str, Any]: 整体健康状态
        """
        # 执行所有检查
        check_results = await self.check_all_health()

        # 计算整体状态
        overall_status = HealthStatus.HEALTHY
        healthy_count = 0
        degraded_count = 0
        unhealthy_count = 0
        unknown_count = 0
        critical_unhealthy = False

        for result in check_results.values():
            if result.status == HealthStatus.HEALTHY:
                healthy_count += 1
            elif result.status == HealthStatus.DEGRADED:
                degraded_count += 1
                if overall_status == HealthStatus.HEALTHY:
                    overall_status = HealthStatus.DEGRADED
            elif result.status == HealthStatus.UNHEALTHY:
                unhealthy_count += 1
                overall_status = HealthStatus.UNHEALTHY

                # 检查是否为关键检查
                check_name = result.name
                if (
                    check_name in self._health_checks
                    and self._health_checks[check_name].critical
                ):
                    critical_unhealthy = True
            else:
                unknown_count += 1

        # 如果有关键检查失败，整体状态为不健康
        if critical_unhealthy:
            overall_status = HealthStatus.UNHEALTHY

        self._overall_status = overall_status
        self._last_overall_check = time.time()

        return {
            "status": overall_status.value,
            "timestamp": self._last_overall_check,
            "datetime": datetime.fromtimestamp(self._last_overall_check).isoformat(),
            "summary": {
                "total_checks": len(check_results),
                "healthy": healthy_count,
                "degraded": degraded_count,
                "unhealthy": unhealthy_count,
                "unknown": unknown_count,
            },
            "checks": {
                name: result.to_dict() for name, result in check_results.items()
            },
            "critical_unhealthy": critical_unhealthy,
        }

    def start_monitoring(self) -> None:
        """启动健康监控"""
        if self._global_check_task is None or self._global_check_task.done():
            self._global_check_task = asyncio.create_task(self._monitoring_loop())
            self._logger.info("健康监控已启动")

    def stop_monitoring(self) -> None:
        """停止健康监控"""
        if self._global_check_task and not self._global_check_task.done():
            self._global_check_task.cancel()

        # 停止所有检查任务
        for task in self._check_tasks.values():
            if not task.done():
                task.cancel()

        self._check_tasks.clear()
        self._logger.info("健康监控已停止")

    async def _monitoring_loop(self) -> None:
        """监控循环"""
        while True:
            try:
                await asyncio.sleep(self.check_interval)

                # 执行健康检查
                await self.get_overall_health()

            except asyncio.CancelledError:
                break
            except Exception as e:
                self._logger.error(f"健康监控出错: {str(e)}")

    def get_monitoring_stats(self) -> Dict[str, Any]:
        """
        获取监控统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "total_checks": len(self._health_checks),
            "check_interval": self.check_interval,
            "failure_threshold": self.failure_threshold,
            "recovery_threshold": self.recovery_threshold,
            "monitoring_running": self._global_check_task is not None
            and not self._global_check_task.done(),
            "overall_status": self._overall_status.value,
            "last_overall_check": self._last_overall_check,
        }
