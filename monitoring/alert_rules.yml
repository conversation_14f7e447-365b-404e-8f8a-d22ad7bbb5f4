# Prometheus 告警规则

groups:
  - name: ai-agent-alerts
    rules:
      # 服务可用性告警
      - alert: AIAgentDown
        expr: up{job="ai-agent"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "AI Agent服务不可用"
          description: "AI Agent服务已经停止运行超过1分钟"
          
      # 高错误率告警
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "AI Agent高错误率"
          description: "AI Agent在过去5分钟内的错误率超过10%"
          
      # 响应时间告警
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "AI Agent响应时间过长"
          description: "AI Agent的95%响应时间超过2秒"
          
      # 内存使用告警
      - alert: HighMemoryUsage
        expr: (process_resident_memory_bytes / 1024 / 1024) > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "AI Agent内存使用过高"
          description: "AI Agent内存使用超过1GB"
          
      # CPU使用告警
      - alert: HighCPUUsage
        expr: rate(process_cpu_seconds_total[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "AI Agent CPU使用过高"
          description: "AI Agent CPU使用率超过80%"
          
  - name: infrastructure-alerts
    rules:
      # Redis连接告警
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis服务不可用"
          description: "Redis服务已经停止运行"
          
      # PostgreSQL连接告警
      - alert: PostgreSQLDown
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "PostgreSQL服务不可用"
          description: "PostgreSQL服务已经停止运行"
          
      # 磁盘空间告警
      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "磁盘空间不足"
          description: "磁盘可用空间少于10%"
