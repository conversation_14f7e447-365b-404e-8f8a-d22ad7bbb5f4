# AI Agent Framework 环境变量配置模板
# 复制此文件为 .env 并填入你的实际配置

# =============================================================================
# AI 模型 API 密钥配置
# =============================================================================

# OpenAI API 配置
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_ORGANIZATION=your-organization-id

# Anthropic Claude API 配置
ANTHROPIC_API_KEY=your-anthropic-api-key-here
ANTHROPIC_API_BASE=https://api.anthropic.com

# 阿里云通义千问 API 配置
QWEN_API_KEY=your-qwen-api-key-here
QWEN_API_BASE=https://dashscope.aliyuncs.com/api/v1

# =============================================================================
# 框架基础配置
# =============================================================================

# 运行环境 (development, production, testing)
AI_AGENT_ENVIRONMENT=development

# 调试模式
AI_AGENT_DEBUG=false

# 默认模型
AI_AGENT_DEFAULT_MODEL=gpt-4

# 最大迭代次数
AI_AGENT_MAX_ITERATIONS=10

# 最大执行时间（秒）
AI_AGENT_MAX_EXECUTION_TIME=300

# =============================================================================
# 工具配置
# =============================================================================

# 工具执行超时时间（秒）
AI_AGENT_TOOL_TIMEOUT=30

# 最大并发工具数
AI_AGENT_MAX_CONCURRENT_TOOLS=5

# 天气API密钥（如果使用真实天气API）
WEATHER_API_KEY=your-weather-api-key-here

# 搜索API密钥（如果使用真实搜索API）
SEARCH_API_KEY=your-search-api-key-here

# =============================================================================
# 记忆系统配置
# =============================================================================

# 记忆数据库路径
AI_AGENT_MEMORY_STORAGE_PATH=memory.db

# 短期记忆TTL（秒）
AI_AGENT_SHORT_TERM_TTL=3600

# 工作记忆TTL（秒）
AI_AGENT_WORKING_TTL=86400

# 相似度搜索阈值
AI_AGENT_SIMILARITY_THRESHOLD=0.7

# 自动清理间隔（秒）
AI_AGENT_CLEANUP_INTERVAL=3600

# =============================================================================
# 日志配置
# =============================================================================

# 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
AI_AGENT_LOG_LEVEL=INFO

# 日志文件路径
AI_AGENT_LOG_FILE=logs/agent.log

# 最大日志文件大小（字节）
AI_AGENT_LOG_MAX_SIZE=10485760

# 日志备份文件数量
AI_AGENT_LOG_BACKUP_COUNT=5

# 是否使用结构化日志
AI_AGENT_LOG_STRUCTURED=true

# 是否使用JSON格式日志
AI_AGENT_LOG_JSON_FORMAT=false

# =============================================================================
# 监控配置
# =============================================================================

# 是否启用监控
AI_AGENT_MONITORING_ENABLED=true

# 指标端口
AI_AGENT_METRICS_PORT=8000

# 健康检查端口
AI_AGENT_HEALTH_CHECK_PORT=8001

# Prometheus指标前缀
AI_AGENT_PROMETHEUS_PREFIX=ai_agent

# =============================================================================
# 数据库配置（如果使用外部数据库）
# =============================================================================

# 数据库URL（可选，默认使用SQLite）
# DATABASE_URL=postgresql://user:password@localhost:5432/ai_agent
# DATABASE_URL=mysql://user:password@localhost:3306/ai_agent

# =============================================================================
# 安全配置
# =============================================================================

# API访问令牌（如果需要）
API_ACCESS_TOKEN=your-api-access-token

# 加密密钥（用于敏感数据加密）
ENCRYPTION_KEY=your-encryption-key-here

# =============================================================================
# 开发和测试配置
# =============================================================================

# 测试模式
TESTING=false

# 测试数据库路径
TEST_DATABASE_PATH=test_memory.db

# 是否启用性能分析
PROFILING_ENABLED=false

# =============================================================================
# 第三方服务配置
# =============================================================================

# Redis配置（如果使用Redis作为缓存）
REDIS_URL=redis://localhost:6379/0

# 消息队列配置（如果使用）
RABBITMQ_URL=amqp://guest:guest@localhost:5672/

# Elasticsearch配置（如果使用）
ELASTICSEARCH_URL=http://localhost:9200
