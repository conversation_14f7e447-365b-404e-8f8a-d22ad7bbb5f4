# AI Agent Framework 生产环境配置

# 应用配置
app:
  name: "AI Agent Framework"
  version: "1.0.0"
  environment: "production"
  debug: false
  
# 服务器配置
server:
  host: "0.0.0.0"
  port: 8000
  workers: 4
  max_connections: 1000
  keepalive_timeout: 65
  
# 数据库配置
database:
  url: "${DATABASE_URL}"
  pool_size: 20
  max_overflow: 30
  pool_timeout: 30
  pool_recycle: 3600
  echo: false
  
# Redis配置
redis:
  url: "${REDIS_URL}"
  max_connections: 100
  retry_on_timeout: true
  socket_timeout: 5
  socket_connect_timeout: 5
  
# AI模型配置
models:
  openai:
    api_key: "${OPENAI_API_KEY}"
    base_url: "https://api.openai.com/v1"
    default_model: "gpt-4"
    timeout: 60
    max_retries: 3
    
  anthropic:
    api_key: "${ANTHROPIC_API_KEY}"
    base_url: "https://api.anthropic.com"
    default_model: "claude-3-sonnet-20240229"
    timeout: 60
    max_retries: 3
    
  google:
    api_key: "${GOOGLE_API_KEY}"
    default_model: "gemini-pro"
    timeout: 60
    max_retries: 3

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "/app/logs/agent.log"
  max_size: "100MB"
  backup_count: 10
  
# 安全配置
security:
  secret_key: "${SECRET_KEY}"
  jwt_secret: "${JWT_SECRET}"
  jwt_expiration: 3600
  cors_origins:
    - "https://yourdomain.com"
    - "https://api.yourdomain.com"
  rate_limit:
    requests_per_minute: 100
    burst_size: 20
    
# 监控配置
monitoring:
  enabled: true
  metrics_port: 9090
  health_check_interval: 30
  prometheus:
    enabled: true
    path: "/metrics"
  grafana:
    enabled: true
    admin_password: "${GRAFANA_PASSWORD}"
    
# 缓存配置
cache:
  default_ttl: 3600
  max_size: 1000
  cleanup_interval: 300
  
# 工具配置
tools:
  database:
    enabled: true
    max_connections: 10
    timeout: 30
    
  git:
    enabled: true
    default_timeout: 60
    
  filesystem:
    enabled: true
    allowed_paths:
      - "/app/data"
      - "/app/uploads"
    max_file_size: "10MB"
    
# MCP服务配置
mcp:
  enabled: true
  services:
    filesystem:
      enabled: true
      read_only: false
      allowed_paths:
        - "/app/data"
      max_file_size: "10MB"
      
# 内存管理配置
memory:
  short_term_capacity: 1000
  long_term_capacity: 10000
  cleanup_interval: 3600
  embedding_model: "text-embedding-ada-002"
  
# 性能配置
performance:
  max_concurrent_requests: 100
  request_timeout: 300
  connection_pool_size: 50
  
# 备份配置
backup:
  enabled: true
  interval: "daily"
  retention_days: 30
  s3_bucket: "${BACKUP_S3_BUCKET}"
  s3_region: "${BACKUP_S3_REGION}"
