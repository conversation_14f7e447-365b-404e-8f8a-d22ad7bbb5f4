# AI Agent Framework 核心模块说明

## 概述

核心模块（`ai_agent_framework.core`）是整个框架的基础，包含了Agent的核心逻辑、接口定义、消息格式和配置管理等关键组件。

## 模块结构

```
core/
├── __init__.py          # 模块初始化
├── agent.py            # Agent核心类
├── config.py           # 配置管理
├── interfaces.py       # 接口定义
└── messages.py         # 消息格式定义
```

## 主要组件

### 1. Agent类 (`agent.py`)

Agent是框架的核心类，负责协调模型、工具和记忆系统，实现智能决策和任务执行。

#### 主要功能

- **多种推理模式**：支持ReAct、Chain-of-Thought、直接响应三种推理模式
- **对话管理**：维护对话历史，支持上下文理解
- **工具调用**：智能调用外部工具完成复杂任务
- **记忆管理**：存储和检索对话记忆，支持长期记忆
- **状态管理**：完整的Agent状态跟踪和错误处理

#### 推理模式详解

**ReAct模式**
```python
agent = Agent(reasoning_mode="react")
response = await agent.chat("帮我计算今天的天气温度转换")
```
- 思考 → 行动 → 观察 → 思考的循环
- 适合需要多步骤推理和工具调用的复杂任务
- 提供完整的推理过程展示

**Chain-of-Thought模式**
```python
agent = Agent(reasoning_mode="cot")
response = await agent.chat("解释量子计算的基本原理")
```
- 逐步展示思考过程
- 适合需要深度分析和解释的任务
- 提高推理的可解释性

**直接响应模式**
```python
agent = Agent(reasoning_mode="direct")
response = await agent.chat("你好")
```
- 直接生成响应，无复杂推理过程
- 适合简单对话和快速响应场景
- 性能最优

#### 使用示例

```python
import asyncio
from ai_agent_framework import Agent
from ai_agent_framework.models import OpenAIAdapter
from ai_agent_framework.memory import MemoryManager

async def main():
    # 创建模型适配器
    model = OpenAIAdapter(
        api_key="your-api-key",
        model_name="gpt-4"
    )
    
    # 创建记忆管理器
    memory = MemoryManager()
    
    # 创建Agent
    agent = Agent(
        agent_id="my_agent",
        model=model,
        memory=memory,
        reasoning_mode="react"
    )
    
    # 开始对话
    response = await agent.chat("请帮我分析一下今天的股市行情")
    print(response.content)
    
    # 获取Agent状态
    state = agent.get_state()
    print(f"Agent状态: {state}")
    
    # 清理资源
    await memory.close()

asyncio.run(main())
```

### 2. 接口定义 (`interfaces.py`)

定义了框架中所有核心组件的抽象接口，确保组件间的松耦合和可扩展性。

#### ModelInterface - 模型接口

```python
class ModelInterface(ABC):
    """模型接口抽象类"""
    
    @abstractmethod
    async def generate(
        self,
        messages: List[Message],
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> ModelResponse:
        """生成响应"""
        pass
    
    @abstractmethod
    async def generate_stream(
        self,
        messages: List[Message],
        tools: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> AsyncIterator[ModelResponse]:
        """流式生成响应"""
        pass
```

#### ToolInterface - 工具接口

```python
class ToolInterface(ABC):
    """工具接口抽象类"""
    
    @abstractmethod
    async def execute(
        self,
        arguments: Dict[str, Any],
        context: Dict[str, Any] = None,
    ) -> ToolResult:
        """执行工具"""
        pass
    
    @abstractmethod
    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """验证参数"""
        pass
```

#### MemoryInterface - 记忆接口

```python
class MemoryInterface(ABC):
    """记忆接口抽象类"""
    
    @abstractmethod
    async def store(
        self,
        key: str,
        value: Any,
        memory_type: str = "working",
        ttl: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """存储记忆"""
        pass
    
    @abstractmethod
    async def retrieve(
        self,
        key: str,
        memory_type: str = "working",
    ) -> Optional[Any]:
        """检索记忆"""
        pass
```

### 3. 消息格式 (`messages.py`)

定义了框架中使用的统一消息格式，基于Pydantic v2实现数据验证。

#### 核心消息类

**Message - 基础消息类**
```python
@dataclass
class Message:
    role: MessageRole
    content: str
    tool_calls: Optional[List[ToolCall]] = None
    tool_call_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
```

**ModelResponse - 模型响应类**
```python
@dataclass
class ModelResponse:
    content: str
    model_name: str
    tool_calls: Optional[List[ToolCall]] = None
    usage: Optional[ModelUsage] = None
    finish_reason: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
```

**ToolCall - 工具调用类**
```python
@dataclass
class ToolCall:
    id: str
    name: str
    arguments: Dict[str, Any]
```

**ToolResult - 工具结果类**
```python
@dataclass
class ToolResult:
    tool_call_id: str
    success: bool
    result: Optional[Any] = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
```

### 4. 配置管理 (`config.py`)

提供框架的配置管理功能，支持多种配置源和热更新。

#### 配置类

**FrameworkConfig - 框架主配置**
```python
@dataclass
class FrameworkConfig:
    debug: bool = False
    environment: str = "development"
    max_iterations: int = 10
    max_execution_time: float = 300.0
    default_model: str = "gpt-4"
    models: Dict[str, ModelConfig] = field(default_factory=dict)
    tool_timeout: float = 30.0
    max_concurrent_tools: int = 5
```

**ModelConfig - 模型配置**
```python
@dataclass
class ModelConfig:
    name: str
    provider: str
    model_id: str
    api_key: str
    max_tokens: int = 4096
    temperature: float = 0.7
    timeout: float = 60.0
    max_retries: int = 3
    supports_tools: bool = True
    supports_streaming: bool = True
```

#### 使用示例

```python
from ai_agent_framework.core.config import FrameworkConfig, ModelConfig

# 创建模型配置
model_config = ModelConfig(
    name="gpt-4",
    provider="openai",
    model_id="gpt-4",
    api_key="your-api-key",
    max_tokens=4096,
    temperature=0.7
)

# 创建框架配置
config = FrameworkConfig(
    debug=True,
    max_iterations=5,
    models={"gpt-4": model_config}
)

# 从文件加载配置
config = FrameworkConfig.from_file("config.yaml")

# 从环境变量加载配置
config = FrameworkConfig.from_env()
```

## 设计原则

### 1. 接口分离原则
- 每个组件都有明确的接口定义
- 组件间通过接口交互，降低耦合度
- 便于单元测试和模拟

### 2. 单一职责原则
- 每个类都有明确的职责
- Agent负责协调，不直接实现具体功能
- 模型、工具、记忆各司其职

### 3. 开放封闭原则
- 对扩展开放：可以轻松添加新的模型、工具
- 对修改封闭：核心逻辑稳定，不需要频繁修改

### 4. 依赖倒置原则
- 高层模块不依赖低层模块
- 都依赖于抽象接口
- 便于替换和测试

## 扩展指南

### 添加新的推理模式

```python
class Agent:
    async def _custom_reasoning(self, tools: Optional[List[str]] = None) -> ModelResponse:
        """自定义推理模式"""
        # 实现自定义推理逻辑
        pass
    
    async def chat(self, user_input: str, **kwargs) -> ModelResponse:
        # 在现有逻辑中添加新模式
        if self.reasoning_mode == "custom":
            response = await self._custom_reasoning(tools)
```

### 扩展消息格式

```python
@dataclass
class CustomMessage(Message):
    """自定义消息类"""
    custom_field: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        data = super().to_dict()
        data["custom_field"] = self.custom_field
        return data
```

### 添加新的配置项

```python
@dataclass
class CustomConfig:
    """自定义配置"""
    custom_setting: str = "default_value"
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "CustomConfig":
        return cls(**data)
```

## 最佳实践

1. **错误处理**：使用框架提供的异常类型，提供详细的错误信息
2. **日志记录**：使用框架的日志系统，记录关键操作和错误
3. **资源管理**：及时关闭资源，使用异步上下文管理器
4. **配置管理**：使用配置类而不是硬编码值
5. **测试覆盖**：为所有公共方法编写单元测试

## 性能考虑

1. **异步操作**：所有I/O操作都是异步的，避免阻塞
2. **连接复用**：模型适配器使用连接池减少连接开销
3. **缓存机制**：合理使用缓存减少重复计算
4. **批量处理**：工具调用支持批量执行提高效率
5. **内存管理**：及时清理不需要的对象和缓存
