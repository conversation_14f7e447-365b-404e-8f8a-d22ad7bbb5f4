#!/bin/bash

# AI Agent Framework 部署脚本
# 用于自动化部署AI智能体框架

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查Git
    if ! command -v git &> /dev/null; then
        log_error "Git未安装，请先安装Git"
        exit 1
    fi
    
    log_success "系统依赖检查完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p data
    mkdir -p logs
    mkdir -p config
    mkdir -p ssl
    mkdir -p grafana/dashboards
    mkdir -p grafana/datasources
    
    log_success "目录创建完成"
}

# 生成配置文件
generate_configs() {
    log_info "生成配置文件..."
    
    # 生成环境变量文件
    if [ ! -f .env ]; then
        cat > .env << EOF
# AI Agent Framework 环境变量配置

# 基础配置
PYTHONPATH=/app
LOG_LEVEL=INFO
DEBUG=false

# 数据库配置
DATABASE_URL=*****************************************************/ai_agent
REDIS_URL=redis://redis:6379/0

# AI模型配置
OPENAI_API_KEY=your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here
GOOGLE_API_KEY=your-google-api-key-here

# 服务配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8000
WORKERS=4

# 安全配置
SECRET_KEY=your-secret-key-here
JWT_SECRET=your-jwt-secret-here

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
EOF
        log_success "环境变量文件已生成: .env"
        log_warning "请编辑 .env 文件，填入正确的API密钥和配置"
    else
        log_info "环境变量文件已存在，跳过生成"
    fi
    
    # 生成Nginx配置
    if [ ! -f nginx.conf ]; then
        cat > nginx.conf << EOF
events {
    worker_connections 1024;
}

http {
    upstream ai_agent {
        server ai-agent:8000;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        location / {
            proxy_pass http://ai_agent;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }
        
        location /health {
            proxy_pass http://ai_agent/health;
            access_log off;
        }
    }
}
EOF
        log_success "Nginx配置文件已生成: nginx.conf"
    fi
    
    # 生成Prometheus配置
    if [ ! -f prometheus.yml ]; then
        cat > prometheus.yml << EOF
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'ai-agent'
    static_configs:
      - targets: ['ai-agent:8000']
    metrics_path: '/metrics'
    scrape_interval: 5s
EOF
        log_success "Prometheus配置文件已生成: prometheus.yml"
    fi
}

# 构建Docker镜像
build_images() {
    log_info "构建Docker镜像..."
    
    docker-compose build --no-cache
    
    log_success "Docker镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动基础服务
    docker-compose up -d redis postgres
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 10
    
    # 启动主服务
    docker-compose up -d ai-agent
    
    # 等待主服务启动
    log_info "等待主服务启动..."
    sleep 5
    
    # 启动其他服务
    docker-compose up -d nginx prometheus grafana
    
    log_success "所有服务已启动"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 检查容器状态
    docker-compose ps
    
    # 检查健康状态
    log_info "检查API健康状态..."
    if curl -f http://localhost/health > /dev/null 2>&1; then
        log_success "API服务正常运行"
    else
        log_warning "API服务可能未正常启动，请检查日志"
    fi
    
    log_info "服务访问地址："
    echo "  - API文档: http://localhost/docs"
    echo "  - 健康检查: http://localhost/health"
    echo "  - Grafana监控: http://localhost:3000 (admin/admin)"
    echo "  - Prometheus: http://localhost:9090"
}

# 显示日志
show_logs() {
    log_info "显示服务日志..."
    docker-compose logs -f --tail=50
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    docker-compose down
    log_success "服务已停止"
}

# 清理资源
cleanup() {
    log_info "清理资源..."
    docker-compose down -v --remove-orphans
    docker system prune -f
    log_success "资源清理完成"
}

# 主函数
main() {
    case "${1:-deploy}" in
        "deploy")
            log_info "开始部署AI Agent Framework..."
            check_dependencies
            create_directories
            generate_configs
            build_images
            start_services
            check_services
            log_success "部署完成！"
            ;;
        "start")
            start_services
            check_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            stop_services
            start_services
            check_services
            ;;
        "logs")
            show_logs
            ;;
        "status")
            check_services
            ;;
        "cleanup")
            cleanup
            ;;
        "help")
            echo "用法: $0 [命令]"
            echo ""
            echo "命令:"
            echo "  deploy   - 完整部署（默认）"
            echo "  start    - 启动服务"
            echo "  stop     - 停止服务"
            echo "  restart  - 重启服务"
            echo "  logs     - 查看日志"
            echo "  status   - 检查状态"
            echo "  cleanup  - 清理资源"
            echo "  help     - 显示帮助"
            ;;
        *)
            log_error "未知命令: $1"
            echo "使用 '$0 help' 查看可用命令"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
